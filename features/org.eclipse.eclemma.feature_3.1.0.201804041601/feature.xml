<?xml version="1.0" encoding="UTF-8"?>
<feature
      id="org.eclipse.eclemma.feature"
      label="%featureName"
      version="3.1.0.201804041601"
      provider-name="%providerName"
      plugin="org.eclipse.eclemma.ui">

   <description url="http://www.eclemma.org/">
      Java code coverage analysis for the Eclipse IDE based on the JaCoCo code coverage library ( http://www.jacoco.org/jacoco )
   </description>

   <copyright url="http://www.eclemma.org/">
      Copyright (c) 2006, 2017 Mountainminds GmbH &amp; Co. KG and Contributors
   </copyright>

   <license url="%licenseURL">
      %license
   </license>

   <url>
      <update label="EclEmma Updates" url="http://update.eclemma.org/"/>
   </url>

   <requires>
      <import feature="org.eclipse.jdt" version="3.8.0" match="greaterOrEqual"/>
      <import feature="org.eclipse.rcp" version="3.8.0" match="greaterOrEqual"/>
      <import feature="org.eclipse.platform" version="3.8.0" match="greaterOrEqual"/>
   </requires>

   <plugin
         id="org.eclipse.eclemma.core"
         download-size="97"
         install-size="202"
         version="3.1.0.201804041601"
         unpack="false"/>

   <plugin
         id="org.eclipse.eclemma.doc"
         download-size="223"
         install-size="265"
         version="3.1.0.201804041601"
         unpack="false"/>

   <plugin
         id="org.eclipse.eclemma.ui"
         download-size="304"
         install-size="553"
         version="3.1.0.201804041601"
         unpack="false"/>

   <plugin
         id="org.jacoco.agent"
         download-size="252"
         install-size="293"
         version="0.8.1.v20180329-2024"
         unpack="false"/>

   <plugin
         id="org.jacoco.core"
         download-size="182"
         install-size="341"
         version="0.8.1.v20180329-2024"
         unpack="false"/>

   <plugin
         id="org.jacoco.report"
         download-size="144"
         install-size="309"
         version="0.8.1.v20180329-2024"
         unpack="false"/>

   <plugin
         id="org.objectweb.asm"
         download-size="111"
         install-size="228"
         version="6.0.0.v20180116-1719"
         unpack="false"/>

   <plugin
         id="org.objectweb.asm.commons"
         download-size="95"
         install-size="208"
         version="6.0.0.v20180116-1719"
         unpack="false"/>

   <plugin
         id="org.objectweb.asm.tree"
         download-size="67"
         install-size="144"
         version="6.0.0.v20180116-1719"
         unpack="false"/>

</feature>
