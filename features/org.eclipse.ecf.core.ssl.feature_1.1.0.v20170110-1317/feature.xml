<?xml version="1.0" encoding="UTF-8"?>
<feature
      id="org.eclipse.ecf.core.ssl.feature"
      label="ECF Core SSL Feature"
      version="1.1.0.v20170110-1317"
      provider-name="Eclipse.org - ECF">

   <description url="http://www.eclipse.org/ecf">
      This feature provides the ECF core SSL fragment.  On Equinox-based frameworks, this fragment exposes the Equinox TrustManager to ECF FileTransfer and other ECF-based communications.
   </description>

   <copyright>
      Copyright (c) 2009 Composent, Inc. and others. All rights
reserved.
This program and the accompanying materials are made available
under the terms of the Eclipse Public License v1.0 which accompanies
this distribution, and is available at 
http://www.eclipse.org/legal/epl-v10.html
 
Contributors: Composent, Inc. - initial API and implementation
   </copyright>

   <license url="%licenseURL">
      %license
   </license>

   <requires>
      <import plugin="org.eclipse.ecf"/>
      <import plugin="org.eclipse.ecf.identity"/>
   </requires>

   <plugin
         id="org.eclipse.ecf.ssl"
         download-size="14"
         install-size="23"
         version="1.2.0.v20160817-1024"
         fragment="true"
         unpack="false"/>

</feature>
