############################################################################
# Copyright (c) 2008 Composent Inc., IBM Corp. and others.
# All rights reserved. This program and the accompanying materials
# are made available under the terms of the Eclipse Public License v1.0
# which accompanies this distribution, and is available at
# http://www.eclipse.org/legal/epl-v10.html
#
############################################################################
featureName=ECF Apache Httpclient4 FileTransfer Provider
providerName=Eclipse.org

# "licenseURL" property - URL of the "Feature License"
# do not translate value - just change to point to a locale-specific HTML page
licenseURL=license.html

# "license" property - text of the "Feature Update License"
# should be plain text version of license agreement pointed to be "licenseURL"
license=\
Eclipse Foundation Software User Agreement\n\
April 9, 2014\n\
\n\
Usage Of Content\n\
\n\
THE ECLIPSE FOUNDATION MAKES AVAILABLE SOFTWARE, DOCUMENTATION, INFORMATION AND/OR\n\
OTHER MATERIALS FOR OPEN SOURCE PROJECTS (COLLECTIVELY "CONTENT").\n\
USE OF THE CONTENT IS GOVERNED BY THE TERMS AND CONDITIONS OF THIS\n\
AGREEMENT AND/OR THE TERMS AND CONDITIONS OF LICENSE AGREEMENTS OR\n\
NOTICES INDICATED OR REFERENCED BELOW.  BY USING THE CONTENT, YOU\n\
AGREE THAT YOUR USE OF THE CONTENT IS GOVERNED BY THIS AGREEMENT\n\
AND/OR THE TERMS AND CONDITIONS OF ANY APPLICABLE LICENSE AGREEMENTS\n\
OR NOTICES INDICATED OR REFERENCED BELOW.  IF YOU DO NOT AGREE TO THE\n\
TERMS AND CONDITIONS OF THIS AGREEMENT AND THE TERMS AND CONDITIONS\n\
OF ANY APPLICABLE LICENSE AGREEMENTS OR NOTICES INDICATED OR REFERENCED\n\
BELOW, THEN YOU MAY NOT USE THE CONTENT.\n\
\n\
Applicable Licenses\n\
\n\
Unless otherwise indicated, all Content made available by the\n\
Eclipse Foundation is provided to you under the terms and conditions of\n\
the Eclipse Public License Version 1.0 ("EPL"). A copy of the EPL is\n\
provided with this Content and is also available at http://www.eclipse.org/legal/epl-v10.html.\n\
For purposes of the EPL, "Program" will mean the Content.\n\
\n\
Content includes, but is not limited to, source code, object code,\n\
documentation and other files maintained in the Eclipse Foundation source code\n\
repository ("Repository") in software modules ("Modules") and made available\n\
as downloadable archives ("Downloads").\n\
\n\
\t- Content may be structured and packaged into modules to facilitate delivering,\n\
\t  extending, and upgrading the Content. Typical modules may include plug-ins ("Plug-ins"),\n\
\t  plug-in fragments ("Fragments"), and features ("Features").\n\
\t- Each Plug-in or Fragment may be packaged as a sub-directory or JAR (Java(TM) ARchive)\n\
\t  in a directory named "plugins".\n\
\t- A Feature is a bundle of one or more Plug-ins and/or Fragments and associated material.\n\
\t  Each Feature may be packaged as a sub-directory in a directory named "features".\n\
\t  Within a Feature, files named "feature.xml" may contain a list of the names and version\n\
\t  numbers of the Plug-ins and/or Fragments associated with that Feature.\n\
\t- Features may also include other Features ("Included Features"). Within a Feature, files\n\
\t  named "feature.xml" may contain a list of the names and version numbers of Included Features.\n\
\n\
The terms and conditions governing Plug-ins and Fragments should be\n\
contained in files named "about.html" ("Abouts"). The terms and\n\
conditions governing Features and Included Features should be contained\n\
in files named "license.html" ("Feature Licenses"). Abouts and Feature\n\
Licenses may be located in any directory of a Download or Module\n\
including, but not limited to the following locations:\n\
\n\
\t- The top-level (root) directory\n\
\t- Plug-in and Fragment directories\n\
\t- Inside Plug-ins and Fragments packaged as JARs\n\
\t- Sub-directories of the directory named "src" of certain Plug-ins\n\
\t- Feature directories\n\
\n\
Note: if a Feature made available by the Eclipse Foundation is installed using the\n\
Provisioning Technology (as defined below), you must agree to a license ("Feature \n\
Update License") during the installation process. If the Feature contains\n\
Included Features, the Feature Update License should either provide you\n\
with the terms and conditions governing the Included Features or inform\n\
you where you can locate them. Feature Update Licenses may be found in\n\
the "license" property of files named "feature.properties" found within a Feature.\n\
Such Abouts, Feature Licenses, and Feature Update Licenses contain the\n\
terms and conditions (or references to such terms and conditions) that\n\
govern your use of the associated Content in that directory.\n\
\n\
THE ABOUTS, FEATURE LICENSES, AND FEATURE UPDATE LICENSES MAY REFER\n\
TO THE EPL OR OTHER LICENSE AGREEMENTS, NOTICES OR TERMS AND CONDITIONS.\n\
SOME OF THESE OTHER LICENSE AGREEMENTS MAY INCLUDE (BUT ARE NOT LIMITED TO):\n\
\n\
\t- Eclipse Distribution License Version 1.0 (available at http://www.eclipse.org/licenses/edl-v1.0.html)\n\
\t- Common Public License Version 1.0 (available at http://www.eclipse.org/legal/cpl-v10.html)\n\
\t- Apache Software License 1.1 (available at http://www.apache.org/licenses/LICENSE)\n\
\t- Apache Software License 2.0 (available at http://www.apache.org/licenses/LICENSE-2.0)\n\
\t- Mozilla Public License Version 1.1 (available at http://www.mozilla.org/MPL/MPL-1.1.html)\n\
\n\
IT IS YOUR OBLIGATION TO READ AND ACCEPT ALL SUCH TERMS AND CONDITIONS PRIOR\n\
TO USE OF THE CONTENT. If no About, Feature License, or Feature Update License\n\
is provided, please contact the Eclipse Foundation to determine what terms and conditions\n\
govern that particular Content.\n\
\n\
\n\Use of Provisioning Technology\n\
\n\
The Eclipse Foundation makes available provisioning software, examples of which include,\n\
but are not limited to, p2 and the Eclipse Update Manager ("Provisioning Technology") for\n\
the purpose of allowing users to install software, documentation, information and/or\n\
other materials (collectively "Installable Software"). This capability is provided with\n\
the intent of allowing such users to install, extend and update Eclipse-based products.\n\
Information about packaging Installable Software is available at\n\
http://eclipse.org/equinox/p2/repository_packaging.html ("Specification").\n\
\n\
You may use Provisioning Technology to allow other parties to install Installable Software.\n\
You shall be responsible for enabling the applicable license agreements relating to the\n\
Installable Software to be presented to, and accepted by, the users of the Provisioning Technology\n\
in accordance with the Specification. By using Provisioning Technology in such a manner and\n\
making it available in accordance with the Specification, you further acknowledge your\n\
agreement to, and the acquisition of all necessary rights to permit the following:\n\
\n\
\t1. A series of actions may occur ("Provisioning Process") in which a user may execute\n\
\t   the Provisioning Technology on a machine ("Target Machine") with the intent of installing,\n\
\t   extending or updating the functionality of an Eclipse-based product.\n\
\t2. During the Provisioning Process, the Provisioning Technology may cause third party\n\
\t   Installable Software or a portion thereof to be accessed and copied to the Target Machine.\n\
\t3. Pursuant to the Specification, you will provide to the user the terms and conditions that\n\
\t   govern the use of the Installable Software ("Installable Software Agreement") and such\n\
\t   Installable Software Agreement shall be accessed from the Target Machine in accordance\n\
\t   with the Specification. Such Installable Software Agreement must inform the user of the\n\
\t   terms and conditions that govern the Installable Software and must solicit acceptance by\n\
\t   the end user in the manner prescribed in such Installable Software Agreement. Upon such\n\
\t   indication of agreement by the user, the provisioning Technology will complete installation\n\
\t   of the Installable Software.\n\
\n\
Cryptography\n\
\n\
Content may contain encryption software. The country in which you are\n\
currently may have restrictions on the import, possession, and use,\n\
and/or re-export to another country, of encryption software. BEFORE\n\
using any encryption software, please check the country's laws,\n\
regulations and policies concerning the import, possession, or use, and\n\
re-export of encryption software, to see if this is permitted.\n\
\n\
Java and all Java-based trademarks are trademarks of Oracle Corporation in the United States, other countries, or both.\n
########### end of license property ##########################################

# "licenseURL" property - URL of the "Feature License"
# do not translate value - just change to point to a locale-specific HTML page
licenseURL=license.html

# "license" property - text of the "Feature Update License"
# should be plain text version of license agreement pointed to be "licenseURL"
license=\
Eclipse Foundation Software User Agreement\n\
April 9, 2014\n\
\n\
Usage Of Content\n\
\n\
THE ECLIPSE FOUNDATION MAKES AVAILABLE SOFTWARE, DOCUMENTATION, INFORMATION AND/OR\n\
OTHER MATERIALS FOR OPEN SOURCE PROJECTS (COLLECTIVELY "CONTENT").\n\
USE OF THE CONTENT IS GOVERNED BY THE TERMS AND CONDITIONS OF THIS\n\
AGREEMENT AND/OR THE TERMS AND CONDITIONS OF LICENSE AGREEMENTS OR\n\
NOTICES INDICATED OR REFERENCED BELOW.  BY USING THE CONTENT, YOU\n\
AGREE THAT YOUR USE OF THE CONTENT IS GOVERNED BY THIS AGREEMENT\n\
AND/OR THE TERMS AND CONDITIONS OF ANY APPLICABLE LICENSE AGREEMENTS\n\
OR NOTICES INDICATED OR REFERENCED BELOW.  IF YOU DO NOT AGREE TO THE\n\
TERMS AND CONDITIONS OF THIS AGREEMENT AND THE TERMS AND CONDITIONS\n\
OF ANY APPLICABLE LICENSE AGREEMENTS OR NOTICES INDICATED OR REFERENCED\n\
BELOW, THEN YOU MAY NOT USE THE CONTENT.\n\
\n\
Applicable Licenses\n\
\n\
Unless otherwise indicated, all Content made available by the\n\
Eclipse Foundation is provided to you under the terms and conditions of\n\
the Eclipse Public License Version 1.0 ("EPL"). A copy of the EPL is\n\
provided with this Content and is also available at http://www.eclipse.org/legal/epl-v10.html.\n\
For purposes of the EPL, "Program" will mean the Content.\n\
\n\
Content includes, but is not limited to, source code, object code,\n\
documentation and other files maintained in the Eclipse Foundation source code\n\
repository ("Repository") in software modules ("Modules") and made available\n\
as downloadable archives ("Downloads").\n\
\n\
\t- Content may be structured and packaged into modules to facilitate delivering,\n\
\t  extending, and upgrading the Content. Typical modules may include plug-ins ("Plug-ins"),\n\
\t  plug-in fragments ("Fragments"), and features ("Features").\n\
\t- Each Plug-in or Fragment may be packaged as a sub-directory or JAR (Java(TM) ARchive)\n\
\t  in a directory named "plugins".\n\
\t- A Feature is a bundle of one or more Plug-ins and/or Fragments and associated material.\n\
\t  Each Feature may be packaged as a sub-directory in a directory named "features".\n\
\t  Within a Feature, files named "feature.xml" may contain a list of the names and version\n\
\t  numbers of the Plug-ins and/or Fragments associated with that Feature.\n\
\t- Features may also include other Features ("Included Features"). Within a Feature, files\n\
\t  named "feature.xml" may contain a list of the names and version numbers of Included Features.\n\
\n\
The terms and conditions governing Plug-ins and Fragments should be\n\
contained in files named "about.html" ("Abouts"). The terms and\n\
conditions governing Features and Included Features should be contained\n\
in files named "license.html" ("Feature Licenses"). Abouts and Feature\n\
Licenses may be located in any directory of a Download or Module\n\
including, but not limited to the following locations:\n\
\n\
\t- The top-level (root) directory\n\
\t- Plug-in and Fragment directories\n\
\t- Inside Plug-ins and Fragments packaged as JARs\n\
\t- Sub-directories of the directory named "src" of certain Plug-ins\n\
\t- Feature directories\n\
\n\
Note: if a Feature made available by the Eclipse Foundation is installed using the\n\
Provisioning Technology (as defined below), you must agree to a license ("Feature \n\
Update License") during the installation process. If the Feature contains\n\
Included Features, the Feature Update License should either provide you\n\
with the terms and conditions governing the Included Features or inform\n\
you where you can locate them. Feature Update Licenses may be found in\n\
the "license" property of files named "feature.properties" found within a Feature.\n\
Such Abouts, Feature Licenses, and Feature Update Licenses contain the\n\
terms and conditions (or references to such terms and conditions) that\n\
govern your use of the associated Content in that directory.\n\
\n\
THE ABOUTS, FEATURE LICENSES, AND FEATURE UPDATE LICENSES MAY REFER\n\
TO THE EPL OR OTHER LICENSE AGREEMENTS, NOTICES OR TERMS AND CONDITIONS.\n\
SOME OF THESE OTHER LICENSE AGREEMENTS MAY INCLUDE (BUT ARE NOT LIMITED TO):\n\
\n\
\t- Eclipse Distribution License Version 1.0 (available at http://www.eclipse.org/licenses/edl-v1.0.html)\n\
\t- Common Public License Version 1.0 (available at http://www.eclipse.org/legal/cpl-v10.html)\n\
\t- Apache Software License 1.1 (available at http://www.apache.org/licenses/LICENSE)\n\
\t- Apache Software License 2.0 (available at http://www.apache.org/licenses/LICENSE-2.0)\n\
\t- Mozilla Public License Version 1.1 (available at http://www.mozilla.org/MPL/MPL-1.1.html)\n\
\n\
IT IS YOUR OBLIGATION TO READ AND ACCEPT ALL SUCH TERMS AND CONDITIONS PRIOR\n\
TO USE OF THE CONTENT. If no About, Feature License, or Feature Update License\n\
is provided, please contact the Eclipse Foundation to determine what terms and conditions\n\
govern that particular Content.\n\
\n\
\n\Use of Provisioning Technology\n\
\n\
The Eclipse Foundation makes available provisioning software, examples of which include,\n\
but are not limited to, p2 and the Eclipse Update Manager ("Provisioning Technology") for\n\
the purpose of allowing users to install software, documentation, information and/or\n\
other materials (collectively "Installable Software"). This capability is provided with\n\
the intent of allowing such users to install, extend and update Eclipse-based products.\n\
Information about packaging Installable Software is available at\n\
http://eclipse.org/equinox/p2/repository_packaging.html ("Specification").\n\
\n\
You may use Provisioning Technology to allow other parties to install Installable Software.\n\
You shall be responsible for enabling the applicable license agreements relating to the\n\
Installable Software to be presented to, and accepted by, the users of the Provisioning Technology\n\
in accordance with the Specification. By using Provisioning Technology in such a manner and\n\
making it available in accordance with the Specification, you further acknowledge your\n\
agreement to, and the acquisition of all necessary rights to permit the following:\n\
\n\
\t1. A series of actions may occur ("Provisioning Process") in which a user may execute\n\
\t   the Provisioning Technology on a machine ("Target Machine") with the intent of installing,\n\
\t   extending or updating the functionality of an Eclipse-based product.\n\
\t2. During the Provisioning Process, the Provisioning Technology may cause third party\n\
\t   Installable Software or a portion thereof to be accessed and copied to the Target Machine.\n\
\t3. Pursuant to the Specification, you will provide to the user the terms and conditions that\n\
\t   govern the use of the Installable Software ("Installable Software Agreement") and such\n\
\t   Installable Software Agreement shall be accessed from the Target Machine in accordance\n\
\t   with the Specification. Such Installable Software Agreement must inform the user of the\n\
\t   terms and conditions that govern the Installable Software and must solicit acceptance by\n\
\t   the end user in the manner prescribed in such Installable Software Agreement. Upon such\n\
\t   indication of agreement by the user, the provisioning Technology will complete installation\n\
\t   of the Installable Software.\n\
\n\
Cryptography\n\
\n\
Content may contain encryption software. The country in which you are\n\
currently may have restrictions on the import, possession, and use,\n\
and/or re-export to another country, of encryption software. BEFORE\n\
using any encryption software, please check the country's laws,\n\
regulations and policies concerning the import, possession, or use, and\n\
re-export of encryption software, to see if this is permitted.\n\
\n\
Java and all Java-based trademarks are trademarks of Oracle Corporation in the United States, other countries, or both.\n
########### end of license property ##########################################
