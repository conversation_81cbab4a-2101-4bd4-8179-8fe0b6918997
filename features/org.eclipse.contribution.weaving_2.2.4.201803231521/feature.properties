###############################################################################
# Copyright (c) 2000, 2005 IBM Corporation and others.
# All rights reserved. This program and the accompanying materials
# are made available under the terms of the Eclipse Public License v1.0
# which accompanies this distribution, and is available at
# http://www.eclipse.org/legal/epl-v10.html
# 
# Contributors:
#     IBM Corporation - initial API and implementation
###############################################################################
# feature.properties
# contains externalized strings for feature.xml
# "%foo" in feature.xml corresponds to the key "foo" in this file
# java.io.Properties file (ISO 8859-1 with "\" escapes)
# This file should be translated.

# "featureName" property - name of the feature
featureName=Eclipse Weaving Service Feature 

# "providerName" property - name of the company that provides the feature
providerName=Eclipse AspectJ Development Tools

# "updateSiteName" property - label for the update site
updateSiteName=AspectJ Update Site on Eclipse.org

# "description" property - description of the feature
description=The Eclipse weaving service provides extra extension\n\
points into the Eclipse platform through the use of AspectJ

descriptionURL=http://eclipse.org/ajdt

# "licenseURL" property - URL of the "Feature License"
# do not translate value - just change to point to a locale-specific HTML page
licenseURL=license.html

updateLabel=Eclipse Weaving Service for Eclipse 3.4 (dev builds)

# "license" property - text of the "Feature Update License"
# should be plain text version of license agreement pointed to be "licenseURL"
license=\
ECLIPSE FOUNDATION SOFTWARE USER AGREEMENT\n\
March 17, 2005\n\
\n\
Usage Of Content\n\
\n\
THE ECLIPSE FOUNDATION MAKES AVAILABLE SOFTWARE, DOCUMENTATION, INFORMATION AND/OR\n\
OTHER MATERIALS FOR OPEN SOURCE PROJECTS (COLLECTIVELY "CONTENT").\n\
USE OF THE CONTENT IS GOVERNED BY THE TERMS AND CONDITIONS OF THIS\n\
AGREEMENT AND/OR THE TERMS AND CONDITIONS OF LICENSE AGREEMENTS OR\n\
NOTICES INDICATED OR REFERENCED BELOW. BY USING THE CONTENT, YOU\n\
AGREE THAT YOUR USE OF THE CONTENT IS GOVERNED BY THIS AGREEMENT\n\
AND/OR THE TERMS AND CONDITIONS OF ANY APPLICABLE LICENSE AGREEMENTS\n\
OR NOTICES INDICATED OR REFERENCED BELOW. IF YOU DO NOT AGREE TO THE\n\
TERMS AND CONDITIONS OF THIS AGREEMENT AND THE TERMS AND CONDITIONS\n\
OF ANY APPLICABLE LICENSE AGREEMENTS OR NOTICES INDICATED OR REFERENCED\n\
BELOW, THEN YOU MAY NOT USE THE CONTENT.\n\
\n\
Applicable Licenses\n\
\n\
Unless otherwise indicated, all Content made available by the Eclipse Foundation\n\
is provided to you under the terms and conditions of the Eclipse Public\n\
License Version 1.0 ("EPL"). A copy of the EPL is provided with this\n\
Content and is also available at http://www.eclipse.org/legal/epl-v10.html.\n\
For purposes of the EPL, "Program" will mean the Content.\n\
\n\
Content includes, but is not limited to, source code, object code,\n\
documentation and other files maintained in the Eclipse.org CVS\n\
repository ("Repository") in CVS modules ("Modules") and made available\n\
as downloadable archives ("Downloads").\n\
\n\
   - Content may be structured and packaged into modules to facilitate delivering,\n\
     extending, and upgrading the Content. Typical modules may include plug-ins ("Plug-ins"),\n\
     plug-in fragments ("Fragments"), and features ("Features").\n\
   - Each Plug-in or Fragment may be packaged as a sub-directory or JAR (Java? ARchive)\n\
     in a directory named "plugins".\n\
   - A Feature is a bundle of one or more Plug-ins and/or Fragments and associated material.\n\
     Each Feature may be packaged as a sub-directory in a directory named "features".\n\
     Within a Feature, files named "feature.xml" may contain a list of the names and version\n\
     numbers of the Plug-ins and/or Fragments associated with that Feature.\n\
   - Features may also include other Features ("Included Features"). Within a Feature, files\n\
     named "feature.xml" may contain a list of the names and version numbers of Included Features.\n\
\n\
Features may also include other Features ("Included Features"). Files named\n\
"feature.xml" may contain a list of the names and version numbers of\n\
Included Features.\n\
\n\
The terms and conditions governing Plug-ins and Fragments should be\n\
contained in files named "about.html" ("Abouts"). The terms and\n\
conditions governing Features and Included Features should be contained\n\
in files named "license.html" ("Feature Licenses"). Abouts and Feature\n\
Licenses may be located in any directory of a Download or Module\n\
including, but not limited to the following locations:\n\
\n\
   - The top-level (root) directory\n\
   - Plug-in and Fragment directories\n\
   - Inside Plug-ins and Fragments packaged as JARs\n\
   - Sub-directories of the directory named "src" of certain Plug-ins\n\
   - Feature directories\n\
\n\
Note: if a Feature made available by the Eclipse Foundation is installed using the\n\
Eclipse Update Manager, you must agree to a license ("Feature Update\n\
License") during the installation process. If the Feature contains\n\
Included Features, the Feature Update License should either provide you\n\
with the terms and conditions governing the Included Features or inform\n\
you where you can locate them. Feature Update Licenses may be found in\n\
the "license" property of files named "feature.properties". Such Abouts,\n\
Feature Licenses and Feature Update Licenses contain the terms and\n\
conditions (or references to such terms and conditions) that govern your\n\
use of the associated Content in that directory.\n\
\n\
THE ABOUTS, FEATURE LICENSES AND FEATURE UPDATE LICENSES MAY REFER\n\
TO THE EPL OR OTHER LICENSE AGREEMENTS, NOTICES OR TERMS AND CONDITIONS.\n\
SOME OF THESE OTHER LICENSE AGREEMENTS MAY INCLUDE (BUT ARE NOT LIMITED TO):\n\
\n\
    - Common Public License Version 1.0 (available at http://www.eclipse.org/legal/cpl-v10.html)\n\
    - Apache Software License 1.1 (available at http://www.apache.org/licenses/LICENSE)\n\
    - Apache Software License 2.0 (available at http://www.apache.org/licenses/LICENSE-2.0)\n\
    - IBM Public License 1.0 (available at http://oss.software.ibm.com/developerworks/opensource/license10.html)\n\
    - Metro Link Public License 1.00 (available at http://www.opengroup.org/openmotif/supporters/metrolink/license.html)\n\
    - Mozilla Public License Version 1.1 (available at http://www.mozilla.org/MPL/MPL-1.1.html)\n\
\n\
IT IS YOUR OBLIGATION TO READ AND ACCEPT ALL SUCH TERMS AND CONDITIONS PRIOR\n\
TO USE OF THE CONTENT. If no About, Feature License or Feature Update License\n\
is provided, please contact the Eclipse Foundation to determine what terms and conditions\n\
govern that particular Content.\n\
\n\
Cryptography\n\
\n\
Content may contain encryption software. The country in which you are\n\
currently may have restrictions on the import, possession, and use,\n\
and/or re-export to another country, of encryption software. BEFORE\n\
using any encryption software, please check the country's laws,\n\
regulations and policies concerning the import, possession, or use,\n\
and re-export of encryption software, to see if this is permitted.\n\
\n\
Java and all Java-based trademarks are trademarks of Sun Microsystems, Inc. in the United States, other countries, or both.\n
########### end of license property ##########################################

# "copyright" property - text of the "Feature Update Copyright"
copyright=\Copyright (c) 2000, 2007 SpringSource and others.\n\
All rights reserved. This program and the accompanying materials\n\
are made available under the terms of the Eclipse Public License v1.0\n\
which accompanies this distribution, and is available at\n\
http://www.eclipse.org/legal/epl-v10.html\n\
