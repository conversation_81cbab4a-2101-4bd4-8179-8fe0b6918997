<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN"
    "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"/>
<title>About</title>
</head>
<body lang="EN-US">
<h2>About This Content</h2>
 
<p>May 13, 2016</p>	
<h3>License</h3>

<p>The Eclipse Foundation makes available all content in this plug-in (&quot;Content&quot;).  Unless otherwise 
indicated below, the Content is provided to you under the terms and conditions of the
Eclipse Public License Version 1.0 (&quot;EPL&quot;).  A copy of the EPL is available 
at <a href="http://www.eclipse.org/legal/epl-v10.html">http://www.eclipse.org/legal/epl-v10.html</a>.
For purposes of the EPL, &quot;Program&quot; will mean the Content.</p>

<p>
Cloud Foundry Tools is dual licensed, and in addition to Eclipse Public License Version 1.0, the Content is also provided under Apache License - Version 2.0:
   <br/>
   <br/>
   Licensed under the Apache License, Version 2.0 (the &quot;License&quot;);
   you may not use this file except in compliance with the License.
   You may obtain a copy of the License at
   <br/>
   <br/>
   <a href="http://www.apache.org/licenses/LICENSE-2.0">http://www.apache.org/licenses/LICENSE-2.0</a>
   <br/>
   <br/>
   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an AS IS BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
</p>

<p>If you did not receive this Content directly from the Eclipse Foundation, the Content is 
being redistributed by another party (&quot;Redistributor&quot;) and different terms and conditions may
apply to your use of any object code in the Content.  Check the Redistributor's license that was 
provided with the Content.  If no such license exists, contact the Redistributor.  Unless otherwise
indicated below, the terms and conditions of the EPL still apply to any source code in the Content
and such source code may be obtained at <a href="http://www.eclipse.org/">http://www.eclipse.org</a>.</p>

		
		<h3>Third Party Content</h3>
		<p>The Content includes items that have been sourced from third parties as set out below. If you 
		did not receive this Content directly from the Eclipse Foundation, the following is provided 
		for informational purposes only, and you should look to the Redistributor's license for 
		terms and conditions of use.</p>
		<p><em>
        <strong>cloudfoundry-client-lib Version: 1.1.4.20170519</strong> 
		<br/> Apache License 2.0 <br/>
		<br/> <strong>commons-logging-1.2</strong> 
		<br/> Apache License 2.0 <br/>
		<br/> <strong>Apache Commons Logging Jar Version: 1.1.1</strong>
		<br /> Apache License 2.0 <br />
		<br/> <strong>spring-aop-4.0.5</strong>
		<br /> Apache License 2.0 <br />
		<br /> <strong>spring-beans-4.0.5</strong>
		<br /> Apache License 2.0 <br />
		<br /> <strong>spring-context-4.0.5</strong>
		<br /> Apache License 2.0 <br />
		<br /> <strong>spring-expression-4.0.5</strong>
		<br /> Apache License 2.0 <br />
		<br /> <strong>org.springframework.core Version: 4.0.5</strong>
		<br /> Apache License 2.0 <br />
	    New BSD license <br />
		<br /> <strong>spring-web-4.0.5</strong>
		<br /> Apache License 2.0 <br />
		<br /> <strong>jackson-core-asl-1.9.13</strong>
		<br /> Apache License 2.0 <br />
		<br /> <strong>jackson-mapper-asl-1.9.13</strong>
		<br /> Apache License 2.0 <br />
		<br /> <strong>spring-boot-loader-1.2.3.RELEASE</strong>
		<br /> Apache License 2.0 <br />
		<br /> <strong>spring-boot-loader-tools-1.2.3</strong>
		<br /> Apache License 2.0 <br />
		<br /> <strong>spring-security-oauth-2.0.4.RELEASE</strong>
		<br /> Apache License 2.0 <br />
		<br /> <strong>tomcat-embed-core Version: 8.0.33</strong>
		<br /> Apache License 2.0 <br />
		Common Development and Distribution License <br />
		<br /> <strong>tomcat-embed-websocket Version: 8.0.33</strong>
		<br /> Apache License 2.0 <br />
		<br /> <strong>tomcat-embed-logging-juli Version: 8.0.33</strong>
		<br /> Apache License 2.0 <br />
		<br /> <strong>yamlbeans-1.06</strong>
		<br /> MIT License <br />
		<br /> <strong>Protocol Buffers Version: 2.6.1</strong>
		<br /> New BSD license <br />
		<br /> <strong>snakeyaml Version: 1.13</strong>
		<br /> Apache License 2.0 <br />
		<br /> <strong>spring-security-config Version: 3.2.5</strong>
		<br /> Apache License 2.0 <br />
		<br /> <strong>spring-security-core Version: 3.2.5</strong>
		<br /> Apache License 2.0 <br />
		<br /> <strong>spring-security-web Version: 3.2.5</strong>
		<br /> Apache License 2.0 <br />
		<br /> <strong>org.springframework.webmvc Version: 4.0.5</strong>
		<br /> Apache License 2.0 <br />
		<br /> <strong>jackson databind Version: 2.3.3</strong>
		<br /> Apache License 2.0 <br />
		<br /> <strong>org.springframework.core Version: 4.1.6.RELEASE</strong>
		<br /> Apache License 2.0 <br />
		<br /> <strong>Apache commons-logging Version: 1.1.3</strong>
		<br /> Apache License 2.0 <br />
		<br /> <strong>Apache Commons IO Version: 2.1</strong>
		<br /> Apache License 2.0 <br />
		<br /> <strong>Apache HttpComponents Client 4.3.6</strong>
		<br /> Apache License 2.0 <br />
		<br /> <strong>Apache HttpComponents Core 4.3.3</strong>
		<br /> Apache License 2.0 <br />
		<br /> <strong>jackson-annotations Version: 2.3.0</strong>
		<br /> Apache License 2.0 <br />
		<br /> <strong>AOP Alliance Version: 1.0</strong>
		<br /> Public Domain <br />
		<br /> <strong>Apache Commons Codec Version: 1.6</strong>
		<br /> Apache License 2.0 <br />
		<br /> <strong>jackson-core Version: 2.3.3</strong>
		<br /> Apache License 2.0 <br />
		<br />
		</em></p>


</body>
</html>