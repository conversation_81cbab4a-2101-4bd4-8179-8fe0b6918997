license=Copyright (c) 2008 <PERSON>, All Rights Reserved\n\
\n\
This library is free software; you can redistribute it and/or\n\
modify it under the terms of the GNU Lesser General Public\n\
License as published by the Free Software Foundation; either\n\
version 2.1 of the License, or (at your option) any later version.\n\
\n\
This library is distributed in the hope that it will be useful,\n\
but WITHOUT ANY WARRANTY; without even the implied warranty of\n\
MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU\n\
Lesser General Public License for more details.
description=JNA provides Java programs easy access to native shared libraries \
(DLLs on Windows) without writing anything but Java code - no JNI \
or native code is required. This functionality is comparable \
to Windows&apos; Platform/Invoke and Python&apos;s ctypes. \
Access is dynamic at runtime without code generation.
