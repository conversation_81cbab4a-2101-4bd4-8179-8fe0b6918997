<?xml version="1.0" encoding="UTF-8"?>
<feature
      id="org.eclipse.e4.rcp"
      label="%featureName"
      version="1.6.3.v20180329-0507"
      provider-name="%providerName">

   <description>
      %description
   </description>

   <copyright>
      %copyright
   </copyright>

   <license url="%licenseURL">
      %license
   </license>

   <requires>
      <import feature="org.eclipse.emf.common" version="2.7.0" match="compatible"/>
      <import feature="org.eclipse.emf.ecore" version="2.7.0" match="compatible"/>
   </requires>

   <plugin
         id="org.eclipse.e4.core.services"
         download-size="55"
         install-size="101"
         version="2.1.0.v20170407-0928"
         unpack="false"/>

   <plugin
         id="org.eclipse.e4.ui.workbench.swt"
         download-size="168"
         install-size="335"
         version="0.14.101.v20170710-1119"
         unpack="false"/>

   <plugin
         id="org.eclipse.e4.core.commands"
         download-size="25"
         install-size="44"
         version="0.12.100.v20170513-0428"
         unpack="false"/>

   <plugin
         id="org.eclipse.e4.ui.bindings"
         download-size="50"
         install-size="102"
         version="0.12.1.v20170823-1632"
         unpack="false"/>

   <plugin
         id="org.eclipse.e4.ui.model.workbench"
         download-size="380"
         install-size="1185"
         version="2.0.1.v20170713-1800"
         unpack="false"/>

   <plugin
         id="org.eclipse.e4.ui.services"
         download-size="25"
         install-size="39"
         version="1.3.0.v20170307-2032"
         unpack="false"/>

   <plugin
         id="org.eclipse.e4.ui.workbench.renderers.swt"
         download-size="255"
         install-size="574"
         version="0.14.102.v20180117-1153"
         unpack="false"/>

   <plugin
         id="org.eclipse.e4.ui.workbench"
         download-size="255"
         install-size="539"
         version="1.5.1.v20170815-1446"
         unpack="false"/>

   <plugin
         id="org.eclipse.e4.ui.css.core"
         download-size="210"
         install-size="430"
         version="0.12.101.v20170712-1547"
         unpack="false"/>

   <plugin
         id="org.eclipse.e4.ui.css.swt"
         download-size="256"
         install-size="536"
         version="0.13.1.v20170808-1940"
         unpack="false"/>

   <plugin
         id="org.apache.batik.css"
         download-size="286"
         install-size="564"
         version="1.8.0.v20170214-1941"
         unpack="false"/>

   <plugin
         id="org.w3c.css.sac"
         download-size="37"
         install-size="65"
         version="1.3.1.v200903091627"
         unpack="false"/>

   <plugin
         id="org.apache.batik.util"
         download-size="127"
         install-size="248"
         version="1.8.0.v20170214-1941"
         unpack="false"/>

   <plugin
         id="org.w3c.dom.svg"
         download-size="95"
         install-size="140"
         version="1.1.0.v201011041433"
         unpack="false"/>

   <plugin
         id="org.w3c.dom.smil"
         download-size="19"
         install-size="37"
         version="1.0.1.v200903091627"
         unpack="false"/>

   <plugin
         id="org.w3c.dom.events"
         download-size="17"
         install-size="26"
         version="3.0.0.draft20060413_v201105210656"
         unpack="false"/>

   <plugin
         id="javax.inject"
         download-size="16"
         install-size="28"
         version="1.0.0.v20091030"
         unpack="false"/>

   <plugin
         id="javax.annotation"
         download-size="28"
         install-size="58"
         version="1.2.0.v201602091430"
         unpack="false"/>

   <plugin
         id="org.eclipse.e4.core.di"
         download-size="53"
         install-size="108"
         version="1.6.100.v20170421-1418"
         unpack="false"/>

   <plugin
         id="org.eclipse.e4.core.contexts"
         download-size="47"
         install-size="92"
         version="1.6.0.v20170322-1144"
         unpack="false"/>

   <plugin
         id="org.apache.batik.util.gui"
         download-size="182"
         install-size="319"
         version="1.8.0.v20170214-1941"
         unpack="false"/>

   <plugin
         id="org.eclipse.e4.core.di.extensions"
         download-size="11"
         install-size="14"
         version="0.15.0.v20170228-1728"
         unpack="false"/>

   <plugin
         id="org.eclipse.e4.ui.css.swt.theme"
         download-size="27"
         install-size="49"
         version="0.11.0.v20170312-2302"
         unpack="false"/>

   <plugin
         id="org.eclipse.e4.ui.di"
         download-size="17"
         install-size="25"
         version="1.2.100.v20170414-1137"
         unpack="false"/>

   <plugin
         id="org.eclipse.e4.ui.widgets"
         download-size="15"
         install-size="23"
         version="1.2.0.v20160630-0736"
         unpack="false"/>

   <plugin
         id="org.eclipse.e4.ui.workbench.renderers.swt.cocoa"
         os="macosx"
         ws="cocoa"
         download-size="35"
         install-size="65"
         version="0.11.300.v20160330-1418"
         fragment="true"
         unpack="false"/>

   <plugin
         id="org.eclipse.equinox.common"
         download-size="118"
         install-size="223"
         version="3.9.0.v20170207-1454"
         unpack="false"/>

   <plugin
         id="org.apache.felix.scr"
         download-size="363"
         install-size="841"
         version="2.0.10.v20170501-2007"
         unpack="false"/>

   <plugin
         id="org.eclipse.equinox.ds"
         download-size="28"
         install-size="56"
         version="1.5.0.v20170307-1429"
         unpack="false"/>

   <plugin
         id="org.eclipse.equinox.event"
         download-size="33"
         install-size="60"
         version="1.4.0.v20170105-1446"
         unpack="false"/>

   <plugin
         id="org.eclipse.core.commands"
         download-size="112"
         install-size="215"
         version="3.9.0.v20170530-1048"
         unpack="false"/>

   <plugin
         id="org.eclipse.core.contenttype"
         download-size="101"
         install-size="213"
         version="3.6.0.v20170207-1037"
         unpack="false"/>

   <plugin
         id="org.eclipse.core.databinding"
         download-size="203"
         install-size="422"
         version="1.6.100.v20170515-1119"
         unpack="false"/>

   <plugin
         id="org.eclipse.core.databinding.beans"
         download-size="78"
         install-size="193"
         version="1.4.0.v20170210-0856"
         unpack="false"/>

   <plugin
         id="org.eclipse.core.databinding.observable"
         download-size="346"
         install-size="800"
         version="1.6.100.v20170515-1119"
         unpack="false"/>

   <plugin
         id="org.eclipse.core.databinding.property"
         download-size="191"
         install-size="499"
         version="1.6.100.v20170515-1119"
         unpack="false"/>

   <plugin
         id="org.eclipse.core.expressions"
         download-size="89"
         install-size="175"
         version="3.6.0.v20170207-1037"
         unpack="false"/>

   <plugin
         id="org.eclipse.core.jobs"
         download-size="105"
         install-size="208"
         version="3.9.3.v20180115-1757"
         unpack="false"/>

   <plugin
         id="org.eclipse.core.runtime"
         download-size="74"
         install-size="156"
         version="3.13.0.v20170207-1030"
         unpack="false"/>

   <plugin
         id="org.eclipse.equinox.app"
         download-size="85"
         install-size="171"
         version="1.3.400.v20150715-1528"
         unpack="false"/>

   <plugin
         id="org.eclipse.equinox.launcher"
         download-size="53"
         install-size="95"
         version="1.4.0.v20161219-1356"
         unpack="false"/>

   <plugin
         id="com.ibm.icu"
         download-size="11775"
         install-size="26242"
         version="58.2.0.v20170418-1837"
         unpack="false"/>

   <plugin
         id="org.eclipse.equinox.preferences"
         download-size="130"
         install-size="264"
         version="3.7.0.v20170126-2132"
         unpack="false"/>

   <plugin
         id="org.eclipse.equinox.registry"
         download-size="182"
         install-size="377"
         version="3.7.0.v20170222-1344"
         unpack="false"/>

   <plugin
         id="org.eclipse.equinox.simpleconfigurator"
         download-size="45"
         install-size="83"
         version="1.2.1.v20180131-1435"
         unpack="false"/>

   <plugin
         id="org.eclipse.osgi"
         download-size="1355"
         install-size="2875"
         version="3.12.100.v20180210-1608"
         unpack="false"/>

   <plugin
         id="org.eclipse.osgi.compatibility.state"
         download-size="238"
         install-size="581"
         version="1.1.0.v20170516-1513"
         fragment="true"
         unpack="false"/>

   <plugin
         id="org.eclipse.osgi.services"
         download-size="118"
         install-size="184"
         version="3.6.0.v20170228-1906"
         unpack="false"/>

   <plugin
         id="org.eclipse.osgi.util"
         download-size="49"
         install-size="96"
         version="3.4.0.v20170111-1608"
         unpack="false"/>

   <plugin
         id="org.eclipse.equinox.launcher.cocoa.macosx.x86_64"
         os="macosx"
         ws="cocoa"
         arch="x86_64"
         download-size="33"
         install-size="69"
         version="1.1.551.v20171108-1834"
         fragment="true"/>

   <plugin
         id="org.eclipse.equinox.launcher.gtk.linux.ppc64"
         os="linux"
         ws="gtk"
         arch="ppc64"
         download-size="83"
         install-size="260"
         version="1.1.551.v20171108-1834"
         fragment="true"/>

   <plugin
         id="org.eclipse.equinox.launcher.gtk.linux.ppc64le"
         os="linux"
         ws="gtk"
         arch="ppc64le"
         download-size="81"
         install-size="258"
         version="1.1.551.v20171108-1834"
         fragment="true"/>

   <plugin
         id="org.eclipse.equinox.launcher.gtk.linux.x86"
         os="linux"
         ws="gtk"
         arch="x86"
         download-size="77"
         install-size="168"
         version="1.1.551.v20171108-1834"
         fragment="true"/>

   <plugin
         id="org.eclipse.equinox.launcher.gtk.linux.x86_64"
         os="linux"
         ws="gtk"
         arch="x86_64"
         download-size="81"
         install-size="188"
         version="1.1.551.v20171108-1834"
         fragment="true"/>

   <plugin
         id="org.eclipse.equinox.launcher.win32.win32.x86"
         os="win32"
         ws="win32"
         arch="x86"
         download-size="27"
         install-size="63"
         version="1.1.551.v20171108-1834"
         fragment="true"/>

   <plugin
         id="org.eclipse.equinox.launcher.win32.win32.x86_64"
         os="win32"
         ws="win32"
         arch="x86_64"
         download-size="29"
         install-size="67"
         version="1.1.551.v20171108-1834"
         fragment="true"/>

   <plugin
         id="org.eclipse.swt"
         download-size="18"
         install-size="38"
         version="3.106.3.v20180329-0507"
         unpack="false"/>

   <plugin
         id="org.eclipse.swt.win32.win32.x86"
         os="win32"
         ws="win32"
         arch="x86"
         download-size="2715"
         install-size="5671"
         version="3.106.3.v20180329-0507"
         fragment="true"
         unpack="false"/>

   <plugin
         id="org.eclipse.swt.win32.win32.x86_64"
         os="win32"
         ws="win32"
         arch="x86_64"
         download-size="2718"
         install-size="5769"
         version="3.106.3.v20180329-0507"
         fragment="true"
         unpack="false"/>

   <plugin
         id="org.eclipse.swt.gtk.linux.x86"
         os="linux"
         ws="gtk"
         arch="x86"
         download-size="2495"
         install-size="5823"
         version="3.106.3.v20180329-0507"
         fragment="true"
         unpack="false"/>

   <plugin
         id="org.eclipse.swt.gtk.linux.ppc64"
         os="linux"
         ws="gtk"
         arch="ppc64"
         download-size="2603"
         install-size="7392"
         version="3.106.3.v20180329-0507"
         fragment="true"
         unpack="false"/>

   <plugin
         id="org.eclipse.swt.gtk.linux.ppc64le"
         os="linux"
         ws="gtk"
         arch="ppc64le"
         download-size="2437"
         install-size="6486"
         version="3.106.3.v20180329-0507"
         fragment="true"
         unpack="false"/>

   <plugin
         id="org.eclipse.swt.gtk.linux.x86_64"
         os="linux"
         ws="gtk"
         arch="x86_64"
         download-size="2595"
         install-size="6330"
         version="3.106.3.v20180329-0507"
         fragment="true"
         unpack="false"/>

   <plugin
         id="org.eclipse.swt.cocoa.macosx.x86_64"
         os="macosx"
         ws="cocoa"
         arch="x86_64"
         download-size="2339"
         install-size="5436"
         version="3.106.3.v20180329-0507"
         fragment="true"
         unpack="false"/>

   <plugin
         id="org.eclipse.equinox.util"
         download-size="75"
         install-size="139"
         version="1.0.500.v20130404-1337"
         unpack="false"/>

   <plugin
         id="org.eclipse.jface"
         download-size="1071"
         install-size="2160"
         version="3.13.2.v20171022-1656"
         unpack="false"/>

   <plugin
         id="org.eclipse.jface.databinding"
         download-size="285"
         install-size="579"
         version="1.8.100.v20170503-1507"
         unpack="false"/>

   <plugin
         id="org.eclipse.e4.ui.workbench3"
         download-size="10"
         install-size="14"
         version="0.14.0.v20160630-0740"
         unpack="false"/>

   <plugin
         id="org.apache.felix.gogo.command"
         download-size="55"
         install-size="111"
         version="0.10.0.v201209301215"
         unpack="false"/>

   <plugin
         id="org.apache.felix.gogo.runtime"
         download-size="77"
         install-size="153"
         version="0.10.0.v201209301036"
         unpack="false"/>

   <plugin
         id="org.apache.felix.gogo.shell"
         download-size="58"
         install-size="119"
         version="0.10.0.v201212101605"
         unpack="false"/>

   <plugin
         id="org.eclipse.equinox.console"
         download-size="117"
         install-size="239"
         version="1.1.300.v20170512-2111"
         unpack="false"/>

   <plugin
         id="org.eclipse.e4.ui.workbench.addons.swt"
         download-size="153"
         install-size="317"
         version="1.3.1.v20170319-1442"
         unpack="false"/>

   <plugin
         id="org.eclipse.equinox.bidi"
         download-size="48"
         install-size="90"
         version="1.1.0.v20160728-1031"
         unpack="false"/>

   <plugin
         id="org.eclipse.e4.ui.dialogs"
         download-size="45"
         install-size="80"
         version="1.1.100.v20170104-1425"
         unpack="false"/>

   <plugin
         id="org.apache.commons.jxpath"
         download-size="308"
         install-size="642"
         version="1.3.0.v200911051830"
         unpack="false"/>

   <plugin
         id="org.eclipse.e4.emf.xpath"
         download-size="48"
         install-size="95"
         version="0.2.0.v20160630-0728"
         unpack="false"/>

   <plugin
         id="org.eclipse.e4.core.di.annotations"
         download-size="11"
         install-size="15"
         version="1.6.0.v20170119-2002"
         unpack="false"/>

   <plugin
         id="org.eclipse.e4.ui.swt.gtk"
         ws="gtk"
         download-size="10"
         install-size="14"
         version="1.0.200.v20170513-0428"
         fragment="true"
         unpack="false"/>

   <plugin
         id="org.eclipse.e4.core.di.extensions.supplier"
         download-size="34"
         install-size="66"
         version="0.15.0.v20170407-0928"
         unpack="false"/>

</feature>
