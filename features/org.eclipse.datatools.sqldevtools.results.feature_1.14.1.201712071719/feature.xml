<?xml version="1.0" encoding="UTF-8"?>
<feature
      id="org.eclipse.datatools.sqldevtools.results.feature"
      label="%featureName"
      version="1.14.1.201712071719"
      provider-name="%providerName"
      image="eclipse_update_120.jpg">

   <description>
      %description
   </description>

   <copyright>
      %featureCopyright
   </copyright>

   <license url="%licenseURL">
      %license
   </license>

   <requires>
      <import feature="org.eclipse.datatools.modelbase.feature" version="1.13.0" match="greaterOrEqual"/>
      <import feature="org.eclipse.datatools.connectivity.feature" version="1.13.0" match="greaterOrEqual"/>
      <import feature="org.eclipse.datatools.doc.user" version="1.13.0" match="greaterOrEqual"/>
   </requires>

   <plugin
         id="org.eclipse.datatools.sqltools.common.ui"
         download-size="0"
         install-size="0"
         version="1.2.1.201712071719"
         unpack="false"/>

   <plugin
         id="org.eclipse.datatools.sqltools.result"
         download-size="0"
         install-size="0"
         version="1.3.1.201712071719"
         unpack="false"/>

   <plugin
         id="org.eclipse.datatools.connectivity"
         download-size="0"
         install-size="0"
         version="1.14.1.201712071719"
         unpack="false"/>

</feature>
