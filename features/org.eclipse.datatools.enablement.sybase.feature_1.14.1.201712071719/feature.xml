<?xml version="1.0" encoding="UTF-8"?>
<feature
      id="org.eclipse.datatools.enablement.sybase.feature"
      label="%featureName"
      version="1.14.1.201712071719"
      provider-name="%providerName"
      plugin="org.eclipse.datatools.enablement.sybase"
      image="eclipse_update_120.jpg">

   <description>
      %description
   </description>

   <copyright>
      %featureCopyright
   </copyright>

   <license url="%licenseURL">
      %license
   </license>

   <requires>
      <import feature="org.eclipse.datatools.modelbase.feature" version="1.13.0" match="greaterOrEqual"/>
      <import feature="org.eclipse.datatools.connectivity.feature" version="1.13.0" match="greaterOrEqual"/>
      <import feature="org.eclipse.datatools.sqldevtools.feature" version="1.13.0" match="greaterOrEqual"/>
      <import feature="org.eclipse.datatools.enablement.jdbc.feature" version="1.13.0" match="greaterOrEqual"/>
   </requires>

   <plugin
         id="org.eclipse.datatools.enablement.sybase.asa.models"
         download-size="0"
         install-size="0"
         version="1.2.1.201712071719"
         unpack="false"/>

   <plugin
         id="org.eclipse.datatools.enablement.sybase.ase.models"
         download-size="0"
         install-size="0"
         version="1.2.1.201712071719"
         unpack="false"/>

   <plugin
         id="org.eclipse.datatools.enablement.sybase.asa"
         download-size="0"
         install-size="0"
         version="1.2.1.201712071719"
         unpack="false"/>

   <plugin
         id="org.eclipse.datatools.enablement.sybase.ase"
         download-size="0"
         install-size="0"
         version="1.2.1.201712071719"
         unpack="false"/>

   <plugin
         id="org.eclipse.datatools.enablement.sybase.ase.dbdefinition"
         download-size="0"
         install-size="0"
         version="1.2.1.201712071719"
         unpack="false"/>

   <plugin
         id="org.eclipse.datatools.enablement.sybase.asa.dbdefinition"
         download-size="0"
         install-size="0"
         version="1.2.1.201712071719"
         unpack="false"/>

   <plugin
         id="org.eclipse.datatools.enablement.sybase.asa.ui"
         download-size="0"
         install-size="0"
         version="1.2.1.201712071719"
         unpack="false"/>

   <plugin
         id="org.eclipse.datatools.enablement.sybase.ase.ui"
         download-size="0"
         install-size="0"
         version="1.2.1.201712071719"
         unpack="false"/>

   <plugin
         id="org.eclipse.datatools.enablement.sybase"
         download-size="0"
         install-size="0"
         version="1.2.1.201712071719"
         unpack="false"/>

   <plugin
         id="org.eclipse.datatools.enablement.sybase.models"
         download-size="0"
         install-size="0"
         version="1.2.1.201712071719"
         unpack="false"/>

   <plugin
         id="org.eclipse.datatools.enablement.sybase.ui"
         download-size="0"
         install-size="0"
         version="1.2.1.201712071719"
         unpack="false"/>

   <plugin
         id="org.eclipse.datatools.enablement.sybase.asa.schemaobjecteditor.examples"
         download-size="0"
         install-size="0"
         version="2.7.1.200810071"
         unpack="false"/>

</feature>
