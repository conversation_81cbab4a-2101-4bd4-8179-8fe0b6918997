<?xml version="1.0" encoding="UTF-8"?>
<feature
      id="org.eclipse.ecf.core.feature"
      label="ECF Core Feature"
      version="1.4.0.v20170516-2248"
      provider-name="Eclipse.org - ECF">

   <description url="http://www.eclipse.org/ecf">
      This feature provides the ECF core (org.eclipse.ecf) and ECF identity (org.eclipse.ecf.identity) bundles.  These two bundles are required for all other parts of ECF.
   </description>

   <copyright>
      Copyright (c) 2009 Composent, Inc. and others. All rights
reserved.
This program and the accompanying materials are made available
under the terms of the Eclipse Public License v1.0 which accompanies
this distribution, and is available at 
http://www.eclipse.org/legal/epl-v10.html
 
Contributors: Composent, Inc. - initial API and implementation
   </copyright>

   <license url="%licenseURL">
      %license
   </license>

   <requires>
      <import plugin="org.eclipse.equinox.common" version="3.6" match="compatible"/>
      <import plugin="org.eclipse.equinox.registry" version="3.5" match="compatible"/>
      <import plugin="org.eclipse.equinox.concurrent" version="1.0" match="compatible"/>
      <import plugin="org.eclipse.core.jobs" version="3.5" match="compatible"/>
   </requires>

   <plugin
         id="org.eclipse.ecf"
         download-size="106"
         install-size="208"
         version="3.8.0.v20170104-0657"
         unpack="false"/>

   <plugin
         id="org.eclipse.ecf.identity"
         download-size="61"
         install-size="120"
         version="3.8.0.v20161203-2153"
         unpack="false"/>

</feature>
