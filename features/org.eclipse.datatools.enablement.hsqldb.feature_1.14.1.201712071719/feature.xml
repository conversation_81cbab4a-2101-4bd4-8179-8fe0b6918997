<?xml version="1.0" encoding="UTF-8"?>
<feature
      id="org.eclipse.datatools.enablement.hsqldb.feature"
      label="%featureName"
      version="1.14.1.201712071719"
      provider-name="%providerName"
      plugin="org.eclipse.datatools.enablement.finfo"
      image="eclipse_update_120.jpg">

   <description>
      %description
   </description>

   <copyright>
      %featureCopyright
   </copyright>

   <license url="%licenseURL">
      %license
   </license>

   <includes
         id="org.eclipse.datatools.enablement.jdbc.feature"
         version="1.14.1.201712071719"/>

   <requires>
      <import feature="org.eclipse.datatools.modelbase.feature" version="1.13.0" match="greaterOrEqual"/>
      <import feature="org.eclipse.datatools.connectivity.feature" version="1.13.0" match="greaterOrEqual"/>
   </requires>

   <plugin
         id="org.eclipse.datatools.enablement.hsqldb"
         download-size="0"
         install-size="0"
         version="1.2.1.201712071719"
         unpack="false"/>

   <plugin
         id="org.eclipse.datatools.enablement.hsqldb.dbdefinition"
         download-size="0"
         install-size="0"
         version="1.2.1.201712071719"
         unpack="false"/>

   <plugin
         id="org.eclipse.datatools.enablement.hsqldb.ui"
         download-size="0"
         install-size="0"
         version="1.2.1.201712071719"
         unpack="false"/>

   <plugin
         id="org.eclipse.datatools.enablement.finfo"
         download-size="0"
         install-size="0"
         version="1.7.0.201712071719"
         unpack="false"/>

</feature>
