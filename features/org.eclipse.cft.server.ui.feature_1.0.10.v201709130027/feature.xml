<?xml version="1.0" encoding="UTF-8"?>
<feature
      id="org.eclipse.cft.server.ui.feature"
      label="%featureName"
      version="1.0.10.v201709130027"
      provider-name="%providerName"
      plugin="org.eclipse.cft.server.branding.ui">

   <description>
      %description
   </description>

   <copyright>
      %copyright
   </copyright>

   <license url="%licenseUrl">
      %license
   </license>

   <requires>
      <import feature="org.eclipse.cft.server.core.feature"/>
   </requires>

   <plugin
         id="org.eclipse.cft.server.rse"
         download-size="48"
         install-size="105"
         version="1.0.1.v201709130027"
         unpack="false"/>

   <plugin
         id="org.eclipse.cft.server.ui"
         download-size="827"
         install-size="1788"
         version="1.0.110.v201709130027"
         unpack="false"/>

   <plugin
         id="org.eclipse.cft.server.verify.ui"
         download-size="25"
         install-size="50"
         version="1.0.1.v201709130027"
         unpack="false"/>

   <plugin
         id="org.eclipse.cft.server.branding.ui"
         download-size="34"
         install-size="57"
         version="1.0.2.v201709130027"
         unpack="false"/>

   <plugin
         id="org.eclipse.cft.server.standalone.ui"
         download-size="1129"
         install-size="1339"
         version="1.0.4.v201709130027"
         unpack="false"/>

</feature>
