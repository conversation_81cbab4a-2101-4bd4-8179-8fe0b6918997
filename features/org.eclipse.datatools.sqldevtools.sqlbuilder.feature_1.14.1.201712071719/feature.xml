<?xml version="1.0" encoding="UTF-8"?>
<feature
      id="org.eclipse.datatools.sqldevtools.sqlbuilder.feature"
      label="%featureName"
      version="1.14.1.201712071719"
      provider-name="%providerName"
      image="eclipse_update_120.jpg">

   <description>
      %description
   </description>

   <copyright>
      %featureCopyright
   </copyright>

   <license url="%licenseURL">
      %license
   </license>

   <requires>
      <import feature="org.eclipse.emf" version="2.4.0" match="greaterOrEqual"/>
      <import feature="org.eclipse.gef" version="3.4.0" match="greaterOrEqual"/>
      <import feature="org.eclipse.datatools.modelbase.feature" version="1.13.0" match="greaterOrEqual"/>
      <import feature="org.eclipse.datatools.connectivity.feature" version="1.13.0" match="greaterOrEqual"/>
      <import feature="org.eclipse.datatools.doc.user" version="1.13.0" match="greaterOrEqual"/>
      <import feature="org.eclipse.datatools.enablement.jdbc.feature" version="1.13.0" match="greaterOrEqual"/>
      <import feature="org.eclipse.datatools.sqldevtools.results.feature" version="1.13.0" match="greaterOrEqual"/>
      <import feature="org.eclipse.datatools.sqldevtools.parsers.feature" version="1.13.0" match="greaterOrEqual"/>
   </requires>

   <plugin
         id="org.eclipse.datatools.sqltools.sqlbuilder"
         download-size="0"
         install-size="0"
         version="1.2.1.201712071719"
         unpack="false"/>

   <plugin
         id="org.eclipse.datatools.sqltools.editor.core"
         download-size="0"
         install-size="0"
         version="1.2.1.201712071719"
         unpack="false"/>

   <plugin
         id="org.eclipse.datatools.sqltools.sql"
         download-size="0"
         install-size="0"
         version="1.2.1.201712071719"
         unpack="false"/>

   <plugin
         id="org.eclipse.datatools.sqltools.sqleditor"
         download-size="0"
         install-size="0"
         version="1.2.1.201712071719"
         unpack="false"/>

   <plugin
         id="org.eclipse.datatools.sqltools.plan"
         download-size="0"
         install-size="0"
         version="1.2.1.201712071719"
         unpack="false"/>

   <plugin
         id="org.eclipse.datatools.sqltools.db.generic"
         download-size="0"
         install-size="0"
         version="1.2.1.201712071719"
         unpack="false"/>

   <plugin
         id="org.eclipse.datatools.sqltools.editor.core.ui"
         download-size="0"
         install-size="0"
         version="1.2.1.201712071719"
         unpack="false"/>

   <plugin
         id="org.eclipse.datatools.sqltools.sql.ui"
         download-size="0"
         install-size="0"
         version="1.2.1.201712071719"
         unpack="false"/>

</feature>
