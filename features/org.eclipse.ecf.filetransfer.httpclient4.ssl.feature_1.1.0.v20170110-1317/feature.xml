<?xml version="1.0" encoding="UTF-8"?>
<feature
      id="org.eclipse.ecf.filetransfer.httpclient4.ssl.feature"
      label="ECF Httpclient4 Filetransfer SSL Provider"
      version="1.1.0.v20170110-1317"
      provider-name="Eclipse.org - ECF">

   <description url="http://www.eclipse.org/ecf">
      This feature provides the SSL support for the Apache HttpComponents/HttpClient4-based FileTransfer provider used by the Eclipse platform to support P2 filetransfer.
   </description>

   <copyright url="http://www.example.com/copyright">
      Copyright (c) 2014 Composent, Inc. and others. All rights reserved.
This program and the accompanying materials are made available
under the terms of the Eclipse Public License v1.0 which accompanies
this distribution, and is available at 
http://www.eclipse.org/legal/epl-v10.html
 
Contributors: Composent, Inc. - initial API and implementation
   </copyright>

   <license url="%licenseURL">
      %license
   </license>

   <requires>
      <import feature="org.eclipse.ecf.filetransfer.ssl.feature" version="1.0" match="compatible"/>
   </requires>

   <plugin
         id="org.eclipse.ecf.provider.filetransfer.httpclient4.ssl"
         download-size="13"
         install-size="25"
         version="1.1.0.v20160817-1024"
         fragment="true"
         unpack="false"/>

</feature>
