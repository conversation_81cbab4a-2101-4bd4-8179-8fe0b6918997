<?xml version="1.0" encoding="UTF-8"?>
<feature
      id="org.eclipse.datatools.modelbase.feature"
      label="%featureName"
      version="1.14.1.201712071719"
      provider-name="%providerName"
      plugin="org.eclipse.datatools.modelbase.sql"
      image="eclipse_update_120.jpg">

   <description>
      %description
   </description>

   <copyright>
      %featureCopyright
   </copyright>

   <license url="%licenseURL">
      %license
   </license>

   <requires>
      <import feature="org.eclipse.emf" version="2.4.0" match="greaterOrEqual"/>
   </requires>

   <plugin
         id="org.eclipse.datatools.modelbase.dbdefinition"
         download-size="0"
         install-size="0"
         version="1.2.1.201712071719"
         unpack="false"/>

   <plugin
         id="org.eclipse.datatools.modelbase.sql.edit"
         download-size="0"
         install-size="0"
         version="1.2.1.201712071719"
         unpack="false"/>

   <plugin
         id="org.eclipse.datatools.modelbase.sql.query"
         download-size="0"
         install-size="0"
         version="1.3.1.201712071719"
         unpack="false"/>

   <plugin
         id="org.eclipse.datatools.modelbase.sql.query.edit"
         download-size="0"
         install-size="0"
         version="1.2.1.201712071719"
         unpack="false"/>

   <plugin
         id="org.eclipse.datatools.modelbase.sql.xml.query"
         download-size="0"
         install-size="0"
         version="1.2.1.201712071719"
         unpack="false"/>

   <plugin
         id="org.eclipse.datatools.modelbase.sql"
         download-size="0"
         install-size="0"
         version="1.2.1.201712071719"
         unpack="false"/>

</feature>
