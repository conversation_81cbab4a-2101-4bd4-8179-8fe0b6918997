<?xml version="1.0" encoding="UTF-8"?>
<!-- don't modify the feature's version attribute as it will be replaced by the build -->
<feature
      id="org.eclipse.buildship"
      label="%featureName"
      version="2.2.1.v20180125-1441"
      provider-name="%providerName"
      plugin="org.eclipse.buildship.branding">

   <description>
      %description
   </description>

   <copyright>
      %copyright
   </copyright>

   <license url="%licenseUrl">
      %license
   </license>

   <plugin
         id="org.eclipse.buildship.core"
         download-size="0"
         install-size="0"
         version="2.2.1.v20180125-1441"
         unpack="false"/>

   <plugin
         id="org.eclipse.buildship.ui"
         download-size="0"
         install-size="0"
         version="2.2.1.v20180125-1441"
         unpack="false"/>

   <plugin
         id="org.eclipse.buildship.branding"
         download-size="0"
         install-size="0"
         version="2.2.1.v20180125-1441"
         unpack="false"/>

   <plugin
         id="org.eclipse.buildship.stsmigration"
         download-size="0"
         install-size="0"
         version="2.2.1.v20180125-1441"
         unpack="false"/>

</feature>
