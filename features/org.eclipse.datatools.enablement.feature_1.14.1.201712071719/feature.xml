<?xml version="1.0" encoding="UTF-8"?>
<feature
      id="org.eclipse.datatools.enablement.feature"
      label="%featureName"
      version="1.14.1.201712071719"
      provider-name="%providerName"
      plugin="org.eclipse.datatools.enablement.finfo"
      image="eclipse_update_120.jpg">

   <description>
      %description
   </description>

   <copyright>
      %featureCopyright
   </copyright>

   <license url="%licenseURL">
      %license
   </license>

   <includes
         id="org.eclipse.datatools.enablement.apache.derby.feature"
         version="1.14.1.201712071719"
         optional="true"/>

   <includes
         id="org.eclipse.datatools.enablement.hsqldb.feature"
         version="1.14.1.201712071719"
         optional="true"/>

   <includes
         id="org.eclipse.datatools.enablement.ibm.feature"
         version="1.14.1.201712071719"
         optional="true"/>

   <includes
         id="org.eclipse.datatools.enablement.jdbc.feature"
         version="1.14.1.201712071719"
         optional="true"/>

   <includes
         id="org.eclipse.datatools.enablement.jdt.feature"
         version="1.14.1.201712071719"
         optional="true"/>

   <includes
         id="org.eclipse.datatools.enablement.msft.feature"
         version="1.14.1.201712071719"
         optional="true"/>

   <includes
         id="org.eclipse.datatools.enablement.mysql.feature"
         version="1.14.1.201712071719"
         optional="true"/>

   <includes
         id="org.eclipse.datatools.enablement.oda.feature"
         version="1.14.1.201712071719"
         optional="true"/>

   <includes
         id="org.eclipse.datatools.enablement.oda.designer.feature"
         version="1.14.1.201712071719"
         optional="true"/>

   <includes
         id="org.eclipse.datatools.enablement.oracle.feature"
         version="1.14.1.201712071719"
         optional="true"/>

   <includes
         id="org.eclipse.datatools.enablement.postgresql.feature"
         version="1.14.1.201712071719"
         optional="true"/>

   <includes
         id="org.eclipse.datatools.enablement.sap.feature"
         version="1.14.1.201712071719"
         optional="true"/>

   <includes
         id="org.eclipse.datatools.enablement.sybase.feature"
         version="1.14.1.201712071719"
         optional="true"/>

   <includes
         id="org.eclipse.datatools.enablement.ingres.feature"
         version="1.14.1.201712071719"
         optional="true"/>

   <includes
         id="org.eclipse.datatools.enablement.sqlite.feature"
         version="1.14.1.201712071719"
         optional="true"/>

   <requires>
      <import feature="org.eclipse.datatools.modelbase.feature" version="1.13.0" match="greaterOrEqual"/>
      <import feature="org.eclipse.datatools.connectivity.feature" version="1.13.0" match="greaterOrEqual"/>
   </requires>

   <plugin
         id="org.eclipse.datatools.enablement.finfo"
         download-size="0"
         install-size="0"
         version="1.7.0.201712071719"
         unpack="false"/>

   <plugin
         id="org.eclipse.datatools.sqltools.db.derby.ui"
         download-size="0"
         install-size="0"
         version="1.2.1.201712071719"
         unpack="false"/>

   <plugin
         id="org.eclipse.datatools.sqltools.db.generic.ui"
         download-size="0"
         install-size="0"
         version="1.2.1.201712071719"
         unpack="false"/>

</feature>
