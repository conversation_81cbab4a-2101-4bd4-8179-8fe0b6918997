<?xml version="1.0" encoding="UTF-8"?>
<config date="1524733781615" transient="false" version="3.0">
	<site updateable="true" url="platform:/base/" enabled="true" policy="USER-EXCLUDE">
		<feature id="org.eclipse.datatools.enablement.postgresql.feature" version="1.14.1.201712071719" url="features/org.eclipse.datatools.enablement.postgresql.feature_1.14.1.201712071719/" plugin-identifier="org.eclipse.datatools.enablement.finfo">
		</feature>
		<feature id="org.springframework.ide.eclipse.webflow.feature" version="3.9.4.201804120850-RELEASE" url="features/org.springframework.ide.eclipse.webflow.feature_3.9.4.201804120850-RELEASE/" plugin-identifier="org.springframework.ide.eclipse">
		</feature>
		<feature id="org.eclipse.rse.core" version="3.7.3.201704251225" url="features/org.eclipse.rse.core_3.7.3.201704251225/">
		</feature>
		<feature id="org.eclipse.oomph.setup.core" version="1.8.0.v20170531-0903" url="features/org.eclipse.oomph.setup.core_1.8.0.v20170531-0903/">
		</feature>
		<feature id="org.eclipse.jpt.jaxb.eclipselink.feature" version="1.4.201.v201803161350" url="features/org.eclipse.jpt.jaxb.eclipselink.feature_1.4.201.v201803161350/" plugin-identifier="org.eclipse.jpt.jaxb.eclipselink.branding">
		</feature>
		<feature id="org.eclipse.datatools.sqldevtools.ddl.feature" version="1.14.1.201712071719" url="features/org.eclipse.datatools.sqldevtools.ddl.feature_1.14.1.201712071719/">
		</feature>
		<feature id="org.springframework.ide.eclipse.cft.feature" version="3.9.4.201804120850-RELEASE" url="features/org.springframework.ide.eclipse.cft.feature_3.9.4.201804120850-RELEASE/" plugin-identifier="org.springframework.ide.eclipse">
		</feature>
		<feature id="org.eclipse.pde" version="3.13.4.v20180330-0640" url="features/org.eclipse.pde_3.13.4.v20180330-0640/">
		</feature>
		<feature id="org.eclipse.wst.xml_core.feature" version="3.9.2.v201711080222" url="features/org.eclipse.wst.xml_core.feature_3.9.2.v201711080222/">
		</feature>
		<feature id="org.eclipse.jpt.jaxb.feature" version="1.5.100.v201803012210" url="features/org.eclipse.jpt.jaxb.feature_1.5.100.v201803012210/" plugin-identifier="org.eclipse.jpt.jaxb.branding">
		</feature>
		<feature id="org.eclipse.m2e.logback.feature" version="1.8.3.20180227-2137" url="features/org.eclipse.m2e.logback.feature_1.8.3.20180227-2137/" plugin-identifier="org.eclipse.m2e.logback.configuration">
		</feature>
		<feature id="org.eclipse.jpt.common.eclipselink.feature" version="1.3.200.v201803012210" url="features/org.eclipse.jpt.common.eclipselink.feature_1.3.200.v201803012210/" plugin-identifier="org.eclipse.jpt.common.eclipselink.branding">
		</feature>
		<feature id="org.eclipse.draw2d" version="3.10.100.201606061308" url="features/org.eclipse.draw2d_3.10.100.201606061308/">
		</feature>
		<feature id="org.eclipse.datatools.enablement.jdt.feature" version="1.14.1.201712071719" url="features/org.eclipse.datatools.enablement.jdt.feature_1.14.1.201712071719/" plugin-identifier="org.eclipse.datatools.enablement.jdt.classpath">
		</feature>
		<feature id="org.eclipse.jst.ws.axis2tools.feature" version="1.1.301.v201410160332" url="features/org.eclipse.jst.ws.axis2tools.feature_1.1.301.v201410160332/" plugin-identifier="org.eclipse.jst.ws.axis2.ui">
		</feature>
		<feature id="org.eclipse.m2e.feature" version="1.8.3.20180227-2137" url="features/org.eclipse.m2e.feature_1.8.3.20180227-2137/" plugin-identifier="org.eclipse.m2e.core">
		</feature>
		<feature id="org.eclipse.jst.server_adapters.feature" version="3.2.400.v201711301708" url="features/org.eclipse.jst.server_adapters.feature_3.2.400.v201711301708/">
		</feature>
		<feature id="org.springframework.ide.eclipse.maven.feature.source" version="3.9.4.201804120850-RELEASE" url="features/org.springframework.ide.eclipse.maven.feature.source_3.9.4.201804120850-RELEASE/" plugin-identifier="org.springframework.ide.eclipse">
		</feature>
		<feature id="org.springframework.ide.eclipse.integration.feature.source" version="3.9.4.201804120850-RELEASE" url="features/org.springframework.ide.eclipse.integration.feature.source_3.9.4.201804120850-RELEASE/" plugin-identifier="org.springframework.ide.eclipse">
		</feature>
		<feature id="org.eclipse.equinox.p2.user.ui" version="2.3.2.v20171108-1343" url="features/org.eclipse.equinox.p2.user.ui_2.3.2.v20171108-1343/">
		</feature>
		<feature id="org.eclipse.jpt.jpa.feature" version="3.5.101.v201803161350" url="features/org.eclipse.jpt.jpa.feature_3.5.101.v201803161350/" plugin-identifier="org.eclipse.jpt.jpa.branding">
		</feature>
		<feature id="org.eclipse.jst.enterprise_ui.feature" version="3.9.3.v201803221418" url="features/org.eclipse.jst.enterprise_ui.feature_3.9.3.v201803221418/" plugin-identifier="org.eclipse.jst.jee.ui">
		</feature>
		<feature id="org.eclipse.jgit" version="4.9.2.201712150930-r" url="features/org.eclipse.jgit_4.9.2.201712150930-r/">
		</feature>
		<feature id="org.eclipse.datatools.sqldevtools.results.feature" version="1.14.1.201712071719" url="features/org.eclipse.datatools.sqldevtools.results.feature_1.14.1.201712071719/">
		</feature>
		<feature id="org.eclipse.emf.codegen.ui" version="2.8.0.v20170609-0928" url="features/org.eclipse.emf.codegen.ui_2.8.0.v20170609-0928/">
		</feature>
		<feature id="org.eclipse.emf.ecore.edit" version="2.9.0.v20170609-0928" url="features/org.eclipse.emf.ecore.edit_2.9.0.v20170609-0928/">
		</feature>
		<feature id="org.springframework.ide.eclipse.feature" version="3.9.4.201804120850-RELEASE" url="features/org.springframework.ide.eclipse.feature_3.9.4.201804120850-RELEASE/" plugin-identifier="org.springframework.ide.eclipse">
		</feature>
		<feature id="org.eclipse.emf.codegen.ecore" version="2.13.0.v20170609-0928" url="features/org.eclipse.emf.codegen.ecore_2.13.0.v20170609-0928/">
		</feature>
		<feature id="org.eclipse.emf.common" version="2.13.0.v20170609-0707" url="features/org.eclipse.emf.common_2.13.0.v20170609-0707/">
		</feature>
		<feature id="org.eclipse.mylyn.context_feature" version="3.23.0.v20170414-0629" url="features/org.eclipse.mylyn.context_feature_3.23.0.v20170414-0629/" plugin-identifier="org.eclipse.mylyn.context.core">
		</feature>
		<feature id="org.eclipse.wst.jsdt.chromium.debug.feature" version="0.6.0.v201705091354" url="features/org.eclipse.wst.jsdt.chromium.debug.feature_0.6.0.v201705091354/" plugin-identifier="org.eclipse.wst.jsdt.chromium.debug">
		</feature>
		<feature id="org.springframework.ide.eclipse.boot.feature.source" version="3.9.4.201804120850-RELEASE" url="features/org.springframework.ide.eclipse.boot.feature.source_3.9.4.201804120850-RELEASE/" plugin-identifier="org.springframework.ide.eclipse">
		</feature>
		<feature id="org.tigris.subversion.subclipse.graph.feature" version="1.1.1" url="features/org.tigris.subversion.subclipse.graph.feature_1.1.1/" plugin-identifier="org.tigris.subversion.subclipse.graph">
		</feature>
		<feature id="org.eclipse.egit" version="4.9.2.201712150930-r" url="features/org.eclipse.egit_4.9.2.201712150930-r/">
		</feature>
		<feature id="org.eclipse.jst.enterprise_core.feature" version="3.9.0.v201711022131" url="features/org.eclipse.jst.enterprise_core.feature_3.9.0.v201711022131/">
		</feature>
		<feature id="org.eclipse.datatools.enablement.oda.designer.feature" version="1.14.1.201712071719" url="features/org.eclipse.datatools.enablement.oda.designer.feature_1.14.1.201712071719/" plugin-identifier="org.eclipse.datatools.enablement.oda.xml.ui">
		</feature>
		<feature id="org.eclipse.wst.ws_core.feature" version="3.7.200.v201710302117" url="features/org.eclipse.wst.ws_core.feature_3.7.200.v201710302117/">
		</feature>
		<feature id="org.eclipse.jst.web_userdoc.feature" version="3.6.0.v201712131442" url="features/org.eclipse.jst.web_userdoc.feature_3.6.0.v201712131442/">
		</feature>
		<feature id="org.springframework.ide.eclipse.webflow.feature.source" version="3.9.4.201804120850-RELEASE" url="features/org.springframework.ide.eclipse.webflow.feature.source_3.9.4.201804120850-RELEASE/" plugin-identifier="org.springframework.ide.eclipse">
		</feature>
		<feature id="org.eclipse.datatools.sqldevtools.sqlbuilder.feature" version="1.14.1.201712071719" url="features/org.eclipse.datatools.sqldevtools.sqlbuilder.feature_1.14.1.201712071719/">
		</feature>
		<feature id="org.eclipse.jst.server_ui.feature" version="3.4.300.v201709251835" url="features/org.eclipse.jst.server_ui.feature_3.4.300.v201709251835/">
		</feature>
		<feature id="org.eclipse.datatools.sqldevtools.ddlgen.feature" version="1.14.1.201712071719" url="features/org.eclipse.datatools.sqldevtools.ddlgen.feature_1.14.1.201712071719/" plugin-identifier="org.eclipse.datatools.sqltools.ddlgen.ui">
		</feature>
		<feature id="org.eclipse.wst.server_core.feature" version="3.3.700.v201705172051" url="features/org.eclipse.wst.server_core.feature_3.3.700.v201705172051/">
		</feature>
		<feature id="org.eclipse.datatools.sqldevtools.parsers.feature" version="1.14.1.201712071719" url="features/org.eclipse.datatools.sqldevtools.parsers.feature_1.14.1.201712071719/" plugin-identifier="org.eclipse.datatools.sqltools.parsers.sql">
		</feature>
		<feature id="org.eclipse.emf.codegen.ecore.ui" version="2.13.0.v20170609-0928" url="features/org.eclipse.emf.codegen.ecore.ui_2.13.0.v20170609-0928/">
		</feature>
		<feature id="org.springframework.ide.eclipse.boot.feature" version="3.9.4.201804120850-RELEASE" url="features/org.springframework.ide.eclipse.boot.feature_3.9.4.201804120850-RELEASE/" plugin-identifier="org.springframework.ide.eclipse">
		</feature>
		<feature id="com.collabnet.subversion.merge.feature" version="3.0.13" url="features/com.collabnet.subversion.merge.feature_3.0.13/" plugin-identifier="com.collabnet.subversion.merge">
		</feature>
		<feature id="org.eclipse.tm.terminal.connector.local.feature" version="4.3.0.201706140544" url="features/org.eclipse.tm.terminal.connector.local.feature_4.3.0.201706140544/">
		</feature>
		<feature id="org.eclipse.rse.local" version="3.7.0.201704251225" url="features/org.eclipse.rse.local_3.7.0.201704251225/" plugin-identifier="org.eclipse.rse.services.local">
		</feature>
		<feature id="org.eclipse.m2e.wtp.jpa.feature" version="1.3.3.20170823-1905" url="features/org.eclipse.m2e.wtp.jpa.feature_1.3.3.20170823-1905/" plugin-identifier="org.eclipse.m2e.wtp.jpa">
		</feature>
		<feature id="org.eclipse.datatools.connectivity.oda.designer.core.feature" version="1.14.1.201712071719" url="features/org.eclipse.datatools.connectivity.oda.designer.core.feature_1.14.1.201712071719/" plugin-identifier="org.eclipse.datatools.connectivity.oda.design.ui">
		</feature>
		<feature id="org.springframework.ide.eclipse.data.feature" version="3.9.4.201804120850-RELEASE" url="features/org.springframework.ide.eclipse.data.feature_3.9.4.201804120850-RELEASE/" plugin-identifier="org.springframework.ide.eclipse">
		</feature>
		<feature id="org.eclipse.wst.server_userdoc.feature" version="3.3.300.v201405011426" url="features/org.eclipse.wst.server_userdoc.feature_3.3.300.v201405011426/">
		</feature>
		<feature id="org.eclipse.ecf.filetransfer.httpclient4.feature" version="3.13.8.v20170715-2257" url="features/org.eclipse.ecf.filetransfer.httpclient4.feature_3.13.8.v20170715-2257/">
		</feature>
		<feature id="org.springframework.ide.eclipse.batch.feature.source" version="3.9.4.201804120850-RELEASE" url="features/org.springframework.ide.eclipse.batch.feature.source_3.9.4.201804120850-RELEASE/" plugin-identifier="org.springframework.ide.eclipse">
		</feature>
		<feature id="org.eclipse.wst.xml_userdoc.feature" version="3.9.2.v201711071522" url="features/org.eclipse.wst.xml_userdoc.feature_3.9.2.v201711071522/">
		</feature>
		<feature id="org.eclipse.datatools.enablement.sqlite.feature" version="1.14.1.201712071719" url="features/org.eclipse.datatools.enablement.sqlite.feature_1.14.1.201712071719/" plugin-identifier="org.eclipse.datatools.enablement.finfo">
		</feature>
		<feature id="org.eclipse.emf.ecore.editor" version="2.13.0.v20170609-0928" url="features/org.eclipse.emf.ecore.editor_2.13.0.v20170609-0928/">
		</feature>
		<feature id="org.eclipse.datatools.connectivity.doc.user" version="1.14.1.201712071719" url="features/org.eclipse.datatools.connectivity.doc.user_1.14.1.201712071719/">
		</feature>
		<feature id="org.eclipse.jst.server_core.feature" version="3.4.300.v201606081655" url="features/org.eclipse.jst.server_core.feature_3.4.300.v201606081655/">
		</feature>
		<feature id="org.eclipse.jst.jsf.apache.trinidad.tagsupport.feature" version="2.6.1.v201802231403" url="features/org.eclipse.jst.jsf.apache.trinidad.tagsupport.feature_2.6.1.v201802231403/">
		</feature>
		<feature id="org.eclipse.rse.ssh" version="3.7.0.201704251225" url="features/org.eclipse.rse.ssh_3.7.0.201704251225/" plugin-identifier="org.eclipse.rse.services.ssh">
		</feature>
		<feature id="org.eclipse.contribution.xref" version="2.2.4.201803231521" url="features/org.eclipse.contribution.xref_2.2.4.201803231521/">
		</feature>
		<feature id="org.eclipse.mylyn.commons" version="3.23.0.v20170503-0014" url="features/org.eclipse.mylyn.commons_3.23.0.v20170503-0014/" plugin-identifier="org.eclipse.mylyn.commons.core">
		</feature>
		<feature id="org.eclipse.equinox.weaving.sdk" version="1.2.0.201803231521" url="features/org.eclipse.equinox.weaving.sdk_1.2.0.201803231521/">
		</feature>
		<feature id="org.eclipse.datatools.intro" version="1.14.1.201712071719" url="features/org.eclipse.datatools.intro_1.14.1.201712071719/">
		</feature>
		<feature id="org.eclipse.epp.package.jee.feature" version="4.7.3.20180405-1200" url="features/org.eclipse.epp.package.jee.feature_4.7.3.20180405-1200/" plugin-identifier="org.eclipse.epp.package.jee">
		</feature>
		<feature id="org.springframework.ide.eclipse.autowire.feature.source" version="3.9.4.201804120850-RELEASE" url="features/org.springframework.ide.eclipse.autowire.feature.source_3.9.4.201804120850-RELEASE/" plugin-identifier="org.springframework.ide.eclipse">
		</feature>
		<feature id="org.eclipse.mylyn.commons.notifications" version="1.15.0.v20170411-1844" url="features/org.eclipse.mylyn.commons.notifications_1.15.0.v20170411-1844/" plugin-identifier="org.eclipse.mylyn.commons.core">
		</feature>
		<feature id="org.eclipse.mylyn.tasks.ide" version="3.23.1.v20170623-0008" url="features/org.eclipse.mylyn.tasks.ide_3.23.1.v20170623-0008/" plugin-identifier="org.eclipse.mylyn.tasks.core">
		</feature>
		<feature id="org.eclipse.wst.server_adapters.feature" version="3.2.601.v201711302104" url="features/org.eclipse.wst.server_adapters.feature_3.2.601.v201711302104/">
		</feature>
		<feature id="org.eclipse.emf.edit" version="2.12.0.v20170609-0928" url="features/org.eclipse.emf.edit_2.12.0.v20170609-0928/">
		</feature>
		<feature id="org.springframework.ide.eclipse.boot.dash.feature.source" version="3.9.4.201804120850-RELEASE" url="features/org.springframework.ide.eclipse.boot.dash.feature.source_3.9.4.201804120850-RELEASE/" plugin-identifier="org.springframework.ide.eclipse">
		</feature>
		<feature id="org.eclipse.emf.databinding.edit" version="1.4.0.v20170609-0928" url="features/org.eclipse.emf.databinding.edit_1.4.0.v20170609-0928/">
		</feature>
		<feature id="org.springframework.ide.eclipse.maven.feature" version="3.9.4.201804120850-RELEASE" url="features/org.springframework.ide.eclipse.maven.feature_3.9.4.201804120850-RELEASE/" plugin-identifier="org.springframework.ide.eclipse">
		</feature>
		<feature id="org.eclipse.tm.terminal.view.feature" version="4.3.0.201706140544" url="features/org.eclipse.tm.terminal.view.feature_4.3.0.201706140544/" plugin-identifier="org.eclipse.tm.terminal.view.core">
		</feature>
		<feature id="org.eclipse.equinox.p2.discovery.feature" version="1.1.1.v20170906-1259" url="features/org.eclipse.equinox.p2.discovery.feature_1.1.1.v20170906-1259/">
		</feature>
		<feature id="org.eclipse.emf.edit.ui" version="2.13.0.v20170609-0928" url="features/org.eclipse.emf.edit.ui_2.13.0.v20170609-0928/">
		</feature>
		<feature id="org.eclipse.mylyn.commons.repositories" version="1.15.0.v20170411-1844" url="features/org.eclipse.mylyn.commons.repositories_1.15.0.v20170411-1844/" plugin-identifier="org.eclipse.mylyn.commons.core">
		</feature>
		<feature id="org.eclipse.mylyn.wikitext.editors_feature" version="3.0.19.201711172000" url="features/org.eclipse.mylyn.wikitext.editors_feature_3.0.19.201711172000/" plugin-identifier="org.eclipse.mylyn.wikitext">
		</feature>
		<feature id="org.eclipse.tm.terminal.connector.telnet.feature" version="4.3.0.201706140544" url="features/org.eclipse.tm.terminal.connector.telnet.feature_4.3.0.201706140544/">
		</feature>
		<feature id="org.eclipse.mylyn.commons.identity" version="1.15.0.v20170411-1844" url="features/org.eclipse.mylyn.commons.identity_1.15.0.v20170411-1844/" plugin-identifier="org.eclipse.mylyn.commons.core">
		</feature>
		<feature id="org.eclipse.datatools.sqldevtools.schemaobjecteditor.feature" version="1.14.1.201712071719" url="features/org.eclipse.datatools.sqldevtools.schemaobjecteditor.feature_1.14.1.201712071719/">
		</feature>
		<feature id="org.eclipse.tm.terminal.control.feature" version="4.3.0.201706140544" url="features/org.eclipse.tm.terminal.control.feature_4.3.0.201706140544/" plugin-identifier="org.eclipse.tm.terminal.control">
		</feature>
		<feature id="org.springframework.ide.eclipse.roo.feature" version="3.9.4.201804120850-RELEASE" url="features/org.springframework.ide.eclipse.roo.feature_3.9.4.201804120850-RELEASE/" plugin-identifier="org.springframework.ide.eclipse">
		</feature>
		<feature id="org.eclipse.ecf.filetransfer.httpclient4.ssl.feature" version="1.1.0.v20170110-1317" url="features/org.eclipse.ecf.filetransfer.httpclient4.ssl.feature_1.1.0.v20170110-1317/">
		</feature>
		<feature id="org.eclipse.jpt.jpa.eclipselink.feature" version="3.4.101.v201803161350" url="features/org.eclipse.jpt.jpa.eclipselink.feature_3.4.101.v201803161350/" plugin-identifier="org.eclipse.jpt.jpa.eclipselink.branding">
		</feature>
		<feature id="org.eclipse.datatools.enablement.jdbc.feature" version="1.14.1.201712071719" url="features/org.eclipse.datatools.enablement.jdbc.feature_1.14.1.201712071719/" plugin-identifier="org.eclipse.datatools.enablement.sybase">
		</feature>
		<feature id="org.eclipse.wst.jsdt.nodejs.feature" version="1.1.0.v201803202007" url="features/org.eclipse.wst.jsdt.nodejs.feature_1.1.0.v201803202007/" plugin-identifier="org.eclipse.wst.jsdt.nodejs">
		</feature>
		<feature id="org.eclipse.datatools.connectivity.oda.designer.feature" version="1.14.1.201712071719" url="features/org.eclipse.datatools.connectivity.oda.designer.feature_1.14.1.201712071719/" plugin-identifier="org.eclipse.datatools.connectivity.oda.design.ui">
		</feature>
		<feature id="org.eclipse.datatools.enablement.ibm.feature" version="1.14.1.201712071719" url="features/org.eclipse.datatools.enablement.ibm.feature_1.14.1.201712071719/" plugin-identifier="org.eclipse.datatools.enablement.ibm">
		</feature>
		<feature id="org.springframework.ide.eclipse.ajdt.feature" version="3.9.4.201804120850-RELEASE" url="features/org.springframework.ide.eclipse.ajdt.feature_3.9.4.201804120850-RELEASE/" plugin-identifier="org.springframework.ide.eclipse">
		</feature>
		<feature id="org.eclipse.wst.xsl.feature" version="1.3.401.v201509231858" url="features/org.eclipse.wst.xsl.feature_1.3.401.v201509231858/" plugin-identifier="org.eclipse.wst.xsl">
		</feature>
		<feature id="org.springframework.ide.eclipse.autowire.feature" version="3.9.4.201804120850-RELEASE" url="features/org.springframework.ide.eclipse.autowire.feature_3.9.4.201804120850-RELEASE/" plugin-identifier="org.springframework.ide.eclipse">
		</feature>
		<feature id="org.eclipse.datatools.modelbase.feature" version="1.14.1.201712071719" url="features/org.eclipse.datatools.modelbase.feature_1.14.1.201712071719/" plugin-identifier="org.eclipse.datatools.modelbase.sql">
		</feature>
		<feature id="org.eclipse.oomph.setup" version="1.8.0.v20170531-0903" url="features/org.eclipse.oomph.setup_1.8.0.v20170531-0903/">
		</feature>
		<feature id="org.eclipse.recommenders.rcp.feature" version="2.5.2.v20180401-1226" url="features/org.eclipse.recommenders.rcp.feature_2.5.2.v20180401-1226/" plugin-identifier="org.eclipse.recommenders.rcp">
		</feature>
		<feature id="org.eclipse.datatools.enablement.apache.derby.feature" version="1.14.1.201712071719" url="features/org.eclipse.datatools.enablement.apache.derby.feature_1.14.1.201712071719/" plugin-identifier="org.eclipse.datatools.enablement.sybase">
		</feature>
		<feature id="org.eclipse.rse.useractions" version="3.7.0.201704251225" url="features/org.eclipse.rse.useractions_3.7.0.201704251225/">
		</feature>
		<feature id="org.eclipse.jst.enterprise_userdoc.feature" version="3.6.0.v201612121628" url="features/org.eclipse.jst.enterprise_userdoc.feature_3.6.0.v201612121628/">
		</feature>
		<feature id="org.eclipse.rcp" version="4.7.3.v20180330-0640" url="features/org.eclipse.rcp_4.7.3.v20180330-0640/">
		</feature>
		<feature id="org.springframework.ide.eclipse.security.feature" version="3.9.4.201804120850-RELEASE" url="features/org.springframework.ide.eclipse.security.feature_3.9.4.201804120850-RELEASE/" plugin-identifier="org.springframework.ide.eclipse">
		</feature>
		<feature id="org.eclipse.m2e.wtp.jsf.feature" version="1.3.3.20170823-1905" url="features/org.eclipse.m2e.wtp.jsf.feature_1.3.3.20170823-1905/" plugin-identifier="org.eclipse.m2e.wtp.jsf">
		</feature>
		<feature id="org.eclipse.m2e.wtp.feature" version="1.3.3.20170823-1905" url="features/org.eclipse.m2e.wtp.feature_1.3.3.20170823-1905/" plugin-identifier="org.eclipse.m2e.wtp">
		</feature>
		<feature id="org.eclipse.mylyn.wikitext_feature" version="3.0.19.201711172000" url="features/org.eclipse.mylyn.wikitext_feature_3.0.19.201711172000/" plugin-identifier="org.eclipse.mylyn.wikitext">
		</feature>
		<feature id="org.eclipse.wst.ws_ui.feature" version="3.8.1.v201803221834" url="features/org.eclipse.wst.ws_ui.feature_3.8.1.v201803221834/">
		</feature>
		<feature id="org.springframework.ide.eclipse.feature.source" version="3.9.4.201804120850-RELEASE" url="features/org.springframework.ide.eclipse.feature.source_3.9.4.201804120850-RELEASE/" plugin-identifier="org.springframework.ide.eclipse">
		</feature>
		<feature id="org.eclipse.ecf.core.ssl.feature" version="1.1.0.v20170110-1317" url="features/org.eclipse.ecf.core.ssl.feature_1.1.0.v20170110-1317/">
		</feature>
		<feature id="org.eclipse.help" version="2.2.104.v20180330-0640" url="features/org.eclipse.help_2.2.104.v20180330-0640/" plugin-identifier="org.eclipse.help.base">
		</feature>
		<feature id="org.eclipse.datatools.common.doc.user" version="1.14.1.201712071719" url="features/org.eclipse.datatools.common.doc.user_1.14.1.201712071719/">
		</feature>
		<feature id="org.eclipse.rse" version="3.7.3.201704251225" url="features/org.eclipse.rse_3.7.3.201704251225/">
		</feature>
		<feature id="org.eclipse.oomph.p2" version="1.8.0.v20170410-0909" url="features/org.eclipse.oomph.p2_1.8.0.v20170410-0909/">
		</feature>
		<feature id="org.eclipse.jpt.dbws.eclipselink.feature" version="1.2.201.v201803161350" url="features/org.eclipse.jpt.dbws.eclipselink.feature_1.2.201.v201803161350/" plugin-identifier="org.eclipse.jpt.dbws.eclipselink.branding">
		</feature>
		<feature id="org.eclipse.datatools.enablement.hsqldb.feature" version="1.14.1.201712071719" url="features/org.eclipse.datatools.enablement.hsqldb.feature_1.14.1.201712071719/" plugin-identifier="org.eclipse.datatools.enablement.finfo">
		</feature>
		<feature id="org.eclipse.mylyn_feature" version="3.23.1.v20170623-0008" url="features/org.eclipse.mylyn_feature_3.23.1.v20170623-0008/" plugin-identifier="org.eclipse.mylyn.tasks.core">
		</feature>
		<feature id="org.eclipse.wst.json_core.feature" version="1.1.2.v201711071522" url="features/org.eclipse.wst.json_core.feature_1.1.2.v201711071522/">
		</feature>
		<feature id="org.eclipse.datatools.sqltools.doc.user" version="1.14.1.201712071719" url="features/org.eclipse.datatools.sqltools.doc.user_1.14.1.201712071719/">
		</feature>
		<feature id="org.tigris.subversion.subclipse" version="1.8.22" url="features/org.tigris.subversion.subclipse_1.8.22/" plugin-identifier="org.tigris.subversion.subclipse.core">
		</feature>
		<feature id="org.eclipse.jsf.feature" version="3.10.0.v201803271721" url="features/org.eclipse.jsf.feature_3.10.0.v201803271721/">
		</feature>
		<feature id="net.java.dev.jna" version="3.4.0.t20120117_1605" url="features/net.java.dev.jna_3.4.0.t20120117_1605/">
		</feature>
		<feature id="org.eclipse.wst.jsdt.feature" version="2.1.1.v201803202007" url="features/org.eclipse.wst.jsdt.feature_2.1.1.v201803202007/" plugin-identifier="org.eclipse.wst.jsdt.ui">
		</feature>
		<feature id="org.springframework.ide.eclipse.osgi.feature.source" version="3.9.4.201804120850-RELEASE" url="features/org.springframework.ide.eclipse.osgi.feature.source_3.9.4.201804120850-RELEASE/" plugin-identifier="org.springframework.ide.eclipse">
		</feature>
		<feature id="org.springframework.ide.eclipse.integration.feature" version="3.9.4.201804120850-RELEASE" url="features/org.springframework.ide.eclipse.integration.feature_3.9.4.201804120850-RELEASE/" plugin-identifier="org.springframework.ide.eclipse">
		</feature>
		<feature id="org.eclipse.cft.server.ui.feature" version="1.0.10.v201709130027" url="features/org.eclipse.cft.server.ui.feature_1.0.10.v201709130027/" plugin-identifier="org.eclipse.cft.server.branding.ui">
		</feature>
		<feature id="org.eclipse.wst.xml_ui.feature" version="3.9.2.v201803221834" url="features/org.eclipse.wst.xml_ui.feature_3.9.2.v201803221834/" plugin-identifier="org.eclipse.wst.xml.ui">
		</feature>
		<feature id="org.springframework.ide.eclipse.osgi.feature" version="3.9.4.201804120850-RELEASE" url="features/org.springframework.ide.eclipse.osgi.feature_3.9.4.201804120850-RELEASE/" plugin-identifier="org.springframework.ide.eclipse">
		</feature>
		<feature id="org.eclipse.datatools.enablement.oda.feature" version="1.14.1.201712071719" url="features/org.eclipse.datatools.enablement.oda.feature_1.14.1.201712071719/" plugin-identifier="org.eclipse.datatools.enablement.oda.xml">
		</feature>
		<feature id="org.eclipse.e4.rcp" version="1.6.3.v20180329-0507" url="features/org.eclipse.e4.rcp_1.6.3.v20180329-0507/">
		</feature>
		<feature id="org.eclipse.datatools.connectivity.feature" version="1.14.1.201712071719" url="features/org.eclipse.datatools.connectivity.feature_1.14.1.201712071719/" plugin-identifier="org.eclipse.datatools.connectivity">
		</feature>
		<feature id="org.eclipse.datatools.enablement.sap.feature" version="1.14.1.201712071719" url="features/org.eclipse.datatools.enablement.sap.feature_1.14.1.201712071719/" plugin-identifier="org.eclipse.datatools.enablement.finfo">
		</feature>
		<feature id="org.springframework.ide.eclipse.data.feature.source" version="3.9.4.201804120850-RELEASE" url="features/org.springframework.ide.eclipse.data.feature.source_3.9.4.201804120850-RELEASE/" plugin-identifier="org.springframework.ide.eclipse">
		</feature>
		<feature id="org.eclipse.gef" version="3.11.0.201606061308" url="features/org.eclipse.gef_3.11.0.201606061308/">
		</feature>
		<feature id="org.eclipse.tm.terminal.feature" version="4.3.0.201706140544" url="features/org.eclipse.tm.terminal.feature_4.3.0.201706140544/" plugin-identifier="org.eclipse.tm.terminal.view.core">
		</feature>
		<feature id="org.eclipse.wst.common_core.feature" version="3.9.1.v201803221834" url="features/org.eclipse.wst.common_core.feature_3.9.1.v201803221834/">
		</feature>
		<feature id="org.eclipse.wst.common_ui.feature" version="3.9.1.v201803221834" url="features/org.eclipse.wst.common_ui.feature_3.9.1.v201803221834/">
		</feature>
		<feature id="org.eclipse.platform" version="4.7.3.v20180330-0640" url="features/org.eclipse.platform_4.7.3.v20180330-0640/">
		</feature>
		<feature id="org.eclipse.emf.mapping.ecore" version="2.9.0.v20170609-0928" url="features/org.eclipse.emf.mapping.ecore_2.9.0.v20170609-0928/">
		</feature>
		<feature id="org.eclipse.epp.package.common.feature" version="4.7.3.20180405-1200" url="features/org.eclipse.epp.package.common.feature_4.7.3.20180405-1200/">
		</feature>
		<feature id="org.eclipse.emf.common.ui" version="2.12.0.v20170609-0928" url="features/org.eclipse.emf.common.ui_2.12.0.v20170609-0928/">
		</feature>
		<feature id="org.springframework.ide.eclipse.mylyn.feature.source" version="3.9.4.201804120850-RELEASE" url="features/org.springframework.ide.eclipse.mylyn.feature.source_3.9.4.201804120850-RELEASE/" plugin-identifier="org.springframework.ide.eclipse">
		</feature>
		<feature id="org.eclipse.mylyn.monitor" version="3.23.0.v20170411-1844" url="features/org.eclipse.mylyn.monitor_3.23.0.v20170411-1844/" plugin-identifier="org.eclipse.mylyn.commons.core">
		</feature>
		<feature id="org.eclipse.rse.dstore" version="3.7.0.201704251225" url="features/org.eclipse.rse.dstore_3.7.0.201704251225/" plugin-identifier="org.eclipse.rse.services.dstore">
		</feature>
		<feature id="org.springframework.ide.eclipse.cft.feature.source" version="3.9.4.201804120850-RELEASE" url="features/org.springframework.ide.eclipse.cft.feature.source_3.9.4.201804120850-RELEASE/" plugin-identifier="org.springframework.ide.eclipse">
		</feature>
		<feature id="org.eclipse.jst.ws.jaxws.dom.feature" version="1.0.302.v201504272154" url="features/org.eclipse.jst.ws.jaxws.dom.feature_1.0.302.v201504272154/">
		</feature>
		<feature id="org.eclipse.emf.converter" version="2.12.0.v20170609-0928" url="features/org.eclipse.emf.converter_2.12.0.v20170609-0928/">
		</feature>
		<feature id="org.eclipse.datatools.sqldevtools.data.feature" version="1.14.1.201712071719" url="features/org.eclipse.datatools.sqldevtools.data.feature_1.14.1.201712071719/">
		</feature>
		<feature id="org.eclipse.contribution.weaving" version="2.2.4.201803231521" url="features/org.eclipse.contribution.weaving_2.2.4.201803231521/">
		</feature>
		<feature id="org.eclipse.mylyn.discovery" version="3.23.0.v20170411-1844" url="features/org.eclipse.mylyn.discovery_3.23.0.v20170411-1844/" plugin-identifier="org.eclipse.mylyn.commons.core">
		</feature>
		<feature id="org.springframework.ide.eclipse.ajdt.feature.source" version="3.9.4.201804120850-RELEASE" url="features/org.springframework.ide.eclipse.ajdt.feature.source_3.9.4.201804120850-RELEASE/" plugin-identifier="org.springframework.ide.eclipse">
		</feature>
		<feature id="org.eclipse.datatools.connectivity.oda.feature" version="1.14.1.201712071719" url="features/org.eclipse.datatools.connectivity.oda.feature_1.14.1.201712071719/" plugin-identifier="org.eclipse.datatools.connectivity.oda">
		</feature>
		<feature id="org.springframework.ide.eclipse.aeri.feature.source" version="3.9.4.201804120850-RELEASE" url="features/org.springframework.ide.eclipse.aeri.feature.source_3.9.4.201804120850-RELEASE/" plugin-identifier="org.springframework.ide.eclipse">
		</feature>
		<feature id="org.springframework.ide.eclipse.aop.feature" version="3.9.4.201804120850-RELEASE" url="features/org.springframework.ide.eclipse.aop.feature_3.9.4.201804120850-RELEASE/" plugin-identifier="org.springframework.ide.eclipse">
		</feature>
		<feature id="org.aspectj" version="1.8.13.201803231521" url="features/org.aspectj_1.8.13.201803231521/" plugin-identifier="org.eclipse.aspectj">
		</feature>
		<feature id="org.springframework.ide.eclipse.aop.feature.source" version="3.9.4.201804120850-RELEASE" url="features/org.springframework.ide.eclipse.aop.feature.source_3.9.4.201804120850-RELEASE/" plugin-identifier="org.springframework.ide.eclipse">
		</feature>
		<feature id="org.eclipse.mylyn.bugzilla_feature" version="3.23.1.v20170623-0008" url="features/org.eclipse.mylyn.bugzilla_feature_3.23.1.v20170623-0008/" plugin-identifier="org.eclipse.mylyn.tasks.core">
		</feature>
		<feature id="org.eclipse.wst.web_ui.feature" version="3.9.2.v201803221834" url="features/org.eclipse.wst.web_ui.feature_3.9.2.v201803221834/" plugin-identifier="org.eclipse.wst.web.ui">
		</feature>
		<feature id="org.eclipse.mylyn.team_feature" version="3.23.0.v20170411-2108" url="features/org.eclipse.mylyn.team_feature_3.23.0.v20170411-2108/" plugin-identifier="org.eclipse.mylyn.context.core">
		</feature>
		<feature id="org.tigris.subversion.subclipse.mylyn" version="3.0.0" url="features/org.tigris.subversion.subclipse.mylyn_3.0.0/">
		</feature>
		<feature id="org.eclipse.equinox.p2.rcp.feature" version="1.3.2.v20171108-1343" url="features/org.eclipse.equinox.p2.rcp.feature_1.3.2.v20171108-1343/">
		</feature>
		<feature id="org.eclipse.mylyn.java_feature" version="3.23.0.v20170411-2108" url="features/org.eclipse.mylyn.java_feature_3.23.0.v20170411-2108/" plugin-identifier="org.eclipse.mylyn.context.core">
		</feature>
		<feature id="org.eclipse.wst.server_ui.feature" version="3.3.700.v201711152154" url="features/org.eclipse.wst.server_ui.feature_3.3.700.v201711152154/">
		</feature>
		<feature id="org.tigris.subversion.clientadapter.feature" version="1.8.6" url="features/org.tigris.subversion.clientadapter.feature_1.8.6/" plugin-identifier="org.tigris.subversion.clientadapter">
		</feature>
		<feature id="org.eclipse.ecf.filetransfer.feature" version="3.13.8.v20170715-2257" url="features/org.eclipse.ecf.filetransfer.feature_3.13.8.v20170715-2257/">
		</feature>
		<feature id="org.eclipse.jdt" version="3.13.4.v20180330-0919" url="features/org.eclipse.jdt_3.13.4.v20180330-0919/">
		</feature>
		<feature id="org.eclipse.epp.logging.aeri.feature" version="2.0.7.v20170906-1327" url="features/org.eclipse.epp.logging.aeri.feature_2.0.7.v20170906-1327/">
		</feature>
		<feature id="org.eclipse.wst.web_userdoc.feature" version="3.9.2.v201710252304" url="features/org.eclipse.wst.web_userdoc.feature_3.9.2.v201710252304/">
		</feature>
		<feature id="org.eclipse.emf.mapping.ui" version="2.9.0.v20170609-0928" url="features/org.eclipse.emf.mapping.ui_2.9.0.v20170609-0928/">
		</feature>
		<feature id="org.springframework.ide.eclipse.aeri.feature" version="3.9.4.201804120850-RELEASE" url="features/org.springframework.ide.eclipse.aeri.feature_3.9.4.201804120850-RELEASE/" plugin-identifier="org.springframework.ide.eclipse">
		</feature>
		<feature id="org.eclipse.emf.codegen" version="2.12.0.v20170609-0928" url="features/org.eclipse.emf.codegen_2.12.0.v20170609-0928/">
		</feature>
		<feature id="org.eclipse.userstorage" version="1.1.0.v20170526-1605" url="features/org.eclipse.userstorage_1.1.0.v20170526-1605/">
		</feature>
		<feature id="org.tmatesoft.svnkit" version="1.7.9.r9659_v20130411_2103" url="features/org.tmatesoft.svnkit_1.7.9.r9659_v20130411_2103/">
		</feature>
		<feature id="org.tigris.subversion.clientadapter.svnkit.feature" version="*******" url="features/org.tigris.subversion.clientadapter.svnkit.feature_*******/" plugin-identifier="org.tigris.subversion.clientadapter.svnkit">
		</feature>
		<feature id="org.eclipse.wst.web_core.feature" version="3.9.2.v201803221834" url="features/org.eclipse.wst.web_core.feature_3.9.2.v201803221834/">
		</feature>
		<feature id="org.eclipse.emf.mapping" version="2.9.0.v20170609-0928" url="features/org.eclipse.emf.mapping_2.9.0.v20170609-0928/">
		</feature>
		<feature id="org.eclipse.egit.mylyn" version="4.9.2.201712150930-r" url="features/org.eclipse.egit.mylyn_4.9.2.201712150930-r/" plugin-identifier="org.eclipse.egit">
		</feature>
		<feature id="org.eclipse.tm.terminal.connector.ssh.feature" version="4.3.0.201706140544" url="features/org.eclipse.tm.terminal.connector.ssh.feature_4.3.0.201706140544/" plugin-identifier="org.eclipse.tm.terminal.ssh">
		</feature>
		<feature id="org.eclipse.datatools.enablement.sybase.feature" version="1.14.1.201712071719" url="features/org.eclipse.datatools.enablement.sybase.feature_1.14.1.201712071719/" plugin-identifier="org.eclipse.datatools.enablement.sybase">
		</feature>
		<feature id="org.eclipse.wst.json_ui.feature" version="1.1.2.v201711071522" url="features/org.eclipse.wst.json_ui.feature_1.1.2.v201711071522/" plugin-identifier="org.eclipse.wst.json.ui">
		</feature>
		<feature id="org.eclipse.jst.webpageeditor.feature" version="2.9.0.v201803271721" url="features/org.eclipse.jst.webpageeditor.feature_2.9.0.v201803271721/">
		</feature>
		<feature id="org.springframework.ide.eclipse.boot.dash.feature" version="3.9.4.201804120850-RELEASE" url="features/org.springframework.ide.eclipse.boot.dash.feature_3.9.4.201804120850-RELEASE/" plugin-identifier="org.springframework.ide.eclipse">
		</feature>
		<feature id="org.eclipse.jpt.common.feature" version="1.5.100.v201803012210" url="features/org.eclipse.jpt.common.feature_1.5.100.v201803012210/" plugin-identifier="org.eclipse.jpt.common.branding">
		</feature>
		<feature id="org.eclipse.rse.ftp" version="3.7.1.201704251225" url="features/org.eclipse.rse.ftp_3.7.1.201704251225/" plugin-identifier="org.eclipse.rse.services.files.ftp">
		</feature>
		<feature id="org.eclipse.wst.xml.xpath2.processor.feature" version="2.0.301.v201409111854" url="features/org.eclipse.wst.xml.xpath2.processor.feature_2.0.301.v201409111854/" plugin-identifier="org.eclipse.wst.xml.xpath2">
		</feature>
		<feature id="org.eclipse.eclemma.feature" version="3.1.0.201804041601" url="features/org.eclipse.eclemma.feature_3.1.0.201804041601/" plugin-identifier="org.eclipse.eclemma.ui">
		</feature>
		<feature id="org.eclipse.datatools.enablement.oracle.feature" version="1.14.1.201712071719" url="features/org.eclipse.datatools.enablement.oracle.feature_1.14.1.201712071719/" plugin-identifier="org.eclipse.datatools.enablement.finfo">
		</feature>
		<feature id="org.eclipse.buildship" version="2.2.1.v20180125-1441" url="features/org.eclipse.buildship_2.2.1.v20180125-1441/" plugin-identifier="org.eclipse.buildship.branding">
		</feature>
		<feature id="org.eclipse.jst.ws.cxf.feature" version="1.1.300.v201701262158" url="features/org.eclipse.jst.ws.cxf.feature_1.1.300.v201701262158/" plugin-identifier="org.eclipse.jst.ws.cxf.core">
		</feature>
		<feature id="org.eclipse.jst.web_ui.feature" version="3.9.0.v201803221834" url="features/org.eclipse.jst.web_ui.feature_3.9.0.v201803221834/">
		</feature>
		<feature id="org.eclipse.recommenders.mylyn.rcp.feature" version="2.5.2.v20180401-1226" url="features/org.eclipse.recommenders.mylyn.rcp.feature_2.5.2.v20180401-1226/" plugin-identifier="org.eclipse.recommenders.mylyn.rcp">
		</feature>
		<feature id="org.eclipse.datatools.enablement.msft.feature" version="1.14.1.201712071719" url="features/org.eclipse.datatools.enablement.msft.feature_1.14.1.201712071719/" plugin-identifier="org.eclipse.datatools.enablement.finfo">
		</feature>
		<feature id="org.eclipse.wst.ws_userdoc.feature" version="3.1.400.v201405061938" url="features/org.eclipse.wst.ws_userdoc.feature_3.1.400.v201405061938/">
		</feature>
		<feature id="org.eclipse.rse.telnet" version="3.7.0.201704251225" url="features/org.eclipse.rse.telnet_3.7.0.201704251225/" plugin-identifier="org.eclipse.rse.services.telnet">
		</feature>
		<feature id="org.eclipse.mylyn.ide_feature" version="3.23.0.v20170411-2108" url="features/org.eclipse.mylyn.ide_feature_3.23.0.v20170411-2108/" plugin-identifier="org.eclipse.mylyn.context.core">
		</feature>
		<feature id="org.eclipse.datatools.enablement.mysql.feature" version="1.14.1.201712071719" url="features/org.eclipse.datatools.enablement.mysql.feature_1.14.1.201712071719/" plugin-identifier="org.eclipse.datatools.enablement.finfo">
		</feature>
		<feature id="org.eclipse.datatools.enablement.ingres.feature" version="1.14.1.201712071719" url="features/org.eclipse.datatools.enablement.ingres.feature_1.14.1.201712071719/" plugin-identifier="org.eclipse.datatools.enablement.finfo">
		</feature>
		<feature id="org.eclipse.jst.server_adapters.ext.feature" version="3.3.601.v201711301708" url="features/org.eclipse.jst.server_adapters.ext.feature_3.3.601.v201711301708/">
		</feature>
		<feature id="org.eclipse.jst.web_core.feature" version="3.9.1.v201802152012" url="features/org.eclipse.jst.web_core.feature_3.9.1.v201802152012/">
		</feature>
		<feature id="org.springframework.ide.eclipse.roo.feature.source" version="3.9.4.201804120850-RELEASE" url="features/org.springframework.ide.eclipse.roo.feature.source_3.9.4.201804120850-RELEASE/" plugin-identifier="org.springframework.ide.eclipse">
		</feature>
		<feature id="org.eclipse.ecf.core.feature" version="1.4.0.v20170516-2248" url="features/org.eclipse.ecf.core.feature_1.4.0.v20170516-2248/">
		</feature>
		<feature id="org.eclipse.datatools.enablement.feature" version="1.14.1.201712071719" url="features/org.eclipse.datatools.enablement.feature_1.14.1.201712071719/" plugin-identifier="org.eclipse.datatools.enablement.finfo">
		</feature>
		<feature id="org.eclipse.epp.mpc" version="1.6.4.v20180214-1810" url="features/org.eclipse.epp.mpc_1.6.4.v20180214-1810/" plugin-identifier="org.eclipse.epp.mpc.ui">
		</feature>
		<feature id="org.springframework.ide.eclipse.security.feature.source" version="3.9.4.201804120850-RELEASE" url="features/org.springframework.ide.eclipse.security.feature.source_3.9.4.201804120850-RELEASE/">
		</feature>
		<feature id="org.eclipse.m2e.wtp.jaxrs.feature" version="1.3.3.20170823-1905" url="features/org.eclipse.m2e.wtp.jaxrs.feature_1.3.3.20170823-1905/" plugin-identifier="org.eclipse.m2e.wtp.jaxrs">
		</feature>
		<feature id="org.eclipse.jst.server_userdoc.feature" version="3.3.300.v201503102136" url="features/org.eclipse.jst.server_userdoc.feature_3.3.300.v201503102136/">
		</feature>
		<feature id="org.eclipse.wst.ws_wsdl15.feature" version="1.5.400.v201405061938" url="features/org.eclipse.wst.ws_wsdl15.feature_1.5.400.v201405061938/">
		</feature>
		<feature id="org.eclipse.emf.databinding" version="1.4.0.v20170609-0928" url="features/org.eclipse.emf.databinding_1.4.0.v20170609-0928/">
		</feature>
		<feature id="org.eclipse.equinox.p2.core.feature" version="1.4.1.v20170928-1405" url="features/org.eclipse.equinox.p2.core.feature_1.4.1.v20170928-1405/">
		</feature>
		<feature id="org.eclipse.jst.ws.jaxws.feature" version="1.2.300.v201701262158" url="features/org.eclipse.jst.ws.jaxws.feature_1.2.300.v201701262158/" plugin-identifier="org.eclipse.jst.ws.jaxws.core">
		</feature>
		<feature id="org.eclipse.emf.ecore" version="2.13.0.v20170609-0707" url="features/org.eclipse.emf.ecore_2.13.0.v20170609-0707/">
		</feature>
		<feature id="org.eclipse.datatools.sqldevtools.feature" version="1.14.1.201712071719" url="features/org.eclipse.datatools.sqldevtools.feature_1.14.1.201712071719/" plugin-identifier="org.eclipse.datatools.sqltools.common.ui">
		</feature>
		<feature id="org.eclipse.emf.mapping.ecore.editor" version="2.10.0.v20170609-0928" url="features/org.eclipse.emf.mapping.ecore.editor_2.10.0.v20170609-0928/">
		</feature>
		<feature id="org.eclipse.equinox.p2.extras.feature" version="1.3.1.v20170928-1405" url="features/org.eclipse.equinox.p2.extras.feature_1.3.1.v20170928-1405/">
		</feature>
		<feature id="org.springframework.ide.eclipse.batch.feature" version="3.9.4.201804120850-RELEASE" url="features/org.springframework.ide.eclipse.batch.feature_3.9.4.201804120850-RELEASE/" plugin-identifier="org.springframework.ide.eclipse">
		</feature>
		<feature id="org.eclipse.datatools.doc.user" version="1.14.1.201712071719" url="features/org.eclipse.datatools.doc.user_1.14.1.201712071719/">
		</feature>
		<feature id="org.eclipse.wst.common.fproj" version="3.7.1.v201707201954" url="features/org.eclipse.wst.common.fproj_3.7.1.v201707201954/" plugin-identifier="org.eclipse.wst.common.project.facet.core">
		</feature>
		<feature id="org.eclipse.cft.server.core.feature" version="1.2.3.v201709130027" url="features/org.eclipse.cft.server.core.feature_1.2.3.v201709130027/" plugin-identifier="org.eclipse.cft.server.branding.core">
		</feature>
		<feature id="org.tigris.subversion.clientadapter.javahl.feature" version="1.7.10" url="features/org.tigris.subversion.clientadapter.javahl.feature_1.7.10/" plugin-identifier="org.tigris.subversion.clientadapter.javahl">
		</feature>
		<feature id="org.eclipse.jst.common.fproj.enablement.jdt" version="3.10.0.v201803211504" url="features/org.eclipse.jst.common.fproj.enablement.jdt_3.10.0.v201803211504/" plugin-identifier="org.eclipse.jst.common.project.facet.core">
		</feature>
		<feature id="org.springframework.ide.eclipse.mylyn.feature" version="3.9.4.201804120850-RELEASE" url="features/org.springframework.ide.eclipse.mylyn.feature_3.9.4.201804120850-RELEASE/" plugin-identifier="org.springframework.ide.eclipse">
		</feature>
		<feature id="org.eclipse.jst.ws.jaxws_userdoc.feature" version="1.0.402.v201503151903" url="features/org.eclipse.jst.ws.jaxws_userdoc.feature_1.0.402.v201503151903/">
		</feature>
		<feature id="org.sonatype.m2e.mavenarchiver.feature" version="0.17.2.201606141937-signed-20160830073346" url="features/org.sonatype.m2e.mavenarchiver.feature_0.17.2.201606141937-signed-20160830073346/" plugin-identifier="org.sonatype.m2e.mavenarchiver">
		</feature>
		<feature id="org.eclipse.ajdt" version="2.2.4.201803231521" url="features/org.eclipse.ajdt_2.2.4.201803231521/" plugin-identifier="org.eclipse.aspectj">
		</feature>
		<feature id="org.eclipse.emf" version="2.13.0.v20170609-0928" url="features/org.eclipse.emf_2.13.0.v20170609-0928/">
		</feature>
		<feature id="org.eclipse.ecf.filetransfer.ssl.feature" version="1.1.0.v20170110-1317" url="features/org.eclipse.ecf.filetransfer.ssl.feature_1.1.0.v20170110-1317/">
		</feature>
	</site>
</config>
