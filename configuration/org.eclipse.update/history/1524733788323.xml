<?xml version="1.0" encoding="UTF-8"?>
<config date="1524733788323" transient="false" version="3.0">
<site enabled="true" policy="USER-EXCLUDE" updateable="true" url="platform:/base/">
<feature id="org.eclipse.ecf.filetransfer.httpclient4.feature" url="features/org.eclipse.ecf.filetransfer.httpclient4.feature_3.13.8.v20170715-2257/" version="3.13.8.v20170715-2257">
</feature>
<feature id="org.eclipse.help" plugin-identifier="org.eclipse.help.base" url="features/org.eclipse.help_2.2.104.v20180330-0640/" version="2.2.104.v20180330-0640">
</feature>
<feature id="org.springframework.ide.eclipse.data.feature.source" plugin-identifier="org.springframework.ide.eclipse" url="features/org.springframework.ide.eclipse.data.feature.source_3.9.4.201804120850-RELEASE/" version="3.9.4.201804120850-RELEASE">
</feature>
<feature id="org.eclipse.contribution.weaving" url="features/org.eclipse.contribution.weaving_2.2.4.201803231521/" version="2.2.4.201803231521">
</feature>
<feature id="org.tigris.subversion.subclipse" plugin-identifier="org.tigris.subversion.subclipse.core" url="features/org.tigris.subversion.subclipse_1.8.22/" version="1.8.22">
</feature>
<feature id="org.springframework.ide.eclipse.ajdt.feature.source" plugin-identifier="org.springframework.ide.eclipse" url="features/org.springframework.ide.eclipse.ajdt.feature.source_3.9.4.201804120850-RELEASE/" version="3.9.4.201804120850-RELEASE">
</feature>
<feature id="org.eclipse.mylyn.bugzilla_feature" plugin-identifier="org.eclipse.mylyn.tasks.core" url="features/org.eclipse.mylyn.bugzilla_feature_3.23.1.v20170623-0008/" version="3.23.1.v20170623-0008">
</feature>
<feature id="org.eclipse.egit" url="features/org.eclipse.egit_4.9.2.201712150930-r/" version="4.9.2.201712150930-r">
</feature>
<feature id="org.eclipse.jpt.dbws.eclipselink.feature" plugin-identifier="org.eclipse.jpt.dbws.eclipselink.branding" url="features/org.eclipse.jpt.dbws.eclipselink.feature_1.2.201.v201803161350/" version="1.2.201.v201803161350">
</feature>
<feature id="org.eclipse.mylyn_feature" plugin-identifier="org.eclipse.mylyn.tasks.core" url="features/org.eclipse.mylyn_feature_3.23.1.v20170623-0008/" version="3.23.1.v20170623-0008">
</feature>
<feature id="org.eclipse.wst.common_core.feature" url="features/org.eclipse.wst.common_core.feature_3.9.1.v201803221834/" version="3.9.1.v201803221834">
</feature>
<feature id="org.eclipse.wst.ws_wsdl15.feature" url="features/org.eclipse.wst.ws_wsdl15.feature_1.5.400.v201405061938/" version="1.5.400.v201405061938">
</feature>
<feature id="org.springframework.ide.eclipse.mylyn.feature.source" plugin-identifier="org.springframework.ide.eclipse" url="features/org.springframework.ide.eclipse.mylyn.feature.source_3.9.4.201804120850-RELEASE/" version="3.9.4.201804120850-RELEASE">
</feature>
<feature id="org.eclipse.ecf.core.ssl.feature" url="features/org.eclipse.ecf.core.ssl.feature_1.1.0.v20170110-1317/" version="1.1.0.v20170110-1317">
</feature>
<feature id="org.eclipse.gef" url="features/org.eclipse.gef_3.11.0.201606061308/" version="3.11.0.201606061308">
</feature>
<feature id="org.eclipse.mylyn.commons.identity" plugin-identifier="org.eclipse.mylyn.commons.core" url="features/org.eclipse.mylyn.commons.identity_1.15.0.v20170411-1844/" version="1.15.0.v20170411-1844">
</feature>
<feature id="org.eclipse.jpt.jpa.feature" plugin-identifier="org.eclipse.jpt.jpa.branding" url="features/org.eclipse.jpt.jpa.feature_3.5.101.v201803161350/" version="3.5.101.v201803161350">
</feature>
<feature id="com.collabnet.subversion.merge.feature" plugin-identifier="com.collabnet.subversion.merge" url="features/com.collabnet.subversion.merge.feature_3.0.13/" version="3.0.13">
</feature>
<feature id="org.eclipse.oomph.p2" url="features/org.eclipse.oomph.p2_1.8.0.v20170410-0909/" version="1.8.0.v20170410-0909">
</feature>
<feature id="org.eclipse.tm.terminal.view.feature" plugin-identifier="org.eclipse.tm.terminal.view.core" url="features/org.eclipse.tm.terminal.view.feature_4.3.0.201706140544/" version="4.3.0.201706140544">
</feature>
<feature id="org.eclipse.emf.codegen.ui" url="features/org.eclipse.emf.codegen.ui_2.8.0.v20170609-0928/" version="2.8.0.v20170609-0928">
</feature>
<feature id="org.eclipse.datatools.enablement.sqlite.feature" plugin-identifier="org.eclipse.datatools.enablement.finfo" url="features/org.eclipse.datatools.enablement.sqlite.feature_1.14.1.201712071719/" version="1.14.1.201712071719">
</feature>
<feature id="org.eclipse.datatools.sqldevtools.ddl.feature" url="features/org.eclipse.datatools.sqldevtools.ddl.feature_1.14.1.201712071719/" version="1.14.1.201712071719">
</feature>
<feature id="org.springframework.ide.eclipse.integration.feature" plugin-identifier="org.springframework.ide.eclipse" url="features/org.springframework.ide.eclipse.integration.feature_3.9.4.201804120850-RELEASE/" version="3.9.4.201804120850-RELEASE">
</feature>
<feature id="org.eclipse.mylyn.commons.notifications" plugin-identifier="org.eclipse.mylyn.commons.core" url="features/org.eclipse.mylyn.commons.notifications_1.15.0.v20170411-1844/" version="1.15.0.v20170411-1844">
</feature>
<feature id="org.eclipse.emf.converter" url="features/org.eclipse.emf.converter_2.12.0.v20170609-0928/" version="2.12.0.v20170609-0928">
</feature>
<feature id="org.eclipse.jst.ws.jaxws_userdoc.feature" url="features/org.eclipse.jst.ws.jaxws_userdoc.feature_1.0.402.v201503151903/" version="1.0.402.v201503151903">
</feature>
<feature id="org.eclipse.recommenders.rcp.feature" plugin-identifier="org.eclipse.recommenders.rcp" url="features/org.eclipse.recommenders.rcp.feature_2.5.2.v20180401-1226/" version="2.5.2.v20180401-1226">
</feature>
<feature id="org.eclipse.ecf.filetransfer.feature" url="features/org.eclipse.ecf.filetransfer.feature_3.13.8.v20170715-2257/" version="3.13.8.v20170715-2257">
</feature>
<feature id="org.eclipse.datatools.enablement.postgresql.feature" plugin-identifier="org.eclipse.datatools.enablement.finfo" url="features/org.eclipse.datatools.enablement.postgresql.feature_1.14.1.201712071719/" version="1.14.1.201712071719">
</feature>
<feature id="org.eclipse.datatools.enablement.oda.designer.feature" plugin-identifier="org.eclipse.datatools.enablement.oda.xml.ui" url="features/org.eclipse.datatools.enablement.oda.designer.feature_1.14.1.201712071719/" version="1.14.1.201712071719">
</feature>
<feature id="org.eclipse.wst.web_core.feature" url="features/org.eclipse.wst.web_core.feature_3.9.2.v201803221834/" version="3.9.2.v201803221834">
</feature>
<feature id="org.springframework.ide.eclipse.autowire.feature.source" plugin-identifier="org.springframework.ide.eclipse" url="features/org.springframework.ide.eclipse.autowire.feature.source_3.9.4.201804120850-RELEASE/" version="3.9.4.201804120850-RELEASE">
</feature>
<feature id="org.eclipse.datatools.connectivity.oda.designer.core.feature" plugin-identifier="org.eclipse.datatools.connectivity.oda.design.ui" url="features/org.eclipse.datatools.connectivity.oda.designer.core.feature_1.14.1.201712071719/" version="1.14.1.201712071719">
</feature>
<feature id="org.eclipse.m2e.wtp.jsf.feature" plugin-identifier="org.eclipse.m2e.wtp.jsf" url="features/org.eclipse.m2e.wtp.jsf.feature_1.3.3.20170823-1905/" version="1.3.3.20170823-1905">
</feature>
<feature id="org.eclipse.datatools.enablement.jdbc.feature" plugin-identifier="org.eclipse.datatools.enablement.sybase" url="features/org.eclipse.datatools.enablement.jdbc.feature_1.14.1.201712071719/" version="1.14.1.201712071719">
</feature>
<feature id="org.eclipse.emf.mapping" url="features/org.eclipse.emf.mapping_2.9.0.v20170609-0928/" version="2.9.0.v20170609-0928">
</feature>
<feature id="org.springframework.ide.eclipse.mylyn.feature" plugin-identifier="org.springframework.ide.eclipse" url="features/org.springframework.ide.eclipse.mylyn.feature_3.9.4.201804120850-RELEASE/" version="3.9.4.201804120850-RELEASE">
</feature>
<feature id="org.eclipse.equinox.p2.user.ui" url="features/org.eclipse.equinox.p2.user.ui_2.3.2.v20171108-1343/" version="2.3.2.v20171108-1343">
</feature>
<feature id="org.eclipse.emf.ecore" url="features/org.eclipse.emf.ecore_2.13.0.v20170609-0707/" version="2.13.0.v20170609-0707">
</feature>
<feature id="org.eclipse.wst.common.fproj" plugin-identifier="org.eclipse.wst.common.project.facet.core" url="features/org.eclipse.wst.common.fproj_3.7.1.v201707201954/" version="3.7.1.v201707201954">
</feature>
<feature id="org.eclipse.datatools.intro" url="features/org.eclipse.datatools.intro_1.14.1.201712071719/" version="1.14.1.201712071719">
</feature>
<feature id="org.eclipse.jst.common.fproj.enablement.jdt" plugin-identifier="org.eclipse.jst.common.project.facet.core" url="features/org.eclipse.jst.common.fproj.enablement.jdt_3.10.0.v201803211504/" version="3.10.0.v201803211504">
</feature>
<feature id="org.eclipse.jgit" url="features/org.eclipse.jgit_4.9.2.201712150930-r/" version="4.9.2.201712150930-r">
</feature>
<feature id="org.eclipse.datatools.sqldevtools.results.feature" url="features/org.eclipse.datatools.sqldevtools.results.feature_1.14.1.201712071719/" version="1.14.1.201712071719">
</feature>
<feature id="org.eclipse.mylyn.wikitext_feature" plugin-identifier="org.eclipse.mylyn.wikitext" url="features/org.eclipse.mylyn.wikitext_feature_3.0.19.201711172000/" version="3.0.19.201711172000">
</feature>
<feature id="org.eclipse.jpt.jaxb.feature" plugin-identifier="org.eclipse.jpt.jaxb.branding" url="features/org.eclipse.jpt.jaxb.feature_1.5.100.v201803012210/" version="1.5.100.v201803012210">
</feature>
<feature id="org.eclipse.emf.mapping.ecore.editor" url="features/org.eclipse.emf.mapping.ecore.editor_2.10.0.v20170609-0928/" version="2.10.0.v20170609-0928">
</feature>
<feature id="org.eclipse.datatools.enablement.feature" plugin-identifier="org.eclipse.datatools.enablement.finfo" url="features/org.eclipse.datatools.enablement.feature_1.14.1.201712071719/" version="1.14.1.201712071719">
</feature>
<feature id="org.springframework.ide.eclipse.feature.source" plugin-identifier="org.springframework.ide.eclipse" url="features/org.springframework.ide.eclipse.feature.source_3.9.4.201804120850-RELEASE/" version="3.9.4.201804120850-RELEASE">
</feature>
<feature id="org.eclipse.datatools.enablement.jdt.feature" plugin-identifier="org.eclipse.datatools.enablement.jdt.classpath" url="features/org.eclipse.datatools.enablement.jdt.feature_1.14.1.201712071719/" version="1.14.1.201712071719">
</feature>
<feature id="org.eclipse.mylyn.discovery" plugin-identifier="org.eclipse.mylyn.commons.core" url="features/org.eclipse.mylyn.discovery_3.23.0.v20170411-1844/" version="3.23.0.v20170411-1844">
</feature>
<feature id="org.eclipse.jst.server_adapters.ext.feature" url="features/org.eclipse.jst.server_adapters.ext.feature_3.3.601.v201711301708/" version="3.3.601.v201711301708">
</feature>
<feature id="org.eclipse.datatools.enablement.mysql.feature" plugin-identifier="org.eclipse.datatools.enablement.finfo" url="features/org.eclipse.datatools.enablement.mysql.feature_1.14.1.201712071719/" version="1.14.1.201712071719">
</feature>
<feature id="org.eclipse.datatools.enablement.msft.feature" plugin-identifier="org.eclipse.datatools.enablement.finfo" url="features/org.eclipse.datatools.enablement.msft.feature_1.14.1.201712071719/" version="1.14.1.201712071719">
</feature>
<feature id="org.eclipse.wst.jsdt.chromium.debug.feature" plugin-identifier="org.eclipse.wst.jsdt.chromium.debug" url="features/org.eclipse.wst.jsdt.chromium.debug.feature_0.6.0.v201705091354/" version="0.6.0.v201705091354">
</feature>
<feature id="org.eclipse.m2e.wtp.jpa.feature" plugin-identifier="org.eclipse.m2e.wtp.jpa" url="features/org.eclipse.m2e.wtp.jpa.feature_1.3.3.20170823-1905/" version="1.3.3.20170823-1905">
</feature>
<feature id="org.eclipse.equinox.p2.core.feature" url="features/org.eclipse.equinox.p2.core.feature_1.4.1.v20170928-1405/" version="1.4.1.v20170928-1405">
</feature>
<feature id="org.springframework.ide.eclipse.boot.feature.source" plugin-identifier="org.springframework.ide.eclipse" url="features/org.springframework.ide.eclipse.boot.feature.source_3.9.4.201804120850-RELEASE/" version="3.9.4.201804120850-RELEASE">
</feature>
<feature id="org.eclipse.ecf.filetransfer.ssl.feature" url="features/org.eclipse.ecf.filetransfer.ssl.feature_1.1.0.v20170110-1317/" version="1.1.0.v20170110-1317">
</feature>
<feature id="org.eclipse.jst.ws.jaxws.feature" plugin-identifier="org.eclipse.jst.ws.jaxws.core" url="features/org.eclipse.jst.ws.jaxws.feature_1.2.300.v201701262158/" version="1.2.300.v201701262158">
</feature>
<feature id="org.eclipse.datatools.connectivity.doc.user" url="features/org.eclipse.datatools.connectivity.doc.user_1.14.1.201712071719/" version="1.14.1.201712071719">
</feature>
<feature id="org.eclipse.datatools.sqldevtools.sqlbuilder.feature" url="features/org.eclipse.datatools.sqldevtools.sqlbuilder.feature_1.14.1.201712071719/" version="1.14.1.201712071719">
</feature>
<feature id="org.eclipse.mylyn.tasks.ide" plugin-identifier="org.eclipse.mylyn.tasks.core" url="features/org.eclipse.mylyn.tasks.ide_3.23.1.v20170623-0008/" version="3.23.1.v20170623-0008">
</feature>
<feature id="org.eclipse.userstorage" url="features/org.eclipse.userstorage_1.1.0.v20170526-1605/" version="1.1.0.v20170526-1605">
</feature>
<feature id="org.tigris.subversion.clientadapter.svnkit.feature" plugin-identifier="org.tigris.subversion.clientadapter.svnkit" url="features/org.tigris.subversion.clientadapter.svnkit.feature_*******/" version="*******">
</feature>
<feature id="org.eclipse.emf.edit" url="features/org.eclipse.emf.edit_2.12.0.v20170609-0928/" version="2.12.0.v20170609-0928">
</feature>
<feature id="org.eclipse.mylyn.commons" plugin-identifier="org.eclipse.mylyn.commons.core" url="features/org.eclipse.mylyn.commons_3.23.0.v20170503-0014/" version="3.23.0.v20170503-0014">
</feature>
<feature id="org.springframework.ide.eclipse.maven.feature" plugin-identifier="org.springframework.ide.eclipse" url="features/org.springframework.ide.eclipse.maven.feature_3.9.4.201804120850-RELEASE/" version="3.9.4.201804120850-RELEASE">
</feature>
<feature id="org.springframework.ide.eclipse.security.feature" plugin-identifier="org.springframework.ide.eclipse" url="features/org.springframework.ide.eclipse.security.feature_3.9.4.201804120850-RELEASE/" version="3.9.4.201804120850-RELEASE">
</feature>
<feature id="org.springframework.ide.eclipse.osgi.feature" plugin-identifier="org.springframework.ide.eclipse" url="features/org.springframework.ide.eclipse.osgi.feature_3.9.4.201804120850-RELEASE/" version="3.9.4.201804120850-RELEASE">
</feature>
<feature id="org.eclipse.jst.server_userdoc.feature" url="features/org.eclipse.jst.server_userdoc.feature_3.3.300.v201503102136/" version="3.3.300.v201503102136">
</feature>
<feature id="org.eclipse.mylyn.ide_feature" plugin-identifier="org.eclipse.mylyn.context.core" url="features/org.eclipse.mylyn.ide_feature_3.23.0.v20170411-2108/" version="3.23.0.v20170411-2108">
</feature>
<feature id="org.eclipse.wst.json_ui.feature" plugin-identifier="org.eclipse.wst.json.ui" url="features/org.eclipse.wst.json_ui.feature_1.1.2.v201711071522/" version="1.1.2.v201711071522">
</feature>
<feature id="org.eclipse.emf.databinding.edit" url="features/org.eclipse.emf.databinding.edit_1.4.0.v20170609-0928/" version="1.4.0.v20170609-0928">
</feature>
<feature id="org.eclipse.emf.codegen.ecore" url="features/org.eclipse.emf.codegen.ecore_2.13.0.v20170609-0928/" version="2.13.0.v20170609-0928">
</feature>
<feature id="org.eclipse.m2e.feature" plugin-identifier="org.eclipse.m2e.core" url="features/org.eclipse.m2e.feature_1.8.3.20180227-2137/" version="1.8.3.20180227-2137">
</feature>
<feature id="org.springframework.ide.eclipse.aop.feature" plugin-identifier="org.springframework.ide.eclipse" url="features/org.springframework.ide.eclipse.aop.feature_3.9.4.201804120850-RELEASE/" version="3.9.4.201804120850-RELEASE">
</feature>
<feature id="net.java.dev.jna" url="features/net.java.dev.jna_3.4.0.t20120117_1605/" version="3.4.0.t20120117_1605">
</feature>
<feature id="org.eclipse.wst.server_core.feature" url="features/org.eclipse.wst.server_core.feature_3.3.700.v201705172051/" version="3.3.700.v201705172051">
</feature>
<feature id="org.eclipse.datatools.connectivity.oda.feature" plugin-identifier="org.eclipse.datatools.connectivity.oda" url="features/org.eclipse.datatools.connectivity.oda.feature_1.14.1.201712071719/" version="1.14.1.201712071719">
</feature>
<feature id="org.eclipse.cft.server.core.feature" plugin-identifier="org.eclipse.cft.server.branding.core" url="features/org.eclipse.cft.server.core.feature_1.2.3.v201709130027/" version="1.2.3.v201709130027">
</feature>
<feature id="org.eclipse.wst.server_adapters.feature" url="features/org.eclipse.wst.server_adapters.feature_3.2.601.v201711302104/" version="3.2.601.v201711302104">
</feature>
<feature id="org.springframework.ide.eclipse.aeri.feature.source" plugin-identifier="org.springframework.ide.eclipse" url="features/org.springframework.ide.eclipse.aeri.feature.source_3.9.4.201804120850-RELEASE/" version="3.9.4.201804120850-RELEASE">
</feature>
<feature id="org.eclipse.wst.common_ui.feature" url="features/org.eclipse.wst.common_ui.feature_3.9.1.v201803221834/" version="3.9.1.v201803221834">
</feature>
<feature id="org.eclipse.datatools.sqldevtools.data.feature" url="features/org.eclipse.datatools.sqldevtools.data.feature_1.14.1.201712071719/" version="1.14.1.201712071719">
</feature>
<feature id="org.eclipse.mylyn.team_feature" plugin-identifier="org.eclipse.mylyn.context.core" url="features/org.eclipse.mylyn.team_feature_3.23.0.v20170411-2108/" version="3.23.0.v20170411-2108">
</feature>
<feature id="org.eclipse.wst.jsdt.nodejs.feature" plugin-identifier="org.eclipse.wst.jsdt.nodejs" url="features/org.eclipse.wst.jsdt.nodejs.feature_1.1.0.v201803202007/" version="1.1.0.v201803202007">
</feature>
<feature id="org.eclipse.emf.common.ui" url="features/org.eclipse.emf.common.ui_2.12.0.v20170609-0928/" version="2.12.0.v20170609-0928">
</feature>
<feature id="org.tigris.subversion.subclipse.mylyn" url="features/org.tigris.subversion.subclipse.mylyn_3.0.0/" version="3.0.0">
</feature>
<feature id="org.eclipse.emf.mapping.ui" url="features/org.eclipse.emf.mapping.ui_2.9.0.v20170609-0928/" version="2.9.0.v20170609-0928">
</feature>
<feature id="org.eclipse.rse.telnet" plugin-identifier="org.eclipse.rse.services.telnet" url="features/org.eclipse.rse.telnet_3.7.0.201704251225/" version="3.7.0.201704251225">
</feature>
<feature id="org.eclipse.wst.web_userdoc.feature" url="features/org.eclipse.wst.web_userdoc.feature_3.9.2.v201710252304/" version="3.9.2.v201710252304">
</feature>
<feature id="org.springframework.ide.eclipse.osgi.feature.source" plugin-identifier="org.springframework.ide.eclipse" url="features/org.springframework.ide.eclipse.osgi.feature.source_3.9.4.201804120850-RELEASE/" version="3.9.4.201804120850-RELEASE">
</feature>
<feature id="org.eclipse.datatools.connectivity.oda.designer.feature" plugin-identifier="org.eclipse.datatools.connectivity.oda.design.ui" url="features/org.eclipse.datatools.connectivity.oda.designer.feature_1.14.1.201712071719/" version="1.14.1.201712071719">
</feature>
<feature id="org.eclipse.datatools.enablement.ibm.feature" plugin-identifier="org.eclipse.datatools.enablement.ibm" url="features/org.eclipse.datatools.enablement.ibm.feature_1.14.1.201712071719/" version="1.14.1.201712071719">
</feature>
<feature id="org.springframework.ide.eclipse.cft.feature.source" plugin-identifier="org.springframework.ide.eclipse" url="features/org.springframework.ide.eclipse.cft.feature.source_3.9.4.201804120850-RELEASE/" version="3.9.4.201804120850-RELEASE">
</feature>
<feature id="org.eclipse.wst.ws_ui.feature" url="features/org.eclipse.wst.ws_ui.feature_3.8.1.v201803221834/" version="3.8.1.v201803221834">
</feature>
<feature id="org.eclipse.ecf.core.feature" url="features/org.eclipse.ecf.core.feature_1.4.0.v20170516-2248/" version="1.4.0.v20170516-2248">
</feature>
<feature id="org.eclipse.wst.server_ui.feature" url="features/org.eclipse.wst.server_ui.feature_3.3.700.v201711152154/" version="3.3.700.v201711152154">
</feature>
<feature id="org.eclipse.rse.core" url="features/org.eclipse.rse.core_3.7.3.201704251225/" version="3.7.3.201704251225">
</feature>
<feature id="org.eclipse.jpt.common.eclipselink.feature" plugin-identifier="org.eclipse.jpt.common.eclipselink.branding" url="features/org.eclipse.jpt.common.eclipselink.feature_1.3.200.v201803012210/" version="1.3.200.v201803012210">
</feature>
<feature id="org.eclipse.datatools.sqldevtools.schemaobjecteditor.feature" url="features/org.eclipse.datatools.sqldevtools.schemaobjecteditor.feature_1.14.1.201712071719/" version="1.14.1.201712071719">
</feature>
<feature id="org.tigris.subversion.subclipse.graph.feature" plugin-identifier="org.tigris.subversion.subclipse.graph" url="features/org.tigris.subversion.subclipse.graph.feature_1.1.1/" version="1.1.1">
</feature>
<feature id="org.eclipse.datatools.connectivity.feature" plugin-identifier="org.eclipse.datatools.connectivity" url="features/org.eclipse.datatools.connectivity.feature_1.14.1.201712071719/" version="1.14.1.201712071719">
</feature>
<feature id="org.eclipse.wst.jsdt.feature" plugin-identifier="org.eclipse.wst.jsdt.ui" url="features/org.eclipse.wst.jsdt.feature_2.1.1.v201803202007/" version="2.1.1.v201803202007">
</feature>
<feature id="org.springframework.ide.eclipse.security.feature.source" url="features/org.springframework.ide.eclipse.security.feature.source_3.9.4.201804120850-RELEASE/" version="3.9.4.201804120850-RELEASE">
</feature>
<feature id="org.springframework.ide.eclipse.ajdt.feature" plugin-identifier="org.springframework.ide.eclipse" url="features/org.springframework.ide.eclipse.ajdt.feature_3.9.4.201804120850-RELEASE/" version="3.9.4.201804120850-RELEASE">
</feature>
<feature id="org.eclipse.tm.terminal.connector.ssh.feature" plugin-identifier="org.eclipse.tm.terminal.ssh" url="features/org.eclipse.tm.terminal.connector.ssh.feature_4.3.0.201706140544/" version="4.3.0.201706140544">
</feature>
<feature id="org.tigris.subversion.clientadapter.feature" plugin-identifier="org.tigris.subversion.clientadapter" url="features/org.tigris.subversion.clientadapter.feature_1.8.6/" version="1.8.6">
</feature>
<feature id="org.eclipse.jsf.feature" url="features/org.eclipse.jsf.feature_3.10.0.v201803271721/" version="3.10.0.v201803271721">
</feature>
<feature id="org.eclipse.wst.xml_core.feature" url="features/org.eclipse.wst.xml_core.feature_3.9.2.v201711080222/" version="3.9.2.v201711080222">
</feature>
<feature id="org.eclipse.buildship" plugin-identifier="org.eclipse.buildship.branding" url="features/org.eclipse.buildship_2.2.1.v20180125-1441/" version="2.2.1.v20180125-1441">
</feature>
<feature id="org.eclipse.datatools.common.doc.user" url="features/org.eclipse.datatools.common.doc.user_1.14.1.201712071719/" version="1.14.1.201712071719">
</feature>
<feature id="org.eclipse.wst.web_ui.feature" plugin-identifier="org.eclipse.wst.web.ui" url="features/org.eclipse.wst.web_ui.feature_3.9.2.v201803221834/" version="3.9.2.v201803221834">
</feature>
<feature id="org.eclipse.rse" url="features/org.eclipse.rse_3.7.3.201704251225/" version="3.7.3.201704251225">
</feature>
<feature id="org.eclipse.tm.terminal.connector.telnet.feature" url="features/org.eclipse.tm.terminal.connector.telnet.feature_4.3.0.201706140544/" version="4.3.0.201706140544">
</feature>
<feature id="org.eclipse.rse.local" plugin-identifier="org.eclipse.rse.services.local" url="features/org.eclipse.rse.local_3.7.0.201704251225/" version="3.7.0.201704251225">
</feature>
<feature id="org.eclipse.datatools.enablement.hsqldb.feature" plugin-identifier="org.eclipse.datatools.enablement.finfo" url="features/org.eclipse.datatools.enablement.hsqldb.feature_1.14.1.201712071719/" version="1.14.1.201712071719">
</feature>
<feature id="org.eclipse.datatools.sqltools.doc.user" url="features/org.eclipse.datatools.sqltools.doc.user_1.14.1.201712071719/" version="1.14.1.201712071719">
</feature>
<feature id="org.tigris.subversion.clientadapter.javahl.feature" plugin-identifier="org.tigris.subversion.clientadapter.javahl" url="features/org.tigris.subversion.clientadapter.javahl.feature_1.7.10/" version="1.7.10">
</feature>
<feature id="org.eclipse.m2e.wtp.feature" plugin-identifier="org.eclipse.m2e.wtp" url="features/org.eclipse.m2e.wtp.feature_1.3.3.20170823-1905/" version="1.3.3.20170823-1905">
</feature>
<feature id="org.eclipse.jst.enterprise_userdoc.feature" url="features/org.eclipse.jst.enterprise_userdoc.feature_3.6.0.v201612121628/" version="3.6.0.v201612121628">
</feature>
<feature id="org.eclipse.jpt.common.feature" plugin-identifier="org.eclipse.jpt.common.branding" url="features/org.eclipse.jpt.common.feature_1.5.100.v201803012210/" version="1.5.100.v201803012210">
</feature>
<feature id="org.eclipse.emf.edit.ui" url="features/org.eclipse.emf.edit.ui_2.13.0.v20170609-0928/" version="2.13.0.v20170609-0928">
</feature>
<feature id="org.eclipse.rcp" url="features/org.eclipse.rcp_4.7.3.v20180330-0640/" version="4.7.3.v20180330-0640">
</feature>
<feature id="org.springframework.ide.eclipse.batch.feature.source" plugin-identifier="org.springframework.ide.eclipse" url="features/org.springframework.ide.eclipse.batch.feature.source_3.9.4.201804120850-RELEASE/" version="3.9.4.201804120850-RELEASE">
</feature>
<feature id="org.eclipse.jpt.jaxb.eclipselink.feature" plugin-identifier="org.eclipse.jpt.jaxb.eclipselink.branding" url="features/org.eclipse.jpt.jaxb.eclipselink.feature_1.4.201.v201803161350/" version="1.4.201.v201803161350">
</feature>
<feature id="org.eclipse.wst.xml_userdoc.feature" url="features/org.eclipse.wst.xml_userdoc.feature_3.9.2.v201711071522/" version="3.9.2.v201711071522">
</feature>
<feature id="org.eclipse.datatools.enablement.apache.derby.feature" plugin-identifier="org.eclipse.datatools.enablement.sybase" url="features/org.eclipse.datatools.enablement.apache.derby.feature_1.14.1.201712071719/" version="1.14.1.201712071719">
</feature>
<feature id="org.eclipse.jst.server_ui.feature" url="features/org.eclipse.jst.server_ui.feature_3.4.300.v201709251835/" version="3.4.300.v201709251835">
</feature>
<feature id="org.springframework.ide.eclipse.aeri.feature" plugin-identifier="org.springframework.ide.eclipse" url="features/org.springframework.ide.eclipse.aeri.feature_3.9.4.201804120850-RELEASE/" version="3.9.4.201804120850-RELEASE">
</feature>
<feature id="org.eclipse.jdt" url="features/org.eclipse.jdt_3.13.4.v20180330-0919/" version="3.13.4.v20180330-0919">
</feature>
<feature id="org.eclipse.emf.common" url="features/org.eclipse.emf.common_2.13.0.v20170609-0707/" version="2.13.0.v20170609-0707">
</feature>
<feature id="org.eclipse.egit.mylyn" plugin-identifier="org.eclipse.egit" url="features/org.eclipse.egit.mylyn_4.9.2.201712150930-r/" version="4.9.2.201712150930-r">
</feature>
<feature id="org.springframework.ide.eclipse.feature" plugin-identifier="org.springframework.ide.eclipse" url="features/org.springframework.ide.eclipse.feature_3.9.4.201804120850-RELEASE/" version="3.9.4.201804120850-RELEASE">
</feature>
<feature id="org.eclipse.datatools.enablement.sybase.feature" plugin-identifier="org.eclipse.datatools.enablement.sybase" url="features/org.eclipse.datatools.enablement.sybase.feature_1.14.1.201712071719/" version="1.14.1.201712071719">
</feature>
<feature id="org.eclipse.jst.ws.cxf.feature" plugin-identifier="org.eclipse.jst.ws.cxf.core" url="features/org.eclipse.jst.ws.cxf.feature_1.1.300.v201701262158/" version="1.1.300.v201701262158">
</feature>
<feature id="org.springframework.ide.eclipse.webflow.feature" plugin-identifier="org.springframework.ide.eclipse" url="features/org.springframework.ide.eclipse.webflow.feature_3.9.4.201804120850-RELEASE/" version="3.9.4.201804120850-RELEASE">
</feature>
<feature id="org.eclipse.datatools.sqldevtools.feature" plugin-identifier="org.eclipse.datatools.sqltools.common.ui" url="features/org.eclipse.datatools.sqldevtools.feature_1.14.1.201712071719/" version="1.14.1.201712071719">
</feature>
<feature id="org.eclipse.contribution.xref" url="features/org.eclipse.contribution.xref_2.2.4.201803231521/" version="2.2.4.201803231521">
</feature>
<feature id="org.eclipse.datatools.modelbase.feature" plugin-identifier="org.eclipse.datatools.modelbase.sql" url="features/org.eclipse.datatools.modelbase.feature_1.14.1.201712071719/" version="1.14.1.201712071719">
</feature>
<feature id="org.eclipse.oomph.setup" url="features/org.eclipse.oomph.setup_1.8.0.v20170531-0903/" version="1.8.0.v20170531-0903">
</feature>
<feature id="org.eclipse.jst.web_ui.feature" url="features/org.eclipse.jst.web_ui.feature_3.9.0.v201803221834/" version="3.9.0.v201803221834">
</feature>
<feature id="org.eclipse.wst.xsl.feature" plugin-identifier="org.eclipse.wst.xsl" url="features/org.eclipse.wst.xsl.feature_1.3.401.v201509231858/" version="1.3.401.v201509231858">
</feature>
<feature id="org.eclipse.rse.ssh" plugin-identifier="org.eclipse.rse.services.ssh" url="features/org.eclipse.rse.ssh_3.7.0.201704251225/" version="3.7.0.201704251225">
</feature>
<feature id="org.eclipse.jst.enterprise_ui.feature" plugin-identifier="org.eclipse.jst.jee.ui" url="features/org.eclipse.jst.enterprise_ui.feature_3.9.3.v201803221418/" version="3.9.3.v201803221418">
</feature>
<feature id="org.springframework.ide.eclipse.autowire.feature" plugin-identifier="org.springframework.ide.eclipse" url="features/org.springframework.ide.eclipse.autowire.feature_3.9.4.201804120850-RELEASE/" version="3.9.4.201804120850-RELEASE">
</feature>
<feature id="org.eclipse.datatools.enablement.oda.feature" plugin-identifier="org.eclipse.datatools.enablement.oda.xml" url="features/org.eclipse.datatools.enablement.oda.feature_1.14.1.201712071719/" version="1.14.1.201712071719">
</feature>
<feature id="org.eclipse.jst.ws.jaxws.dom.feature" url="features/org.eclipse.jst.ws.jaxws.dom.feature_1.0.302.v201504272154/" version="1.0.302.v201504272154">
</feature>
<feature id="org.eclipse.jst.ws.axis2tools.feature" plugin-identifier="org.eclipse.jst.ws.axis2.ui" url="features/org.eclipse.jst.ws.axis2tools.feature_1.1.301.v201410160332/" version="1.1.301.v201410160332">
</feature>
<feature id="org.eclipse.recommenders.mylyn.rcp.feature" plugin-identifier="org.eclipse.recommenders.mylyn.rcp" url="features/org.eclipse.recommenders.mylyn.rcp.feature_2.5.2.v20180401-1226/" version="2.5.2.v20180401-1226">
</feature>
<feature id="org.eclipse.platform" url="features/org.eclipse.platform_4.7.3.v20180330-0640/" version="4.7.3.v20180330-0640">
</feature>
<feature id="org.springframework.ide.eclipse.roo.feature" plugin-identifier="org.springframework.ide.eclipse" url="features/org.springframework.ide.eclipse.roo.feature_3.9.4.201804120850-RELEASE/" version="3.9.4.201804120850-RELEASE">
</feature>
<feature id="org.eclipse.epp.logging.aeri.feature" url="features/org.eclipse.epp.logging.aeri.feature_2.0.7.v20170906-1327/" version="2.0.7.v20170906-1327">
</feature>
<feature id="org.eclipse.emf.ecore.edit" url="features/org.eclipse.emf.ecore.edit_2.9.0.v20170609-0928/" version="2.9.0.v20170609-0928">
</feature>
<feature id="org.springframework.ide.eclipse.cft.feature" plugin-identifier="org.springframework.ide.eclipse" url="features/org.springframework.ide.eclipse.cft.feature_3.9.4.201804120850-RELEASE/" version="3.9.4.201804120850-RELEASE">
</feature>
<feature id="org.eclipse.e4.rcp" url="features/org.eclipse.e4.rcp_1.6.3.v20180329-0507/" version="1.6.3.v20180329-0507">
</feature>
<feature id="org.eclipse.jpt.jpa.eclipselink.feature" plugin-identifier="org.eclipse.jpt.jpa.eclipselink.branding" url="features/org.eclipse.jpt.jpa.eclipselink.feature_3.4.101.v201803161350/" version="3.4.101.v201803161350">
</feature>
<feature id="org.eclipse.emf.mapping.ecore" url="features/org.eclipse.emf.mapping.ecore_2.9.0.v20170609-0928/" version="2.9.0.v20170609-0928">
</feature>
<feature id="org.springframework.ide.eclipse.webflow.feature.source" plugin-identifier="org.springframework.ide.eclipse" url="features/org.springframework.ide.eclipse.webflow.feature.source_3.9.4.201804120850-RELEASE/" version="3.9.4.201804120850-RELEASE">
</feature>
<feature id="org.eclipse.datatools.enablement.ingres.feature" plugin-identifier="org.eclipse.datatools.enablement.finfo" url="features/org.eclipse.datatools.enablement.ingres.feature_1.14.1.201712071719/" version="1.14.1.201712071719">
</feature>
<feature id="org.eclipse.mylyn.wikitext.editors_feature" plugin-identifier="org.eclipse.mylyn.wikitext" url="features/org.eclipse.mylyn.wikitext.editors_feature_3.0.19.201711172000/" version="3.0.19.201711172000">
</feature>
<feature id="org.eclipse.m2e.logback.feature" plugin-identifier="org.eclipse.m2e.logback.configuration" url="features/org.eclipse.m2e.logback.feature_1.8.3.20180227-2137/" version="1.8.3.20180227-2137">
</feature>
<feature id="org.eclipse.emf.codegen" url="features/org.eclipse.emf.codegen_2.12.0.v20170609-0928/" version="2.12.0.v20170609-0928">
</feature>
<feature id="org.eclipse.wst.xml_ui.feature" plugin-identifier="org.eclipse.wst.xml.ui" url="features/org.eclipse.wst.xml_ui.feature_3.9.2.v201803221834/" version="3.9.2.v201803221834">
</feature>
<feature id="org.eclipse.datatools.doc.user" url="features/org.eclipse.datatools.doc.user_1.14.1.201712071719/" version="1.14.1.201712071719">
</feature>
<feature id="org.eclipse.emf" url="features/org.eclipse.emf_2.13.0.v20170609-0928/" version="2.13.0.v20170609-0928">
</feature>
<feature id="org.springframework.ide.eclipse.boot.dash.feature" plugin-identifier="org.springframework.ide.eclipse" url="features/org.springframework.ide.eclipse.boot.dash.feature_3.9.4.201804120850-RELEASE/" version="3.9.4.201804120850-RELEASE">
</feature>
<feature id="org.eclipse.epp.package.jee.feature" plugin-identifier="org.eclipse.epp.package.jee" url="features/org.eclipse.epp.package.jee.feature_4.7.3.20180405-1200/" version="4.7.3.20180405-1200">
</feature>
<feature id="org.eclipse.tm.terminal.connector.local.feature" url="features/org.eclipse.tm.terminal.connector.local.feature_4.3.0.201706140544/" version="4.3.0.201706140544">
</feature>
<feature id="org.springframework.ide.eclipse.roo.feature.source" plugin-identifier="org.springframework.ide.eclipse" url="features/org.springframework.ide.eclipse.roo.feature.source_3.9.4.201804120850-RELEASE/" version="3.9.4.201804120850-RELEASE">
</feature>
<feature id="org.eclipse.draw2d" url="features/org.eclipse.draw2d_3.10.100.201606061308/" version="3.10.100.201606061308">
</feature>
<feature id="org.eclipse.eclemma.feature" plugin-identifier="org.eclipse.eclemma.ui" url="features/org.eclipse.eclemma.feature_3.1.0.201804041601/" version="3.1.0.201804041601">
</feature>
<feature id="org.eclipse.epp.package.common.feature" url="features/org.eclipse.epp.package.common.feature_4.7.3.20180405-1200/" version="4.7.3.20180405-1200">
</feature>
<feature id="org.springframework.ide.eclipse.aop.feature.source" plugin-identifier="org.springframework.ide.eclipse" url="features/org.springframework.ide.eclipse.aop.feature.source_3.9.4.201804120850-RELEASE/" version="3.9.4.201804120850-RELEASE">
</feature>
<feature id="org.eclipse.jst.web_userdoc.feature" url="features/org.eclipse.jst.web_userdoc.feature_3.6.0.v201712131442/" version="3.6.0.v201712131442">
</feature>
<feature id="org.springframework.ide.eclipse.maven.feature.source" plugin-identifier="org.springframework.ide.eclipse" url="features/org.springframework.ide.eclipse.maven.feature.source_3.9.4.201804120850-RELEASE/" version="3.9.4.201804120850-RELEASE">
</feature>
<feature id="org.eclipse.jst.server_adapters.feature" url="features/org.eclipse.jst.server_adapters.feature_3.2.400.v201711301708/" version="3.2.400.v201711301708">
</feature>
<feature id="org.eclipse.jst.webpageeditor.feature" url="features/org.eclipse.jst.webpageeditor.feature_2.9.0.v201803271721/" version="2.9.0.v201803271721">
</feature>
<feature id="org.eclipse.equinox.p2.discovery.feature" url="features/org.eclipse.equinox.p2.discovery.feature_1.1.1.v20170906-1259/" version="1.1.1.v20170906-1259">
</feature>
<feature id="org.eclipse.equinox.p2.rcp.feature" url="features/org.eclipse.equinox.p2.rcp.feature_1.3.2.v20171108-1343/" version="1.3.2.v20171108-1343">
</feature>
<feature id="org.eclipse.jst.web_core.feature" url="features/org.eclipse.jst.web_core.feature_3.9.1.v201802152012/" version="3.9.1.v201802152012">
</feature>
<feature id="org.eclipse.epp.mpc" plugin-identifier="org.eclipse.epp.mpc.ui" url="features/org.eclipse.epp.mpc_1.6.4.v20180214-1810/" version="1.6.4.v20180214-1810">
</feature>
<feature id="org.springframework.ide.eclipse.boot.dash.feature.source" plugin-identifier="org.springframework.ide.eclipse" url="features/org.springframework.ide.eclipse.boot.dash.feature.source_3.9.4.201804120850-RELEASE/" version="3.9.4.201804120850-RELEASE">
</feature>
<feature id="org.eclipse.cft.server.ui.feature" plugin-identifier="org.eclipse.cft.server.branding.ui" url="features/org.eclipse.cft.server.ui.feature_1.0.10.v201709130027/" version="1.0.10.v201709130027">
</feature>
<feature id="org.aspectj" plugin-identifier="org.eclipse.aspectj" url="features/org.aspectj_1.8.13.201803231521/" version="1.8.13.201803231521">
</feature>
<feature id="org.springframework.ide.eclipse.batch.feature" plugin-identifier="org.springframework.ide.eclipse" url="features/org.springframework.ide.eclipse.batch.feature_3.9.4.201804120850-RELEASE/" version="3.9.4.201804120850-RELEASE">
</feature>
<feature id="org.eclipse.wst.ws_core.feature" url="features/org.eclipse.wst.ws_core.feature_3.7.200.v201710302117/" version="3.7.200.v201710302117">
</feature>
<feature id="org.eclipse.rse.dstore" plugin-identifier="org.eclipse.rse.services.dstore" url="features/org.eclipse.rse.dstore_3.7.0.201704251225/" version="3.7.0.201704251225">
</feature>
<feature id="org.eclipse.pde" url="features/org.eclipse.pde_3.13.4.v20180330-0640/" version="3.13.4.v20180330-0640">
</feature>
<feature id="org.eclipse.ecf.filetransfer.httpclient4.ssl.feature" url="features/org.eclipse.ecf.filetransfer.httpclient4.ssl.feature_1.1.0.v20170110-1317/" version="1.1.0.v20170110-1317">
</feature>
<feature id="org.eclipse.datatools.sqldevtools.parsers.feature" plugin-identifier="org.eclipse.datatools.sqltools.parsers.sql" url="features/org.eclipse.datatools.sqldevtools.parsers.feature_1.14.1.201712071719/" version="1.14.1.201712071719">
</feature>
<feature id="org.eclipse.wst.ws_userdoc.feature" url="features/org.eclipse.wst.ws_userdoc.feature_3.1.400.v201405061938/" version="3.1.400.v201405061938">
</feature>
<feature id="org.eclipse.mylyn.java_feature" plugin-identifier="org.eclipse.mylyn.context.core" url="features/org.eclipse.mylyn.java_feature_3.23.0.v20170411-2108/" version="3.23.0.v20170411-2108">
</feature>
<feature id="org.eclipse.m2e.wtp.jaxrs.feature" plugin-identifier="org.eclipse.m2e.wtp.jaxrs" url="features/org.eclipse.m2e.wtp.jaxrs.feature_1.3.3.20170823-1905/" version="1.3.3.20170823-1905">
</feature>
<feature id="org.eclipse.datatools.sqldevtools.ddlgen.feature" plugin-identifier="org.eclipse.datatools.sqltools.ddlgen.ui" url="features/org.eclipse.datatools.sqldevtools.ddlgen.feature_1.14.1.201712071719/" version="1.14.1.201712071719">
</feature>
<feature id="org.eclipse.equinox.weaving.sdk" url="features/org.eclipse.equinox.weaving.sdk_1.2.0.201803231521/" version="1.2.0.201803231521">
</feature>
<feature id="org.eclipse.mylyn.commons.repositories" plugin-identifier="org.eclipse.mylyn.commons.core" url="features/org.eclipse.mylyn.commons.repositories_1.15.0.v20170411-1844/" version="1.15.0.v20170411-1844">
</feature>
<feature id="org.eclipse.rse.ftp" plugin-identifier="org.eclipse.rse.services.files.ftp" url="features/org.eclipse.rse.ftp_3.7.1.201704251225/" version="3.7.1.201704251225">
</feature>
<feature id="org.eclipse.jst.enterprise_core.feature" url="features/org.eclipse.jst.enterprise_core.feature_3.9.0.v201711022131/" version="3.9.0.v201711022131">
</feature>
<feature id="org.eclipse.jst.jsf.apache.trinidad.tagsupport.feature" url="features/org.eclipse.jst.jsf.apache.trinidad.tagsupport.feature_2.6.1.v201802231403/" version="2.6.1.v201802231403">
</feature>
<feature id="org.eclipse.wst.server_userdoc.feature" url="features/org.eclipse.wst.server_userdoc.feature_3.3.300.v201405011426/" version="3.3.300.v201405011426">
</feature>
<feature id="org.eclipse.rse.useractions" url="features/org.eclipse.rse.useractions_3.7.0.201704251225/" version="3.7.0.201704251225">
</feature>
<feature id="org.eclipse.mylyn.monitor" plugin-identifier="org.eclipse.mylyn.commons.core" url="features/org.eclipse.mylyn.monitor_3.23.0.v20170411-1844/" version="3.23.0.v20170411-1844">
</feature>
<feature id="org.eclipse.ajdt" plugin-identifier="org.eclipse.aspectj" url="features/org.eclipse.ajdt_2.2.4.201803231521/" version="2.2.4.201803231521">
</feature>
<feature id="org.eclipse.tm.terminal.control.feature" plugin-identifier="org.eclipse.tm.terminal.control" url="features/org.eclipse.tm.terminal.control.feature_4.3.0.201706140544/" version="4.3.0.201706140544">
</feature>
<feature id="org.springframework.ide.eclipse.integration.feature.source" plugin-identifier="org.springframework.ide.eclipse" url="features/org.springframework.ide.eclipse.integration.feature.source_3.9.4.201804120850-RELEASE/" version="3.9.4.201804120850-RELEASE">
</feature>
<feature id="org.eclipse.jst.server_core.feature" url="features/org.eclipse.jst.server_core.feature_3.4.300.v201606081655/" version="3.4.300.v201606081655">
</feature>
<feature id="org.eclipse.equinox.p2.extras.feature" url="features/org.eclipse.equinox.p2.extras.feature_1.3.1.v20170928-1405/" version="1.3.1.v20170928-1405">
</feature>
<feature id="org.springframework.ide.eclipse.data.feature" plugin-identifier="org.springframework.ide.eclipse" url="features/org.springframework.ide.eclipse.data.feature_3.9.4.201804120850-RELEASE/" version="3.9.4.201804120850-RELEASE">
</feature>
<feature id="org.eclipse.tm.terminal.feature" plugin-identifier="org.eclipse.tm.terminal.view.core" url="features/org.eclipse.tm.terminal.feature_4.3.0.201706140544/" version="4.3.0.201706140544">
</feature>
<feature id="org.springframework.ide.eclipse.boot.feature" plugin-identifier="org.springframework.ide.eclipse" url="features/org.springframework.ide.eclipse.boot.feature_3.9.4.201804120850-RELEASE/" version="3.9.4.201804120850-RELEASE">
</feature>
<feature id="org.eclipse.emf.codegen.ecore.ui" url="features/org.eclipse.emf.codegen.ecore.ui_2.13.0.v20170609-0928/" version="2.13.0.v20170609-0928">
</feature>
<feature id="org.eclipse.emf.ecore.editor" url="features/org.eclipse.emf.ecore.editor_2.13.0.v20170609-0928/" version="2.13.0.v20170609-0928">
</feature>
<feature id="org.sonatype.m2e.mavenarchiver.feature" plugin-identifier="org.sonatype.m2e.mavenarchiver" url="features/org.sonatype.m2e.mavenarchiver.feature_0.17.2.201606141937-signed-20160830073346/" version="0.17.2.201606141937-signed-20160830073346">
</feature>
<feature id="org.eclipse.mylyn.context_feature" plugin-identifier="org.eclipse.mylyn.context.core" url="features/org.eclipse.mylyn.context_feature_3.23.0.v20170414-0629/" version="3.23.0.v20170414-0629">
</feature>
<feature id="org.eclipse.wst.json_core.feature" url="features/org.eclipse.wst.json_core.feature_1.1.2.v201711071522/" version="1.1.2.v201711071522">
</feature>
<feature id="org.tmatesoft.svnkit" url="features/org.tmatesoft.svnkit_1.7.9.r9659_v20130411_2103/" version="1.7.9.r9659_v20130411_2103">
</feature>
<feature id="org.eclipse.datatools.enablement.oracle.feature" plugin-identifier="org.eclipse.datatools.enablement.finfo" url="features/org.eclipse.datatools.enablement.oracle.feature_1.14.1.201712071719/" version="1.14.1.201712071719">
</feature>
<feature id="org.eclipse.emf.databinding" url="features/org.eclipse.emf.databinding_1.4.0.v20170609-0928/" version="1.4.0.v20170609-0928">
</feature>
<feature id="org.eclipse.datatools.enablement.sap.feature" plugin-identifier="org.eclipse.datatools.enablement.finfo" url="features/org.eclipse.datatools.enablement.sap.feature_1.14.1.201712071719/" version="1.14.1.201712071719">
</feature>
<feature id="org.eclipse.wst.xml.xpath2.processor.feature" plugin-identifier="org.eclipse.wst.xml.xpath2" url="features/org.eclipse.wst.xml.xpath2.processor.feature_2.0.301.v201409111854/" version="2.0.301.v201409111854">
</feature>
<feature id="org.eclipse.oomph.setup.core" url="features/org.eclipse.oomph.setup.core_1.8.0.v20170531-0903/" version="1.8.0.v20170531-0903">
</feature>
</site>
</config>
