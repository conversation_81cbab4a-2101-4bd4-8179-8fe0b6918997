<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<?serverAdapter version="3"?><!--Generated by org.eclipse.wst.server.discovery 1.3.100.v201705102053 on Fri Sep 17 2021 11:12:59 CST--><extensionDetails>
  <feature description="Tools for developing applications for Oracle WebLogic Server (versions 12.2.x, 12.1.x, 10.3.x, 10.0 and 9.2)." id="oracle.eclipse.tools.weblogic.feature.group" name="Oracle WebLogic Server Tools" provider="Oracle" runtimeId="com.bea.weblogic1221" serverId="com.bea.weblogic1221.server" uri="http://download.oracle.com/otn_software/oepe/oxygen/" version="17.2.0.201806071916"/>
  <feature description="Additional tools for developing Oracle Cloud applications." id="oracle.eclipse.tools.cloud.feature.group" name="Oracle Cloud Tools" provider="Oracle" runtimeId="oracle.cloud" serverId="oracle.cloud.server" uri="http://download.oracle.com/otn_software/oepe/oxygen/" version="17.2.0.201806071916"/>
  <feature description="Extended tools for developing and administering WebSphere® Application Server Liberty" id="com.ibm.ws.st.tools.base.feature.group" name="WebSphere® Application Server Liberty Extended Tools" provider="IBM" uri="http://public.dhe.ibm.com/ibmdl/export/pub/software/websphere/wasdev/updates/libertyAdapter/oxygen/" version="1.0.556.v20190326_1423"/>
  <feature description="Provides WTP server adapters for JBoss installations. These adapters are capable of deployment and providing classpaths for projects. Zipped or exploded deployments, JMX integration, and other extensions are included." id="org.jboss.ide.eclipse.as.feature.feature.group" name="JBoss AS, WildFly &amp; EAP Server Tools" provider="Red Hat JBoss Middleware" runtimeId="org.jboss.ide.eclipse.as.runtime.eap.70" serverId="org.jboss.ide.eclipse.as.eap.70" uri="http://download.jboss.org/jbosstools/updates/webtools/oxygen/" version="3.5.3.v20180329-1604"/>
  <feature description="Provides Tooling to work with OpenShift architectural version 3." id="org.jboss.tools.openshift.feature.feature.group" name="JBoss OpenShift 3 Tools" provider="JBoss by Red Hat" uri="http://download.jboss.org/jbosstools/updates/webtools/oxygen/" version="3.4.3.v20180412-2005"/>
</extensionDetails>
