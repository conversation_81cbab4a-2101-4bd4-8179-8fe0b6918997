[2018-04-19 21:28:12] Executing startup tasks
[2018-04-19 21:28:12] Java(TM) SE Runtime Environment 1.8.0_152-b16
[2018-04-19 21:28:12] Product self.product.version
[2018-04-19 21:28:12] Workspace D:\Code\EclipseWorkspaces2018\EclipseWorkspace_CZT
[2018-04-19 21:28:12] Bundle org.eclipse.oomph.setup 1.8.0.v20170408-0745, build=3059, branch=2161405b80cf99ed791602ba56cdf44084f5ca43
[2018-04-19 21:28:12] Bundle org.eclipse.oomph.setup.core 1.8.0.v20170531-0903, build=3059, branch=2161405b80cf99ed791602ba56cdf44084f5ca43
[2018-04-19 21:28:12] Bundle org.eclipse.oomph.setup.p2 1.8.0.v20170318-0419, build=3059, branch=2161405b80cf99ed791602ba56cdf44084f5ca43
[2018-04-19 21:28:12] Performing Preference /instance/org.eclipse.jdt.junit/org.eclipse.jdt.junit.show_in_all_views = false
[2018-04-19 21:28:12] Performing Preference /instance/org.eclipse.jdt.junit.core/org.eclipse.jdt.junit.enable_assertions = true
[2018-04-19 21:28:12] Performing Preference /instance/org.eclipse.jdt.ui/org.eclipse.jdt.ui.visibility.order = B,V,R,D,
[2018-04-19 21:28:12] Performing Preference /instance/org.eclipse.jdt.ui/outlinesortoption = T,SF,SI,SM,F,I,C,M,
[2018-04-19 21:28:12] Performing Preference /instance/org.eclipse.m2e.core/eclipse.m2.defaultRuntime = Apache-maven-3.5.2
[2018-04-19 21:28:12] Performing Preference /instance/org.eclipse.m2e.core/eclipse.m2.runtimes = Apache-maven-3.5.2
[2018-04-19 21:28:12] Performing Preference /instance/org.eclipse.m2e.core/eclipse.m2.runtimesNodes/Apache-maven-3.5.2/location = D:\Tools\Apache-maven-3.5.2
[2018-04-19 21:28:12] Performing Preference /instance/org.eclipse.m2e.core/eclipse.m2.runtimesNodes/Apache-maven-3.5.2/type = EXTERNAL
[2018-04-19 21:28:12] Performing Preference /instance/org.eclipse.wst.server.discovery/cache-lastUpdatedDate =  Tue Nov 14 2017 17:26:38 CST
[2018-04-19 21:28:12] 
[2018-04-20 09:05:14] Executing startup tasks
[2018-04-20 09:05:14] Java(TM) SE Runtime Environment 1.8.0_152-b16
[2018-04-20 09:05:14] Product self.product.version
[2018-04-20 09:05:14] Workspace D:\Code\EclipseWorkspaces2018\EclipseWorkspace_CZT
[2018-04-20 09:05:14] Bundle org.eclipse.oomph.setup 1.8.0.v20170408-0745, build=3059, branch=2161405b80cf99ed791602ba56cdf44084f5ca43
[2018-04-20 09:05:14] Bundle org.eclipse.oomph.setup.core 1.8.0.v20170531-0903, build=3059, branch=2161405b80cf99ed791602ba56cdf44084f5ca43
[2018-04-20 09:05:14] Bundle org.eclipse.oomph.setup.p2 1.8.0.v20170318-0419, build=3059, branch=2161405b80cf99ed791602ba56cdf44084f5ca43
[2018-04-20 09:05:14] Performing Preference /instance/org.eclipse.wst.server.discovery/cache-lastUpdatedDate =  Tue Nov 14 2017 17:26:38 CST
[2018-04-20 09:05:14] 
[2018-04-20 09:24:13] Executing startup tasks
[2018-04-20 09:24:13] Java(TM) SE Runtime Environment 1.8.0_152-b16
[2018-04-20 09:24:13] Product self.product.version
[2018-04-20 09:24:13] Workspace D:\Code\EclipseWorkspaces2018\EclipseWorkspace_CZT
[2018-04-20 09:24:13] Bundle org.eclipse.oomph.setup 1.8.0.v20170408-0745, build=3059, branch=2161405b80cf99ed791602ba56cdf44084f5ca43
[2018-04-20 09:24:13] Bundle org.eclipse.oomph.setup.core 1.8.0.v20170531-0903, build=3059, branch=2161405b80cf99ed791602ba56cdf44084f5ca43
[2018-04-20 09:24:13] Bundle org.eclipse.oomph.setup.p2 1.8.0.v20170318-0419, build=3059, branch=2161405b80cf99ed791602ba56cdf44084f5ca43
[2018-04-20 09:24:13] Performing Preference /instance/org.eclipse.wst.server.discovery/cache-lastUpdatedDate =  Tue Nov 14 2017 17:26:38 CST
[2018-04-20 09:24:14] 
[2018-04-20 09:32:45] Executing startup tasks
[2018-04-20 09:32:46] Java(TM) SE Runtime Environment 1.8.0_152-b16
[2018-04-20 09:32:46] Product self.product.version
[2018-04-20 09:32:46] Workspace D:\Code\EclipseWorkspaces2018\EclipseWorkspace_CZT
[2018-04-20 09:32:46] Bundle org.eclipse.oomph.setup 1.8.0.v20170408-0745, build=3059, branch=2161405b80cf99ed791602ba56cdf44084f5ca43
[2018-04-20 09:32:46] Bundle org.eclipse.oomph.setup.core 1.8.0.v20170531-0903, build=3059, branch=2161405b80cf99ed791602ba56cdf44084f5ca43
[2018-04-20 09:32:46] Bundle org.eclipse.oomph.setup.p2 1.8.0.v20170318-0419, build=3059, branch=2161405b80cf99ed791602ba56cdf44084f5ca43
[2018-04-20 09:32:46] Performing Preference /instance/org.eclipse.jdt.junit/org.eclipse.jdt.junit.show_in_all_views = false
[2018-04-20 09:32:46] Performing Preference /instance/org.eclipse.jdt.junit.core/org.eclipse.jdt.junit.enable_assertions = true
[2018-04-20 09:32:46] Performing Preference /instance/org.eclipse.jdt.ui/org.eclipse.jdt.ui.visibility.order = B,V,R,D,
[2018-04-20 09:32:46] Performing Preference /instance/org.eclipse.jdt.ui/outlinesortoption = T,SF,SI,SM,F,I,C,M,
[2018-04-20 09:32:46] Performing Preference /instance/org.eclipse.m2e.core/eclipse.m2.defaultRuntime = Apache-maven-3.5.2
[2018-04-20 09:32:46] Performing Preference /instance/org.eclipse.m2e.core/eclipse.m2.runtimes = Apache-maven-3.5.2
[2018-04-20 09:32:46] Performing Preference /instance/org.eclipse.m2e.core/eclipse.m2.runtimesNodes/Apache-maven-3.5.2/location = D:\Tools\Apache-maven-3.5.2
[2018-04-20 09:32:46] Performing Preference /instance/org.eclipse.m2e.core/eclipse.m2.runtimesNodes/Apache-maven-3.5.2/type = EXTERNAL
[2018-04-20 09:32:46] Performing Preference /instance/org.eclipse.wst.server.discovery/cache-lastUpdatedDate =  Tue Nov 14 2017 17:26:38 CST
[2018-04-20 09:32:46] 
[2018-04-20 09:39:39] Executing startup tasks
[2018-04-20 09:39:39] Java(TM) SE Runtime Environment 1.8.0_152-b16
[2018-04-20 09:39:39] Product self.product.version
[2018-04-20 09:39:39] Workspace D:\Code\EclipseWorkspaces2018\working space_one_project
[2018-04-20 09:39:39] Bundle org.eclipse.oomph.setup 1.8.0.v20170408-0745, build=3059, branch=2161405b80cf99ed791602ba56cdf44084f5ca43
[2018-04-20 09:39:39] Bundle org.eclipse.oomph.setup.core 1.8.0.v20170531-0903, build=3059, branch=2161405b80cf99ed791602ba56cdf44084f5ca43
[2018-04-20 09:39:39] Bundle org.eclipse.oomph.setup.p2 1.8.0.v20170318-0419, build=3059, branch=2161405b80cf99ed791602ba56cdf44084f5ca43
[2018-04-20 09:39:39] Performing Preference /instance/org.eclipse.jdt.junit/org.eclipse.jdt.junit.show_in_all_views = false
[2018-04-20 09:39:39] Performing Preference /instance/org.eclipse.jdt.junit.core/org.eclipse.jdt.junit.enable_assertions = true
[2018-04-20 09:39:39] Performing Preference /instance/org.eclipse.jdt.ui/org.eclipse.jdt.ui.visibility.order = B,V,R,D,
[2018-04-20 09:39:39] Performing Preference /instance/org.eclipse.jdt.ui/outlinesortoption = T,SF,SI,SM,F,I,C,M,
[2018-04-20 09:39:39] Performing Preference /instance/org.eclipse.m2e.core/eclipse.m2.defaultRuntime = Apache-maven-3.5.2
[2018-04-20 09:39:39] Performing Preference /instance/org.eclipse.m2e.core/eclipse.m2.runtimes = Apache-maven-3.5.2
[2018-04-20 09:39:39] Performing Preference /instance/org.eclipse.m2e.core/eclipse.m2.runtimesNodes/Apache-maven-3.5.2/location = D:\Tools\Apache-maven-3.5.2
[2018-04-20 09:39:39] Performing Preference /instance/org.eclipse.m2e.core/eclipse.m2.runtimesNodes/Apache-maven-3.5.2/type = EXTERNAL
[2018-04-20 09:39:39] Performing Preference /instance/org.eclipse.wst.server.discovery/cache-lastUpdatedDate =  Tue Nov 14 2017 17:26:38 CST
[2018-04-20 09:39:39] 
[2018-04-20 09:50:53] Executing startup tasks
[2018-04-20 09:50:53] Java(TM) SE Runtime Environment 1.8.0_152-b16
[2018-04-20 09:50:53] Product self.product.version
[2018-04-20 09:50:53] Workspace D:\Code\EclipseWorkspaces2018\working space_one_project
[2018-04-20 09:50:53] Bundle org.eclipse.oomph.setup 1.8.0.v20170408-0745, build=3059, branch=2161405b80cf99ed791602ba56cdf44084f5ca43
[2018-04-20 09:50:53] Bundle org.eclipse.oomph.setup.core 1.8.0.v20170531-0903, build=3059, branch=2161405b80cf99ed791602ba56cdf44084f5ca43
[2018-04-20 09:50:53] Bundle org.eclipse.oomph.setup.p2 1.8.0.v20170318-0419, build=3059, branch=2161405b80cf99ed791602ba56cdf44084f5ca43
[2018-04-20 09:50:53] Performing Preference /instance/org.eclipse.wst.server.discovery/cache-lastUpdatedDate =  Tue Nov 14 2017 17:26:38 CST
[2018-04-20 09:50:54] 
