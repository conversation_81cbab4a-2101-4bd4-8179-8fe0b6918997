<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
<meta http-equiv="content-type" content="text/html; charset=ISO-8859-1">
<title>About Spring IDE</title>
<meta name="author" content="Spring IDE Developers">
</head>
<body>
<h2>About Spring IDE</h2>
<p>March 26, 2007</p>
<h3>Abstract</h3>
<p>Spring IDE is a set of plugins which provide a user interface for
<a href="http://www.springframework.org">The Spring Framework</a>'s bean
factory XML configuration files including support for Spring 2.0
namespaces, AOP configuration and Spring Web Flow.</p>
<h3>License</h3>

<p>The Spring IDE Project makes available all content in this
plug-in (&quot;Content&quot;). Unless otherwise indicated below, the
Content is provided to you under the terms and conditions of the Eclipse
Public License Version 1.0 (&quot;EPL&quot;). A copy of the EPL is
available at <a href="http://www.eclipse.org/legal/epl-v10.html">http://www.eclipse.org/legal/epl-v10.html</a>.
For purposes of the EPL, &quot;Program&quot; will mean the Content.</p>

<p>If you did not receive this Content directly from the Spring IDE
Project, the Content is being redistributed by another party
(&quot;Redistributor&quot;) and different terms and conditions may apply
to your use of any object code in the Content. Check the Redistributor's
license that was provided with the Content. If no such license exists,
contact the Redistributor. Unless otherwise indicated below, the terms
and conditions of the EPL still apply to any source code in the Content
and such source code may be obtained at <a href="http://www.eclipse.org">http://www.eclipse.org</a>.</p>

<h3>Third Party Content</h3>
<p>The Content includes items that have been sourced from third
parties as set out below. If you did not receive this Content directly
from the Spring IDE Project, the following is provided for informational
purposes only, and you should look to the Redistributor's license for
terms and conditions of use.</p>
<p><em> <strong>The Spring Framework v3.0.5</strong>
<p>This product includes software developed by the Spring Framework
Project (<a href="http://www.springframework.org">http://www.springframework.org</a>).</p>

<p><em> <strong>Spring Web Flow v2.0.7.A</strong>
<p>This product includes software developed by the Spring Framework
Project (<a href="http://www.springframework.org">http://www.springframework.org</a>).</p>

<p><em> <strong>Spring Web Services v1.5.8</strong>
<p>This product includes software developed by the Spring Framework
Project (<a href="http://www.springframework.org">http://www.springframework.org</a>).</p>

<p><em> <strong>Spring Security v3.0.2</strong>
<p>This product includes software developed by the Spring Framework
Project (<a href="http://www.springframework.org">http://www.springframework.org</a>).</p>

<p><em> <strong>Spring Dynamic Modules for OSGi(TM) Runtimes v2.0.0.M2</strong>
<p>This product includes software developed by the Spring Framework
Project (<a href="http://www.springframework.org">http://www.springframework.org</a>).</p>

<p><em> <strong>Spring Batch v2.1.0</strong>
<p>This product includes software developed by the Spring Framework
Project (<a href="http://www.springframework.org">http://www.springframework.org</a>).</p>

<p><em> <strong>Commons Codec v1.3.0</strong>
<p>This product includes software developed by The Apache Software
Foundation (<a href="http://www.apache.org/">http://www.apache.org/</a>).</p>

<p><em> <strong>Commons Collections v3.2.0</strong>
<p>This product includes software developed by The Apache Software
Foundation (<a href="http://www.apache.org/">http://www.apache.org/</a>).</p>

<p><em> <strong>Commons Logging v1.1.1</strong>
<p>This product includes software developed by The Apache Software
Foundation (<a href="http://www.apache.org/">http://www.apache.org/</a>).</p>

<p><em> <strong>Apache MyFaces v1.2.2</strong>
<p>This product includes software developed by The Apache Software
Foundation (<a href="http://www.apache.org/">http://www.apache.org/</a>).</p>

<p><em> <strong>AspectJ Weaver v1.6.10</strong>
<p>This product includes software developed by The Eclipse Software
Foundation (<a href="http://www.eclipse.org/">http://www.eclipse.org/</a>).</p>

<p><em> <strong>Antlr v3.0.1</strong>
<p>Spring IDE includes a binary version of Antlr v3.0.1 (<a
	href="http://www.antlr.org">http://www.antlr.org/</a>).
The source code for Antlr is available from the Antlr download site at
<a href="http://www.antlr.org/download.html">http://www.antlr.org/download.html</a>.

<p>The Antlr license is available at <a
	href="http://www.antlr.org/license.html">http://www.antlr.org/license.html</a>.
The license is also reproduced here:</p>

<pre>Copyright (c) 2003-2007, Terence Parr
All rights reserved.
Redistribution and use in source and binary forms, with or without modification, 
are permitted provided that the following conditions are met:

Redistributions of source code must retain the above copyright notice, this list of conditions 
and the following disclaimer.
Redistributions in binary form must reproduce the above copyright notice, this list of conditions 
and the following disclaimer in the documentation and/or other materials provided with the distribution.
Neither the name of the author nor the names of its contributors may be used to endorse or promote 
products derived from this software without specific prior written permission.
THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND ANY EXPRESS OR 
IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND 
FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS 
BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, 
BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR 
BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT 
LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS 
SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
</pre></p>


<p><em> <strong>ASM v2.2.3</strong>
<p>Spring IDE includes a binary version of ASM v2.2.3 (<a
	href="http://asm.objectweb.org/index.html">http://asm.objectweb.org/</a>).
The source code for ASM is available from the ObjectWeb download site at
<a href="http://asm.objectweb.org/download/">http://asm.objectweb.org/download/</a>.

<p>The ASM license is available at <a
	href="http://asm.objectweb.org/license.html">http://asm.objectweb.org/license.html</a>.
The license is also reproduced here:</p>

<pre>Copyright (c) 2000-2005 INRIA, France Telecom
All rights reserved.

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions
are met:

1. Redistributions of source code must retain the above copyright
   notice, this list of conditions and the following disclaimer.

2. Redistributions in binary form must reproduce the above copyright
   notice, this list of conditions and the following disclaimer in the
   documentation and/or other materials provided with the distribution.

3. Neither the name of the copyright holders nor the names of its
   contributors may be used to endorse or promote products derived from
   this software without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE
LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF
THE POSSIBILITY OF SUCH DAMAGE.
</pre></p>


<p><em> <strong>backport-util-concurrent 3.1.0</strong>
<p>Spring IDE includes a binary version of backport-util-concurrent v3.1.0 (<a
	href="http://dcl.mathcs.emory.edu/util/backport-util-concurrent/">http://dcl.mathcs.emory.edu/util/backport-util-concurrent/</a>).
The source code for backport-util-concurrent is available from the download site at
<a href="http://dcl.mathcs.emory.edu/util/backport-util-concurrent/dist/">http://dcl.mathcs.emory.edu/util/backport-util-concurrent/dist/</a>.


<p>The backport-util-concurrent license is available at <a
	href="http://creativecommons.org/licenses/publicdomain">http://creativecommons.org/licenses/publicdomain</a>.
The license is also reproduced here:</p>

<pre><em>Copyright-Only Dedication (based on United States law) or Public Domain Certification</em>

The person or persons who have associated work with this document (the 
"Dedicator" or "Certifier") hereby either (a) certifies that, to the 
best of his knowledge, the work of authorship identified is in the public 
domain of the country from which the work is published, or (b) hereby 
dedicates whatever copyright the dedicators holds in the work of authorship 
identified below (the "Work") to the public domain. A certifier, moreover, 
dedicates any copyright interest he may have in the associated work, and 
for these purposes, is described as a "dedicator" below.

A certifier has taken reasonable steps to verify the copyright status of 
this work. Certifier recognizes that his good faith efforts may not shield 
him from liability if in fact the work certified is not in the public domain.

Dedicator makes this dedication for the benefit of the public at large and 
to the detriment of the Dedicator's heirs and successors. Dedicator intends 
this dedication to be an overt act of relinquishment in perpetuity of all 
present and future rights under copyright law, whether vested or contingent, 
in the Work. Dedicator understands that such relinquishment of all rights 
includes the relinquishment of all rights to enforce (by lawsuit or otherwise) 
those copyrights in the Work.

Dedicator recognizes that, once placed in the public domain, the Work may be 
freely reproduced, distributed, transmitted, used, modified, built upon, or 
otherwise exploited by anyone for any purpose, commercial or non-commercial, 
and in any way, including by methods that have not yet been invented or 
conceived.
</pre></p>

<p><em> <strong>AOP Alliance</strong>
<p>LICENCE: all the source code provided by AOP Alliance is Public Domain.
</p>

<p><em> <strong>javax.el v1.0.0</strong>
<p>This product includes software distributed by Sun (meanwhile Oracle) under CDDL license
(<a href="http://www.sun.com/cddl/cddl.html">http://www.sun.com/cddl/cddl.html</a>).</p>

<p><em> <strong>javax.jms v1.1.0</strong>
<p>This product includes software distributed by Sun (meanwhile Oracle) under CDDL license
(<a href="http://www.sun.com/cddl/cddl.html">http://www.sun.com/cddl/cddl.html</a>).</p>

<p><em> <strong>javax.servlet v2.5.0</strong>
<p>This product includes software distributed by Sun (meanwhile Oracle) under CDDL license
(<a href="http://www.sun.com/cddl/cddl.html">http://www.sun.com/cddl/cddl.html</a>).</p>

<p><em> <strong>javax.servlet.jsp v2.1.0</strong>
<p>This product includes software distributed by Sun (meanwhile Oracle) under CDDL license
(<a href="http://www.sun.com/cddl/cddl.html">http://www.sun.com/cddl/cddl.html</a>).</p>

<p><em> <strong>javax.servlet.jsp.jstl v1.2.0</strong>
<p>This product includes software distributed by Sun (meanwhile Oracle) under CDDL license
(<a href="http://www.sun.com/cddl/cddl.html">http://www.sun.com/cddl/cddl.html</a>).</p>

<p><em> <strong>javax.activation v1.1.0</strong>
<p>This product includes software distributed by Sun (meanwhile Oracle) under CDDL license
(<a href="http://www.sun.com/cddl/cddl.html">http://www.sun.com/cddl/cddl.html</a>).</p>

<p><em> <strong>javax.annotation v1.0.0</strong>
<p>This product includes software distributed by Sun (meanwhile Oracle) under CDDL license
(<a href="http://www.sun.com/cddl/cddl.html">http://www.sun.com/cddl/cddl.html</a>).</p>

<p><em> <strong>javax.mail v1.4.0</strong>
<p>This product includes software distributed by Sun (meanwhile Oracle) under CDDL license
(<a href="http://www.sun.com/cddl/cddl.html">http://www.sun.com/cddl/cddl.html</a>).</p>

<p><em> <strong>javax.xml.stream v1.0.1</strong>
<p>This product includes software distributed by Sun (meanwhile Oracle) under CDDL license
(<a href="http://www.sun.com/cddl/cddl.html">http://www.sun.com/cddl/cddl.html</a>).</p>

<p><em> <strong>javax.xml.ws v2.1.0</strong>
<p>This product includes software distributed by Sun (meanwhile Oracle) under CDDL license
(<a href="http://www.sun.com/cddl/cddl.html">http://www.sun.com/cddl/cddl.html</a>).</p>

</em></p>


<address>Copyright (c) 2005, 2011 Spring IDE Developers</address>
</body>
</html>
