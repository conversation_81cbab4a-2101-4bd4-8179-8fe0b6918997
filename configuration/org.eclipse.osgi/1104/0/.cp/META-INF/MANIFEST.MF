Manifest-Version: 1.0
Source-Control-Identifier: 4dc0ff8c145b191285855fd39e343281b2f06528
Bundle-SymbolicName: org.springframework.ide.eclipse.doc;singleton:=tr
 ue
Bundle-Version: 3.9.4.201804120850-RELEASE
Bundle-Name: %pluginName
Archiver-Version: Plexus Archiver
Bundle-Localization: plugin
Built-By: bamboo
Bundle-ManifestVersion: 2
Bundle-Vendor: %providerName
Created-By: Apache Maven 3.3.9
Build-Jdk: 1.8.0_144

Name: about.html
SHA-256-Digest: LfxAWCjqIthvJ32yMY0FF8cAzMidBC82sCNg5Kyoej8=

Name: plugin.xml
SHA-256-Digest: qkDmRP6WrTfRMixDNxuLDkQeeJTV3TTituM34lSaxfk=

Name: intro/images/springide.png
SHA-256-Digest: 0HdArnF8ZSGnItbHCIt8vTgnezkDd2uQCg34+sp7Kbs=

Name: intro/css/overview.css
SHA-256-Digest: FXCfXBKLYw7ANpYMKe8/wO45SyggThihg74HcfM2gKg=

Name: intro/overview.xml
SHA-256-Digest: GbQQD3tJRMPJE4xiD3fQ47tuUGZd5oY2EGkO7SW1J7Q=

Name: toc.xml
SHA-256-Digest: N3FXlcpO1H26Zs3pw9hgD+uMKds5oDIOU1OTtOpxyxk=

Name: intro/css/overview.properties
SHA-256-Digest: yej2akShYxIRdLgwUhUxBbK4RSqdOdwiALepXfQGF2Y=

Name: plugin.properties
SHA-256-Digest: pLvPik9dx47T+z9snprvWGIXnworMiiiI+IaFdPM/a8=

