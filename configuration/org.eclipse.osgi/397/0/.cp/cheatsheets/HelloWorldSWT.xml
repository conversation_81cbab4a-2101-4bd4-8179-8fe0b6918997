<?xml version="1.0" encoding="UTF-8"?>
<cheatsheet title="Create a Hello World SWT application">
   <intro href="/org.eclipse.platform.doc.user/reference/ref-cheatsheets.htm">
      <description>
         This cheat sheet shows you how to create a &quot;Hello World&quot; application
			that uses the Standard Widget Toolkit (SWT). The application will
			simply display an empty window to the user.<br/>
			<br/>
			If you need help at any step, click the (?) to the right. Let&apos;s get
			started!
      </description>
   </intro>
   <item title="Open the Java perspective" dialog="true" skip="false" href="/org.eclipse.platform.doc.user/concepts/concepts-4.htm">
      <description>
         If you&apos;re not already in the Java perspective, in the main menu
			select <b>Window</b> &gt; <b>Open Perspective</b> &gt; <b>Java</b>
			or click on the &quot;Click to Perform&quot; link below.
      </description>
      <command serialization="org.eclipse.ui.perspectives.showPerspective(org.eclipse.ui.perspectives.showPerspective.perspectiveId=org.eclipse.jdt.ui.JavaPerspective)" required="false" translate="">
      </command>
   </item>
   <item title="Import the SWT project from target platform using Plug-In Development Environment" dialog="true" skip="true" href="/org.eclipse.platform.doc.user/tasks/tasks-importproject.htm">
      <description>
      <b>Note:</b> This step requires <b>Plugin Developement Environment (PDE)</b> to be installed in Eclipse. If you do not have PDE installed, skip this step or install PDE to continue with this step.<br/><br/>
         Open the Import wizard from the main menu via <b>File &gt; Import...</b>,
			and select <b>Plug-in Development > Plug-ins and Fragments</b>. Click <b>Next</b>.<br/><br/>
			On the <b>Import Plug-ins and Fragments</b> page, select <br/>
				<b>Import from: </b> The active target platform.<br/>
				<b>Plug-ins and Fragments to import: </b> Select from all plug-ins and fragments found at specified location.<br/>
				<b>Import As: </b> Projects with source folders.<br/>
			Click <b>Next</b>.<br/><br/>
			On the <b>Selection</b> page, Add <b>org.eclipse.swt.{platform}.{os}.{arch}</b> (for example: org.eclipse.swt.win32.win32.x86 for win32) to <b>Plug-ins and Fragments to Import:</b> list.<br/>
			Click <b>Finish</b>.<br/>
			<br/>
			This will create the org.eclipse.swt.{platform}.{os}.{arch} project which we will need
			to compile and run the application.
      </description>
      <command serialization="org.eclipse.ui.file.import(importWizardId=org.eclipse.pde.ui.PluginImportWizard)" required="false" translate="">
      </command>
   </item>
   <item title="Import the SWT project from downloaded SWT archive" dialog="true" skip="true" href="/org.eclipse.platform.doc.user/tasks/tasks-importproject.htm">
      <description>
 		 <b>Note:</b> Skip this step if you have already created an SWT project in the previous step.<br/><br/>
         Open page http://download.eclipse.org/eclipse/downloads/#Latest_Release in your web browser,
			go to the latest release build, and find the <b>SWT Binary and Source</b>
			download for your platform (e.g.: swt-4.4.2-win32-win32-x86_64.zip).<br/>
			<br/>
			Save the file to your disk; you do not need to extract the archive.<br/>
			<br/>
			Import the SWT project from the main menu via File &gt; Import...,
			and select <b>Existing Projects into Workspace</b>. Click <b>Next</b>. Specify the
			<b>archive</b> file you downloaded and click <b>Finish</b>.<br/>
			<br/>
			This will create the org.eclipse.swt project which we will need
			to compile and run the application.
      </description>
      <command serialization="org.eclipse.ui.file.import(importWizardId=org.eclipse.ui.wizards.import.ExternalProject)" required="false" translate="">
      </command>
   </item>
   <item title="Verify the SWT Project in workspace" skip="false">
      <description>
		Verify that an SWT project has been created in your workspace in the format <b>org.eclipse.swt.{platform}.{os}.{arch}</b>
		(or just <b>org.eclipse.swt</b> if you've imported the project from an archive).<br/><br/>
		If not, make sure that you have completed either one of the above <b>Import the SWT project</b> steps.<br/><br/>
		Click on <b>Click when complete</b> to go to the next step.
      </description>
   </item>
   <item title="Create a Java project" dialog="true" skip="false" href="/org.eclipse.jdt.doc.user/concepts/concepts-3.htm">
      <description>
         Now we need a project to store our own source code. In the main
			toolbar, click on the <b>New Java Project</b> button, or click on
			the link below. Enter <b>HelloWorldSWT</b> for the project name,
			then click <b>Finish</b>.
      </description>
      <command serialization="org.eclipse.ui.newWizard(newWizardId=org.eclipse.jdt.ui.wizards.JavaProjectWizard)" required="false" translate="">
      </command>
   </item>
   <item title="Configure the Java project" dialog="true" skip="false" href="/org.eclipse.jdt.doc.user/reference/ref-123.htm">
      <description>
         Since our project requires SWT, we need to specify this in the
			project properties. Right-click on the project and select
			<b>Properties</b>.<br/>
			<br/>
			In the <b>Java Build Path</b> page open the <b>Projects</b> tab,
			add the org.eclipse.swt.{platform}.{os}.{arch} project, then click <b>OK</b>.
      </description>
   </item>
   <item title="Create a class" dialog="true" skip="false" href="/org.eclipse.jdt.doc.user/gettingStarted/qs-9.htm">
      <description>
         The next step is to create a new class. In the main toolbar,
			click on the <b>New Java Class</b> button (or the link below).
			If not already specified, select <b>HelloWorldSWT/src</b> as the source folder. Enter <b>HelloWorldSWT</b> for the class name and select the checkbox to
			create the <b>main()</b> method, then click <b>Finish</b>.<br/>
			<br/>
			The Java editor will automatically open showing your new class.
      </description>
      <command serialization="org.eclipse.ui.newWizard(newWizardId=org.eclipse.jdt.ui.wizards.NewClassCreationWizard)" required="false" translate="">
      </command>
   </item>
   <item title="Write the Java code" dialog="true" skip="false" href="/org.eclipse.platform.doc.isv/guide/swt_widgets.htm">
      <description>
         In the Java editor, enter the following Java code in the
			<b>main()</b> method:<br/>
			<br/>
			Display display = new Display();<br/> 
			Shell shell = new Shell(display);<br/> 
			shell.setText(&quot;Hello world!&quot;);<br/> 
			shell.open();<br/> 
			while (!shell.isDisposed()) {<br/> 
			if (!display.readAndDispatch()) display.sleep();<br/> 
			}<br/> 
			display.dispose();<br/> 
			<br/>
			You will get <b>compile errors</b>. Right click in the Java editor
			and select Source &gt; Organize Imports, then <b>save</b> your
			changes.
      </description>
   </item>
   <item title="Run your Java application" dialog="true" skip="false" href="/org.eclipse.jdt.doc.user/gettingStarted/qs-12.htm">
      <description>
         To <b>run</b> your application, right-click on your class in the
			Package Explorer and select <b>Run As</b> &gt; <b>Java
			Application</b>. A new empty window should appear with the
			title &quot;Hello world!&quot;.<br/>
			<br/>
			Congratulations! You have successfully created a Hello World SWT
			application!
      </description>
   </item>
</cheatsheet>
