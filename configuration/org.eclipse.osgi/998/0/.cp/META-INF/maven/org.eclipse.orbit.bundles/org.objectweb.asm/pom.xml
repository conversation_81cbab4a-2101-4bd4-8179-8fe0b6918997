<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>org.eclipse.orbit.bundles</groupId>
    <artifactId>asm</artifactId>
    <version>1.0.0-SNAPSHOT</version>
  </parent>
  <artifactId>org.objectweb.asm</artifactId>
  <version>6.0.0-SNAPSHOT</version>
  <packaging>eclipse-bundle-recipe</packaging>
  <name>ASM Core</name>
  <dependencies>
    <dependency>
      <groupId>org.ow2.asm</groupId>
      <artifactId>asm</artifactId>
      <version>6.0</version>
    </dependency>
  </dependencies>

  <build>
    <plugins>
      <plugin>
        <groupId>org.eclipse.ebr</groupId>
        <artifactId>ebr-maven-plugin</artifactId>
        <configuration>
          <bndInstructions>
            <!-- https://issues.apache.org/jira/browse/FELIX-5430 -->
            <_failok>true</_failok>
          </bndInstructions>
        </configuration>
      </plugin>
    </plugins>
  </build>

</project>
