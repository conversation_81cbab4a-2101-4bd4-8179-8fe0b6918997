<!DOCTYPE html>
<html lang="en" dir="ltr">
<head profile="http://www.w3.org/1999/xhtml/vocab">
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta name="viewport" content="width=device-width, initial-scale=1" />
<link rel="shortcut icon" href="https://opensource.org/files/osi_favicon.png" type="image/png" />
<meta name="HandheldFriendly" content="true" />
<link rel="shortlink" href="/node/568" />
<meta name="Generator" content="Drupal 7 (http://drupal.org)" />
<link rel="canonical" href="/licenses/BSD-3-Clause" />
<meta name="MobileOptimized" content="width" />
  <title>The 3-Clause BSD License | Open Source Initiative</title>
  <link type="text/css" rel="stylesheet" href="https://opensource.org/files/css/css_xE-rWrJf-fncB6ztZfd2huxqgxu4WO-qwma6Xer30m4.css" media="all" />
<link type="text/css" rel="stylesheet" href="https://opensource.org/files/css/css_4p37TiWeuzRfdymI_lPgCuu6wEwSDhUquxUkHLI7QnU.css" media="all" />
<link type="text/css" rel="stylesheet" href="https://opensource.org/files/css/css_7pVDXe_UpB2nZBNQQy5luyoV4RcrNSYSFXF0ZRr1OwI.css" media="all" />
<link type="text/css" rel="stylesheet" href="//maxcdn.bootstrapcdn.com/bootstrap/3.2.0/css/bootstrap.min.css" media="all" />
<link type="text/css" rel="stylesheet" href="https://opensource.org/files/css/css_KGZcOm3i1wmtbgZsjo-3V9FM4wZ-5UDcpJ7Vfzmt45E.css" media="all" />
<link type="text/css" rel="stylesheet" href="https://opensource.org/files/css/css_G9cu63kkDQ56GYuF3QrqJxma5HT-bUVZckUWKUzFCF4.css" media="all" />

<!--[if (lt IE 9)]>
<link type="text/css" rel="stylesheet" href="https://opensource.org/sites/all/themes/bootstrap-business/css/ie8.css?oixc1s" media="all" />
<![endif]-->

    
  <!-- HTML5 element support for IE6-8 -->
  <!--[if lt IE 9]>
    <script src="//html5shiv.googlecode.com/svn/trunk/html5.js"></script>
  <![endif]-->
  <script type="text/javascript" src="//code.jquery.com/jquery-1.10.2.min.js"></script>
<script type="text/javascript">
<!--//--><![CDATA[//><!--
window.jQuery || document.write("<script src='/sites/all/modules/jquery_update/replace/jquery/1.10/jquery.min.js'>\x3C/script>")
//--><!]]>
</script>
<script type="text/javascript" src="https://opensource.org/files/js/js_aczm2rRgH_slWBPnvD3KMrK7rwa1i99HOq8IUAb99Co.js"></script>
<script type="text/javascript" src="https://opensource.org/files/js/js_S1LEF47EkPmx3YwhVw4GyJzlvDrb3h52SfXyxa_2HPA.js"></script>
<script type="text/javascript" src="//maxcdn.bootstrapcdn.com/bootstrap/3.2.0/js/bootstrap.min.js"></script>
<script type="text/javascript">
<!--//--><![CDATA[//><!--
jQuery(document).ready(function($) { 
		$(window).scroll(function() {
			if($(this).scrollTop() != 0) {
				$("#toTop").fadeIn();	
			} else {
				$("#toTop").fadeOut();
			}
		});
		
		$("#toTop").click(function() {
			$("body,html").animate({scrollTop:0},800);
		});	
		
		});
//--><!]]>
</script>
<script type="text/javascript" src="https://opensource.org/files/js/js_ruOYJN6FkJU2O5L1dAKVnDloSn5R6LjnLW88zFxS1Uw.js"></script>
<script type="text/javascript" src="https://opensource.org/files/js/js_JQHTvV_SkyFlN3f2BnQwnusF-eI6tkX8wrKAk2siiZU.js"></script>
<script type="text/javascript">
<!--//--><![CDATA[//><!--
jQuery.extend(Drupal.settings, {"basePath":"\/","pathPrefix":"","ajaxPageState":{"theme":"bootstrap_business","theme_token":"9gfPxioUp189wPjudiitm4VNnTXr7bPzIux1h3qkjFY","js":{"\/\/code.jquery.com\/jquery-1.10.2.min.js":1,"0":1,"misc\/jquery.once.js":1,"misc\/drupal.js":1,"sites\/all\/modules\/popup_announcement\/popup_announcement.js":1,"\/\/maxcdn.bootstrapcdn.com\/bootstrap\/3.2.0\/js\/bootstrap.min.js":1,"1":1,"sites\/all\/libraries\/superfish\/jquery.hoverIntent.minified.js":1,"sites\/all\/libraries\/superfish\/sftouchscreen.js":1,"sites\/all\/libraries\/superfish\/sfsmallscreen.js":1,"sites\/all\/libraries\/superfish\/supposition.js":1,"sites\/all\/libraries\/superfish\/superfish.js":1,"sites\/all\/libraries\/superfish\/supersubs.js":1,"sites\/all\/modules\/superfish\/superfish.js":1,"sites\/all\/themes\/bootstrap-business\/js\/jquery.browser.min.js":1},"css":{"modules\/system\/system.base.css":1,"modules\/system\/system.menus.css":1,"modules\/system\/system.messages.css":1,"modules\/system\/system.theme.css":1,"modules\/aggregator\/aggregator.css":1,"modules\/comment\/comment.css":1,"modules\/field\/theme\/field.css":1,"sites\/all\/modules\/mollom\/mollom.css":1,"modules\/node\/node.css":1,"modules\/search\/search.css":1,"modules\/user\/user.css":1,"sites\/all\/modules\/views\/css\/views.css":1,"sites\/all\/modules\/ctools\/css\/ctools.css":1,"sites\/all\/modules\/popup_announcement\/popup_announcement.css":1,"\/\/maxcdn.bootstrapcdn.com\/bootstrap\/3.2.0\/css\/bootstrap.min.css":1,"sites\/all\/libraries\/superfish\/css\/superfish.css":1,"sites\/all\/themes\/bootstrap-business\/css\/style.css":1,"sites\/all\/themes\/bootstrap-business\/color\/colors.css":1,"sites\/all\/themes\/bootstrap-business\/css\/local.css":1,"sites\/all\/themes\/bootstrap-business\/css\/ie8.css":1}},"popup_announcement":{"width":"500","height":"300","delay":"1"},"urlIsAjaxTrusted":{"\/licenses\/BSD-3-Clause":true},"superfish":{"1":{"id":"1","sf":{"animation":{"opacity":"show","height":"show"},"speed":"\u0027fast\u0027","autoArrows":false,"dropShadows":true,"disableHI":false},"plugins":{"touchscreen":{"mode":"window_width"},"smallscreen":{"mode":"window_width","addSelected":false,"menuClasses":false,"hyperlinkClasses":false,"title":"Navigation"},"supposition":true,"bgiframe":false,"supersubs":{"minWidth":"12","maxWidth":"27","extraWidth":1}}}}});
//--><!]]>
</script>
</head>
<body class="html not-front not-logged-in no-sidebars page-node page-node- page-node-568 node-type-page" >
  <div id="skip-link">
    <a href="#main-content" class="element-invisible element-focusable">Skip to main content</a>
  </div>
    <div id="toTop"><span class="glyphicon glyphicon-chevron-up"></span></div>



<!-- #header-top -->
<div id="header-top" class="clearfix">
    <div class="container">

        <!-- #header-top-inside -->
        <div id="header-top-inside" class="clearfix">
            <div class="row">
            
                        <div class="col-md-8">
                <!-- #header-top-left -->
                <div id="header-top-left" class="clearfix">
                      <div class="region region-header-top-left">
    <div id="block-menu-secondary-menu" class="block block-menu clearfix">

    
  <div class="content">
    <ul class="menu"><li class="first leaf"><a href="/" title="">Home</a></li>
<li class="leaf"><a href="/blog" title="">From the Board</a></li>
<li class="leaf"><a href="/contact" title="">Contact</a></li>
<li class="last leaf"><a href="/civicrm/contribute/transact?reset=1&amp;id=2" title="">Donate</a></li>
</ul>  </div>
</div>
  </div>
                </div>
                <!-- EOF:#header-top-left -->
            </div>
                        
                        <div class="col-md-4">
                <!-- #header-top-right -->
                <div id="header-top-right" class="clearfix">
                      <div class="region region-header-top-right">
    <div id="block-search-form" class="block block-search clearfix">

    
  <div class="content">
    <form action="/licenses/BSD-3-Clause" method="post" id="search-block-form" accept-charset="UTF-8"><div><div class="container-inline">
      <h2 class="element-invisible">Search form</h2>
    <div class="form-item form-type-textfield form-item-search-block-form">
 <input onblur="if (this.value == &#039;&#039;) {this.value = &#039;Search this website...&#039;;}" onfocus="if (this.value == &#039;Search this website...&#039;) {this.value = &#039;&#039;;}" type="text" id="edit-search-block-form--2" name="search_block_form" value="Search this website..." size="15" maxlength="128" class="form-text" />
</div>
<div class="form-actions form-wrapper" id="edit-actions"><input value="" type="submit" id="edit-submit" name="op" class="form-submit" /></div><input type="hidden" name="form_build_id" value="form-1aIg0OAI1BDeBpzoJMh4MKR2-mAY4mxLIKEb4ip_OvE" />
<input type="hidden" name="form_id" value="search_block_form" />
</div>
</div></form>  </div>
</div>
  </div>
                </div>
                <!-- EOF:#header-top-right -->
            </div>
                        
            </div>
        </div>
        <!-- EOF: #header-top-inside -->

    </div>
</div>
<!-- EOF: #header-top -->    

<!-- header -->
<header id="header" role="banner" class="clearfix">
    <div class="container">

        <!-- #header-inside -->
        <div id="header-inside" class="clearfix">
            <div class="row">
                <div class="col-md-8">

                                <div id="logo">
                <a href="/" title="Home" rel="home"> <img src="https://opensource.org/files/osi_keyhole_300X300_90ppi_0.png" alt="Home" /> </a>
                </div>
                
                                <div id="site-name">
                <a href="/" title="Home">Open Source Initiative</a>
                </div>
                                
                                
                </div>
                
                <div class="col-md-4">
                
                                  <div class="region region-header">
    <div id="block-block-16" class="block block-block clearfix">

    <h2>Donate Today!</h2>
  
  <div class="content">
    <font size="2">Your contributions make open source work.<br />
Help us work for open source: <a href="https://opensource.org/donate"><b><i>DONATE TODAY </i></b></a>!<br /><b>Giving Tuesday: <font color="green">$0....[$4,268]</font><font color="red">................$20,000</font> :Dec. 31</b></font>  </div>
</div>
  </div>
                
                </div>
            </div>
        </div>
        <!-- EOF: #header-inside -->

    </div>
</header>
<!-- EOF: #header --> 

<!-- #main-navigation --> 
<div id="main-navigation" class="clearfix">
    <div class="container">

        <!-- #main-navigation-inside -->
        <div id="main-navigation-inside" class="clearfix">
            <div class="row">
                <div class="col-md-12">
                    <nav role="navigation">
                                                  <div class="region region-navigation">
    <div id="block-superfish-1" class="block block-superfish clearfix">

    
  <div class="content">
    <ul id="superfish-1" class="menu sf-menu sf-navigation sf-horizontal sf-style-none sf-total-items-6 sf-parent-items-6 sf-single-items-0"><li id="menu-37-1" class="first odd sf-item-1 sf-depth-1 sf-total-children-4 sf-parent-children-2 sf-single-children-2 menuparent"><a href="/about" title="About the Open Source Initiative" class="sf-depth-1 menuparent">About</a><ul><li id="menu-75-1" class="first odd sf-item-1 sf-depth-2 sf-no-children"><a href="/history" title="History of the OSI" class="sf-depth-2">History</a></li><li id="menu-82-1" class="middle even sf-item-2 sf-depth-2 sf-total-children-7 sf-parent-children-0 sf-single-children-7 menuparent"><a href="/board" title="Board of Directors" class="sf-depth-2 menuparent">Board</a><ul><li id="menu-83-1" class="first odd sf-item-1 sf-depth-3 sf-no-children"><a href="/docs/board-annotated" title="OSI Board -- With Annotations" class="sf-depth-3">Board - Annotated</a></li><li id="menu-96-1" class="middle even sf-item-2 sf-depth-3 sf-no-children"><a href="/minutes" title="Public Minutes of Board Meetings" class="sf-depth-3">Minutes</a></li><li id="menu-185-1" class="middle odd sf-item-3 sf-depth-3 sf-no-children"><a href="/organization" title="These portfolios represent the activities of the current OSI board." class="sf-depth-3">Organization &amp; Operations</a></li><li id="menu-95-1" class="middle even sf-item-4 sf-depth-3 sf-no-children"><a href="/articles-of-incorporation" title="OSI incorporation record" class="sf-depth-3">Articles of Incorporation</a></li><li id="menu-1475-1" class="middle odd sf-item-5 sf-depth-3 sf-no-children"><a href="/elections" class="sf-depth-3">Board Elections</a></li><li id="menu-84-1" class="middle even sf-item-6 sf-depth-3 sf-no-children"><a href="/bylaws" title="Bylaws of the Open Source Initiative" class="sf-depth-3">Bylaws</a></li><li id="menu-1317-1" class="last odd sf-item-7 sf-depth-3 sf-no-children"><a href="/conflict_of_interest_policy" title="" class="sf-depth-3">Conflict of Interest</a></li></ul></li><li id="menu-1843-1" class="middle odd sf-item-3 sf-depth-2 sf-total-children-2 sf-parent-children-0 sf-single-children-2 menuparent"><a href="/trademark" title="" class="sf-depth-2 menuparent">Trademark &amp; Logo</a><ul><li id="menu-184-1" class="first odd sf-item-1 sf-depth-3 sf-no-children"><a href="/trademark-guidelines" title="OSI&#039;s Trademark Policy" class="sf-depth-3">Trademark Guidelines</a></li><li id="menu-183-1" class="last even sf-item-2 sf-depth-3 sf-no-children"><a href="/logo-usage-guidelines" title="Guidelines for appearance and usage of OSI Logo" class="sf-depth-3">Logo Guidelines</a></li></ul></li><li id="menu-126-1" class="last even sf-item-4 sf-depth-2 sf-no-children"><a href="/ToS" title="Rules for posting content on this site" class="sf-depth-2">Terms of Service</a></li></ul></li><li id="menu-65-1" class="middle even sf-item-2 sf-depth-1 sf-total-children-5 sf-parent-children-3 sf-single-children-2 menuparent"><a href="/licenses" class="sf-depth-1 menuparent">Licenses</a><ul><li id="menu-61-1" class="first odd sf-item-1 sf-depth-2 sf-total-children-1 sf-parent-children-0 sf-single-children-1 menuparent"><a href="/osd" title="The actual OSD defining what constitutes an Open Source licence" class="sf-depth-2 menuparent">Open Source Definition</a><ul><li id="menu-62-1" class="firstandlast odd sf-item-1 sf-depth-3 sf-no-children"><a href="/osd-annotated" title="The OSD with explationations and rationale interspersed." class="sf-depth-3">OSD - Annotated</a></li></ul></li><li id="menu-77-1" class="middle even sf-item-2 sf-depth-2 sf-no-children"><a href="/licenses/category" title="Licenses by Category" class="sf-depth-2">Licenses by Category</a></li><li id="menu-72-1" class="middle odd sf-item-3 sf-depth-2 sf-no-children"><a href="/licenses/alphabetical" title="Licenses that are approved by the OSI as conforming to the OSD" class="sf-depth-2">Licenses by Name</a></li><li id="menu-66-1" class="middle even sf-item-4 sf-depth-2 sf-total-children-2 sf-parent-children-0 sf-single-children-2 menuparent"><a href="/approval" title="Certifying licences as OSD-compliant" class="sf-depth-2 menuparent">License Review Process</a><ul><li id="menu-67-1" class="first odd sf-item-1 sf-depth-3 sf-no-children"><a href="/proliferation" title="Supporting choice while maintaining sanity" class="sf-depth-3">Licence Proliferation</a></li><li id="menu-69-1" class="last even sf-item-2 sf-depth-3 sf-no-children"><a href="/proliferation-report" title="License Proliferation Committee&#039;s report to the OSI Board" class="sf-depth-3">LP report to the Board</a></li></ul></li><li id="menu-99-1" class="last odd sf-item-5 sf-depth-2 sf-total-children-4 sf-parent-children-0 sf-single-children-4 menuparent"><a href="/osr-intro" title="Open Standards Requirement for Software" class="sf-depth-2 menuparent">Open Standards</a><ul><li id="menu-101-1" class="first odd sf-item-1 sf-depth-3 sf-no-children"><a href="/osr" title="An &quot;open standard&quot; must not prohibit conforming implementations in open source software." class="sf-depth-3">The Open Standards Requirement</a></li><li id="menu-102-1" class="middle even sf-item-2 sf-depth-3 sf-no-children"><a href="/osr-compliance" class="sf-depth-3">Open Standards Requirement Compliance</a></li><li id="menu-100-1" class="middle odd sf-item-3 sf-depth-3 sf-no-children"><a href="/osr-rationale" class="sf-depth-3">Open Standards Requirement Rationale</a></li><li id="menu-103-1" class="last even sf-item-4 sf-depth-3 sf-no-children"><a href="/osr-faq" title="Frequently asked questions about the Open Standards Requirement" class="sf-depth-3">OSR Frequently Asked Questions</a></li></ul></li></ul></li><li id="menu-1842-1" class="middle odd sf-item-3 sf-depth-1 sf-total-children-3 sf-parent-children-2 sf-single-children-1 menuparent"><a href="/membership" title="Page for our various membership programs" class="sf-depth-1 menuparent">Membership</a><ul><li id="menu-914-1" class="first odd sf-item-1 sf-depth-2 sf-total-children-1 sf-parent-children-0 sf-single-children-1 menuparent"><a href="/members" class="sf-depth-2 menuparent">Individuals</a><ul><li id="menu-897-1" class="firstandlast odd sf-item-1 sf-depth-3 sf-no-children"><a href="/civicrm/contribute/transact?reset=1&amp;id=1" title="" class="sf-depth-3">Join</a></li></ul></li><li id="menu-675-1" class="middle even sf-item-2 sf-depth-2 sf-total-children-3 sf-parent-children-0 sf-single-children-3 menuparent"><a href="/affiliates" title="Home page for OSI&#039;s membership scheme for non-profits and not-for-profits" class="sf-depth-2 menuparent">Affiliates</a><ul><li id="menu-676-1" class="first odd sf-item-1 sf-depth-3 sf-no-children"><a href="/affiliates/about" title="" class="sf-depth-3">Become an Affiliate</a></li><li id="menu-677-1" class="middle even sf-item-2 sf-depth-3 sf-no-children"><a href="/affiliates/list" title="Full list of non-profits and not-for-profits affiliated to OSI" class="sf-depth-3">List of Affiliates</a></li><li id="menu-2071-1" class="last odd sf-item-3 sf-depth-3 sf-no-children"><a href="/AffiliateRequirements" class="sf-depth-3">Affiliate  Criteria</a></li></ul></li><li id="menu-1436-1" class="last odd sf-item-3 sf-depth-2 sf-no-children"><a href="/sponsors" class="sf-depth-2">Sponsors &amp; Support</a></li></ul></li><li id="menu-1841-1" class="middle even sf-item-4 sf-depth-1 sf-total-children-4 sf-parent-children-1 sf-single-children-3 menuparent"><a href="/community" title="Page for our various community members." class="sf-depth-1 menuparent">Community</a><ul><li id="menu-63-1" class="first odd sf-item-1 sf-depth-2 sf-total-children-4 sf-parent-children-0 sf-single-children-4 menuparent"><a href="/lists" title="The virtual committees where the OSI&#039;s work gets done" class="sf-depth-2 menuparent">Mailing lists</a><ul><li id="menu-78-1" class="first odd sf-item-1 sf-depth-3 sf-no-children"><a href="/codeofconduct" title="Guidelines for OSI Mailing Lists" class="sf-depth-3">General Code of Conduct</a></li><li id="menu-1072-1" class="middle even sf-item-2 sf-depth-3 sf-no-children"><a href="/codeofconduct/licensing" class="sf-depth-3">Licensing Code of Conduct</a></li><li id="menu-2111-1" class="middle odd sf-item-3 sf-depth-3 sf-no-children"><a href="/public_forums_disclaimer" class="sf-depth-3">Disclaimer for OSI Public Forums</a></li><li id="menu-2110-1" class="last even sf-item-4 sf-depth-3 sf-no-children"><a href="/public_archives_policy" class="sf-depth-3">Policy on Public Communications and Archives</a></li></ul></li><li id="menu-2032-1" class="middle even sf-item-2 sf-depth-2 sf-no-children"><a href="/volunteers" class="sf-depth-2">Volunteers</a></li><li id="menu-1846-1" class="middle odd sf-item-3 sf-depth-2 sf-no-children"><a href="http://wiki.opensource.org" title="" class="sf-depth-2">Wiki</a></li><li id="menu-1524-1" class="last even sf-item-4 sf-depth-2 sf-no-children"><a href="/store" class="sf-depth-2">OSI Store</a></li></ul></li><li id="menu-1840-1" class="middle odd sf-item-5 sf-depth-1 sf-total-children-5 sf-parent-children-1 sf-single-children-4 menuparent"><a href="/resources" title="Page offering resources to OSI personas" class="sf-depth-1 menuparent">Resources</a><ul><li id="menu-342-1" class="first odd sf-item-1 sf-depth-2 sf-no-children"><a href="/faq" title="Frequently Asked Questions about open source and about the OSI." class="sf-depth-2">FAQ</a></li><li id="menu-38-1" class="middle even sf-item-2 sf-depth-2 sf-no-children"><a href="/blog" title="A group blog / aggregation point for OSI Board Member blogs" class="sf-depth-2">OSI Board Blog</a></li><li id="menu-45-1" class="middle odd sf-item-3 sf-depth-2 sf-total-children-2 sf-parent-children-0 sf-single-children-2 menuparent"><a href="/help" title="Resources for questions and further exploration" class="sf-depth-2 menuparent">Getting Help</a><ul><li id="menu-76-1" class="first odd sf-item-1 sf-depth-3 sf-no-children"><a href="/links" title="Links and References to Open Source" class="sf-depth-3">Bibliography</a></li><li id="menu-125-1" class="last even sf-item-2 sf-depth-3 sf-no-children"><a href="/advocacy/case_for_business.php" title="How to advocate Open Source to businesses" class="sf-depth-3">Open Source Case for Business</a></li></ul></li><li id="menu-1514-1" class="middle even sf-item-4 sf-depth-2 sf-no-children"><a href="/working_groups" class="sf-depth-2">Working Groups</a></li><li id="menu-12-1" class="last odd sf-item-5 sf-depth-2 sf-no-children"><a href="/osi-open-source-education" title="OSI&#039;s Open Source Education Initiative and Activities" class="sf-depth-2">Open Source Education</a></li></ul></li><li id="menu-1844-1" class="last even sf-item-6 sf-depth-1 sf-total-children-2 sf-parent-children-0 sf-single-children-2 menuparent"><a href="/news" title="Page dedicated to the latest news and events." class="sf-depth-1 menuparent">News &amp; Events</a><ul><li id="menu-1845-1" class="first odd sf-item-1 sf-depth-2 sf-no-children"><a href="/newsletters" title="Index of newsletters" class="sf-depth-2">Newsletters</a></li><li id="menu-1999-1" class="last even sf-item-2 sf-depth-2 sf-no-children"><a href="/events" class="sf-depth-2">Events</a></li></ul></li></ul>  </div>
</div>
  </div>
                                            </nav>
                </div>
            </div>
        </div>
        <!-- EOF: #main-navigation-inside -->

    </div>
</div>
<!-- EOF: #main-navigation -->


<!-- #page -->
<div id="page" class="clearfix">
    
    
    <!-- #main-content -->
    <div id="main-content">
        <div class="container">
        
            <!-- #messages-console -->
                        <!-- EOF: #messages-console -->
            
            <div class="row">

                

                <section class="col-md-12">

                    <!-- #main -->
                    <div id="main" class="clearfix">
                    
                        
                        
                        <!-- EOF:#content-wrapper -->
                        <div id="content-wrapper">

                                                                                    <h1 class="page-title">The 3-Clause BSD License</h1>
                                                        
                                                  
                            <!-- #tabs -->
                                                            <div class="tabs">
                                                                </div>
                                                        <!-- EOF: #tabs -->

                            <!-- #action links -->
                                                        <!-- EOF: #action links -->

                              <div class="region region-content">
    <div id="block-system-main" class="block block-system clearfix">

    
  <div class="content">
    <article id="node-568" class="node node-page clearfix">

  
  <div class="content">
    <div class="field field-name-body field-type-text-with-summary field-label-hidden"><div class="field-items"><div class="field-item even"><p style="font-weight:bold">
SPDX short identifier: BSD-3-Clause
</p>

<div align="right">
<button onclick="myFunction()">Further resources on the <b>3-clause BSD license</b></button>

<p id="demo"></p>

<script>
<!--//--><![CDATA[// ><!--

function myFunction() {
    var x;
    if (confirm("Disclaimer: While the OSI acknowledges these as potentially helpful resources for the community, it does not endorse any content, contributors or license interpretations from these websites. Any links to these resources across opensource.org are solely for navigational purposes. The OSI does not promote or exclusively favor any of the mentioned resources, but instead provides them as separate third-party resource to help inform your opinion. Any content from or links to these resources are separate from the OSI, exist for purely informational purposes and creates no attorney-client relationship between you, the OSI or the resources. If you have questions about how licenses apply to you or your organization, you should seek legal advice. ") == true) {
        x = "<br><p>The following are other community resources that may be helpful:<br><br><a href=https://tldrlegal.com/license/bsd-3-clause-license-%28revised%29 style='font-weight: bold;'>The 3-clause BSD license on TLDRLegal<br><a href=http://www.gnu.org/licenses/license-list.en.html>GNU License List<br><a href=https://en.wikipedia.org/wiki/Comparison_of_free_and_open-source_software_licenses>Wikipedia License List<br><a href=http://oss-watch.ac.uk/apps/licdiff/>OSSWatch License Diff<br><a href=choosealicense.com>Choosealicense";
    } else {
        x = " ";
    }
    document.getElementById("demo").innerHTML = x;
}

//--><!]]>
</script></div> 

    
      <p><em>Note: This license has also been called the "New BSD License" or "Modified BSD License". See also the <a href="https://opensource.org/licenses/BSD-2-Clause">2-clause BSD License</a>.</em></p>

   
    <p>Copyright &lt;YEAR&gt; &lt;COPYRIGHT HOLDER&gt;<br /></p>

    <p>Redistribution and use in source and binary forms, with or without modification, are permitted provided that the following conditions are met:</p>

    <p>1. Redistributions of source code must retain the above copyright notice, this list of conditions and the following disclaimer.</p>
    <p>2. Redistributions in binary form must reproduce the above copyright notice, this list of conditions and the following disclaimer in the documentation and/or other materials provided with the distribution.</p>
    <p>3. Neither the name of the copyright holder nor the names of its contributors may be used to endorse or promote products derived from this software without specific prior written permission.</p>

    <p>THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.</p>
  </div></div></div>  </div>
    
     

  
</article>  </div>
</div>
<div id="block-popup-announcement-popup-announcement-1" class="block block-popup-announcement clearfix">

    
  <div class="content">
    <div style="display:none" id="popup-announcement-overlay"></div>
<div style="display:none" id="popup-announcement-wrap">
  <div id="popup-announcement">
    <center>
<h2><a href="https://opensource.org/donate">Please Donate to the OSI</a></h2>
<p>It's your contributions that make open source work...<br>
...<i>help us work for open source:</i><br>
<a href="https://opensource.org/donate"><b>DONATE TODAY!</b></a></p>
<p><font size="1">Your commitment and contributions to open source projects<br>have transformed software, the Internet, technology, business, government, and community.<br> We're raising $20,000 to extend your work, and expand open source for society—<a href="https://opensource.org/donate"><i>please commit</i></a>.</font></p>
</center>  </div>
  <div id="popup-announcement-close"></div>
</div>
  </div>
</div>
  </div>
                            
                        </div>
                        <!-- EOF:#content-wrapper -->

                    </div>
                    <!-- EOF:#main -->

                </section>

                        
            </div>

        </div>
    </div>
    <!-- EOF:#main-content -->

    
</div>
<!-- EOF:#page -->


<footer id="subfooter" class="clearfix">
    <div class="container">
        
        <!-- #subfooter-inside -->
        <div id="subfooter-inside" class="clearfix">
            <div class="row">
                <div class="col-md-12">
                    <!-- #subfooter-left -->
                    <div class="subfooter-area">
                                            

                                          <div class="region region-footer">
    <div id="block-block-11" class="block block-block clearfix">

    
  <div class="content">
    <div class="filler" style="vertical-align: middle; display: inline-block;">
<p style="margin: 0pt auto; display: table-cell; text-align: center; vertical-align: middle;">
<a href="https://twitter.com/OpenSourceOrg" style="margin: 0pt auto; display: table-cell; text-align: center; vertical-align: middle;"><img src="/files/twitterlogo.png" width="50" style="margin: 0pt auto; display: table-cell; text-align: center; vertical-align: middle;" /></a>

<a href="https://plus.google.com/+opensourceinitiative" style="margin: 0pt auto; display: table-cell; text-align: center; vertical-align: middle;"><img src="/files/google.png" width="50" style="margin: 0pt auto; display: table-cell; text-align: center; vertical-align: middle;" /></a>

<a href="https://www.linkedin.com/company/open-source-initiative-osi-" style="margin: 0pt auto; display: table-cell; text-align: center; vertical-align: middle;"><img src="/files/linkedin.png" width="50" style="margin: 0pt auto; display: table-cell; text-align: center; vertical-align: middle;" /></a>

<a href="http://wiki.opensource.org" style="margin: 0pt auto; display: table-cell; text-align: center; vertical-align: middle;"><img src="/files/xwikilogo.png" width="50" style="margin: 0pt auto; display: table-cell; text-align: center; vertical-align: middle;" /></a>

<a href="http://creativecommons.org/licenses/by/4.0/" style="margin: 0pt auto; display: table-cell; text-align: center; vertical-align: middle;"><img src="/files/ccby.png" width="50" style="margin: 0pt auto; display: table-cell; text-align: center; vertical-align: middle;" /></a>

<script id="fbwiuwz">
<!--//--><![CDATA[// ><!--
(function(i){var f,s=document.getElementById(i);f=document.createElement('iframe');f.src='//api.flattr.com/button/view/?uid=osi&url=http%3A%2F%2Fopensource.org';f.title='Flattr';f.height=70;f.width=70;f.style.borderWidth=0;s.parentNode.insertBefore(f,s);})('fbwiuwz');
//--><!]]>
</script></p>
</div>

<br /><div class="license" style="vertical-align: middle; display: inline-block;">
<p>
Opensource.org site content is licensed under a <a rel="license" href="http://creativecommons.org/licenses/by/4.0/">Creative Commons Attribution 4.0 International License</a>.
</p>
<p>
<a href="../ToS">Terms of Service</a>
</p>
</div>
  </div>
</div>
<div id="block-block-7" class="block block-block clearfix">

    
  <div class="content">
    <script src="https://www.google-analytics.com/urchin.js" type="text/javascript">
<!--//--><![CDATA[// ><!--


//--><!]]>
</script><script type="text/javascript">
<!--//--><![CDATA[// ><!--

_uacct = "**********-1";
urchinTracker();

//--><!]]>
</script>  </div>
</div>
  </div>
                    
                    </div>
                    <!-- EOF: #subfooter-left -->
                </div>
            </div>
        </div>
        <!-- EOF: #subfooter-inside -->
    
    </div>
</footer>
<!-- EOF:#subfooter -->
  </body>
</html>