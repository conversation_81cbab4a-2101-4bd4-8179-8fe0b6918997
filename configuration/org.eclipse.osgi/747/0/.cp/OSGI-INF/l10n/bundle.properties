# META-INF/MANIFEST.MF
Bundle-Name=Code Recommenders UI
Bundle-Vendor=Eclipse Code Recommenders

# plugin.xml
views.root.name=Code Recommenders
preferencePages.root.name=Code Recommenders
linkContribution.homepage.label=Visit the <a href="https://www.eclipse.org/recommenders/">Project Homepage</a>
linkContribution.manual.label=View the <a href="https://www.eclipse.org/recommenders/manual/">Project Manual</a>
linkContribution.marketplace.label=<a href="https://marketplace.eclipse.org/content/eclipse-code-recommenders">Favorite us</a> on the Eclipse Marketplace
linkContribution.twitter.label=<a href="https://twitter.com/recommenders">Follow us</a> on Twitter
linkContribution.extensionDiscovery.label=<a href="https://www.eclipse.org/downloads/download.php?r=1&file=/recommenders/discovery/2.x/directories/completion.xml">Discover new extensions</a> to Code Recommenders
linkContribution.forum.label=Ask a question on our <a href="https://www.eclipse.org/forums/index.php/f/211/">forum</a>
commands.extensionDiscovery.name=Discover New Extensions
commands.openBrowser.name=Open a Web browser
commands.openPreferences.name=Open the preferences dialog
commandParameter.uri.name=URI
support.recommenders.name=Code Recommenders
support.recommenders.description=Intelligent code completion, extended documentation, and Snipmatch
feed.ide.name=Code Recommenders News
