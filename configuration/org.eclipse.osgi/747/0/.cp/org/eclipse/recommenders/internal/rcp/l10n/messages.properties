PREFPAGE_DESCRIPTION_EMPTY=Expand the tree to edit preferences for a specific feature.

LOG_ERROR_ACTIVE_PAGE_FINDER_TOO_EARLY=Could not run active page finder that early.
LOG_ERROR_ARRAY_TYPE_IN_JAVA_ELEMENT_RESOLVER=An array type was passed to the JavaElementResolver: \u2018{0}\u2019.
LOG_ERROR_DIALOG_RESTART_NOT_POSSIBLE=Failed to restart Eclipse. \
 The Eclipse VM arguments could not be read.
LOG_ERROR_EXCEPTION_OCCURRED_IN_SERVICE_HOOK=Exception occurred in IRcpService hook {0}.
LOG_ERROR_EXCEPTION_WHILE_CHECKING_OFFSETS=Exception while checking editor offset.
LOG_ERROR_FAILED_TO_EXECUTE_COMMAND=Failed to execute command {0}.
LOG_ERROR_FAILED_TO_FIND_FIELDS_FOR_TYPE=Failed to find fields for type \u2018{0}\u2019.
LOG_ERROR_FAILED_TO_FIND_METHODS_FOR_TYPE=Failed to find methods for type \u2018{0}\u2019.
LOG_ERROR_FAILED_TO_FIND_OVERRIDDEN_METHOD_OF_METHOD=Failed to find an overridden method of \u2018{0}\u2019.\u2018{1}\u2019.
LOG_ERROR_FAILED_TO_FIND_SUPERTYPE_NAME_OF_TYPE=Failed to find a type name for the supertype of the type \u2018{0}\u2019.
LOG_ERROR_FAILED_TO_FIND_SUPERTYPE_OF_TYPE=Failed to find a supertype of the type \u2018{0}\u2019.
LOG_ERROR_FAILED_TO_FIND_TYPE_FROM_SIGNATURE=Failed to find a type from the signature \u2018{0}\u2019.
LOG_ERROR_FAILED_TO_FIND_TYPE_HIERARCHY_OF_TYPE=Failed to find the type hierarchy of the type \u2018{0}\u2019.
LOG_ERROR_FAILED_TO_FIND_TYPE_OF_FIELD=Failed to find the type of the field \u2018{0}\u2019.\u2018{1}\u2019.
LOG_ERROR_FAILED_TO_GENERATE_UUID=Failed to generate UUID from MAC address.
LOG_ERROR_FAILED_TO_READ_EXTENSION_ATTRIBUTE=Failed to read extension attribute \u2018{0}\u2019: {1}.
LOG_ERROR_FAILED_TO_RESOLVE_METHOD=Failed to resolve JDT method \u2018{0}\u2019: {1}.
LOG_ERROR_FAILED_TO_RESOLVE_SELECTION=Failed to resolve selection in \u2018{0}\u2019 at offset {1}.
LOG_ERROR_FAILED_TO_RESOLVE_UNQUALIFIED_JDT_TYPE=Failed to resolve unqualified JDT type with parent \u2018{0}\u2019 and signature \u2018{1}\u2019.
LOG_ERROR_FAILED_TO_RESOLVE_UNQUALIFIED_TYPE_NAME=Failed to resolve unqualified type name \u2018{0}\u2019 (type: {1}).
LOG_ERROR_FAILED_TO_RESOLVE_TYPE_PARAMETER=Failed to resolve type parameter \u2018{0}\u2019.
LOG_ERROR_PREFERENCES_NOT_SAVED=The preferences could not be saved.

LOG_WARNING_ERROR_WHILE_PARSING_NEWS_FEED=Failed to parse news feed
LOG_WARNING_ERROR_WHILE_PARSING_NEWS_FEED_ITEM=Failed to parse news feed item

UNKNOWN_TYPE=unknown type

DIALOG_TITLE_BUNDLE_RESOLUTION_FAILURE=Code Recommenders installation problems
DIALOG_MESSAGE_BUNDLE_RESOLUTION_FAILURE=Eclipse Code Recommenders is experiencing installation problems. \
 Not all of its bundles could be resolved. \
 Please update to the latest version of Code Recommenders. \
 Also, restarting Eclipse with the \u2018clean\u2019 parameter may fix this problem. \
 Do you want to restart now?
DIALOG_LABEL_BUNDLE_LIST=Unresolved Bundles:
DIALOG_TOGGLE_IGNORE_BUNDLE_RESOLUTION_FAILURES=Silently ignore future bundle resolution failures.
DIALOG_MESSAGE_BUNDLE_RESOLUTION_FAQ=See our <a href="{0}">FAQ</a> for more details.
DIALOG_MESSAGE_BUNDLE_RESOLUTION_FILE_A_BUG=If this problem persists, please <a href="{0}">file a bug</a> with the Eclipse Code Recommenders project.
DIALOG_BUTTON_RESTART=Restart

NEWS_NOTIFY_MESSAGE=The Code Recommenders project has published a new blog post: {0}. <a href="{1}">Read more\u2026</a>
NEWS_LOADING_MESSAGE=Loading Project Newsfeed\u2026
NEWS_TURN_OFF_MESSAGE=Click <a>here</a> to turn off news notifications.

LABEL_NOTIFICATION_NAME=Notification

JOB_NAME_CLOSE=Close Job
JOB_NAME_INITIALIZING_PROJECTS=Initializing Java projects for use with Code Recommenders
JOB_NAME_FADE=Fading
JOB_NAME_SELECTION_LISTENER_REGISTRATION=Registering workbench selection listener.
