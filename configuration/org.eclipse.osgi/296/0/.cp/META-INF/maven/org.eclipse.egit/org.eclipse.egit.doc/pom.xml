<?xml version="1.0" encoding="UTF-8"?>
<!--
   Copyright (C) 2010, <PERSON> <<EMAIL>>

   All rights reserved. This program and the accompanying materials
   are made available under the terms of the Eclipse Public License v1.0
   which accompanies this distribution, and is available at
   http://www.eclipse.org/legal/epl-v10.html
-->

<project xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.eclipse.egit</groupId>
    <artifactId>egit-parent</artifactId>
    <version>4.9.2.201712150930-r</version>
  </parent>

  <artifactId>org.eclipse.egit.doc</artifactId>
  <packaging>eclipse-plugin</packaging>

  <name>Git Team Provider (Documentation)</name>

  <build>
    <plugins>
      <plugin>
        <artifactId>maven-antrun-plugin</artifactId>
          <dependencies>
            <dependency>
              <groupId>ant</groupId>
              <artifactId>optional</artifactId>
              <version>1.5.4</version>
            </dependency>
            <dependency>
              <groupId>org.eclipse.mylyn.docs</groupId>
              <artifactId>org.eclipse.mylyn.wikitext.ant</artifactId>
              <version>3.0.9</version>
            </dependency>
            <dependency>
              <groupId>org.eclipse.mylyn.docs</groupId>
              <artifactId>org.eclipse.mylyn.wikitext.mediawiki.ant</artifactId>
              <version>3.0.9</version>
            </dependency>
          </dependencies>
        <executions>
          <execution>
            <phase>generate-sources</phase>
            <configuration>
                <target if="update.egit.doc">
                <ant target="all" inheritRefs="true" antfile="build-help.xml"/>
              </target>
            </configuration>
            <goals>
              <goal>run</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
</project>
