<?xml version='1.0' encoding='utf-8' ?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
		<title>JGit User Guide - Snippets</title>
		<link type="text/css" rel="stylesheet" href="../../book.css"/>
	</head>
	<body>
		<table class="navigation" style="width: 100%;" border="0" summary="navigation">
			<tr>
				<th style="width: 100%" align="center" colspan="3">Snippets</th>
			</tr>
			<tr>
				<td style="width: 20%" align="left">
					<a href="Reference.html" title="Reference">
						<img alt="Previous" border="0" src="../../images/prev.gif"/>
					</a>
				</td>
				<td style="width: 60%" align="center"></td>
				<td style="width: 20%" align="right">
					<a href="Advanced-Topics.html" title="Advanced Topics">
						<img alt="Next" border="0" src="../../images/next.gif"/>
					</a>
				</td>
			</tr>
			<tr>
				<td style="width: 20%" align="left" valign="top">Reference</td>
				<td style="width: 60%" align="center"></td>
				<td style="width: 20%" align="right" valign="top">Advanced Topics</td>
			</tr>
		</table><hr class="navigation-separator"/>
		<h1 id="Snippets">Snippets</h1>
		<h2 id="Finding_children_of_a_commit">Finding children of a commit</h2>
		<pre>
PlotWalk revWalk = new PlotWalk(repo());
ObjectId rootId = (branch==null)?repo().resolve(HEAD):branch.getObjectId();
RevCommit root = revWalk.parseCommit(rootId);
revWalk.markStart(root);
PlotCommitList&lt;PlotLane&gt; plotCommitList = new PlotCommitList&lt;PlotLane&gt;();
plotCommitList.source(revWalk);
plotCommitList.fillTo(Integer.MAX_VALUE);
return revWalk;
</pre>
		<h2 id="Snippet_Collection">Snippet Collection</h2>
		<p>There is a collection of ready-to-run JGit code snippets available at 
			<a href="https://github.com/centic9/jgit-cookbook" target="egit_external">https://github.com/centic9/jgit-cookbook</a>
		</p><hr class="navigation-separator"/>
		<table class="navigation" style="width: 100%;" border="0" summary="navigation">
			<tr>
				<td style="width: 20%" align="left">
					<a href="Reference.html" title="Reference">
						<img alt="Previous" border="0" src="../../images/prev.gif"/>
					</a>
				</td>
				<td style="width: 60%" align="center">
					<a href="User-Guide.html" title="JGit User Guide">
						<img alt="JGit User Guide" border="0" src="../../images/home.gif"/>
					</a>
				</td>
				<td style="width: 20%" align="right">
					<a href="Advanced-Topics.html" title="Advanced Topics">
						<img alt="Next" border="0" src="../../images/next.gif"/>
					</a>
				</td>
			</tr>
			<tr>
				<td style="width: 20%" align="left" valign="top">Reference</td>
				<td style="width: 60%" align="center"></td>
				<td style="width: 20%" align="right" valign="top">Advanced Topics</td>
			</tr>
		</table>
	</body>
</html>