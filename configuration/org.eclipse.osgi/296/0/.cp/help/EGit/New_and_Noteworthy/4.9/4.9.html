<?xml version='1.0' encoding='utf-8' ?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
		<title>EGit 4.9 New and Noteworthy</title>
		<link type="text/css" rel="stylesheet" href="../../../book.css"/>
	</head>
	<body>
		<table class="navigation" style="width: 100%;" border="0" summary="navigation">
			<tr>
				<th style="width: 100%" align="center" colspan="3">EGit 4.9 New and Noteworthy</th>
			</tr>
			<tr>
				<td style="width: 20%" align="left"></td>
				<td style="width: 60%" align="center"></td>
				<td style="width: 20%" align="right">
					<a href="Contributors.html" title="Contributors">
						<img alt="Next" border="0" src="../../../images/next.gif"/>
					</a>
				</td>
			</tr>
			<tr>
				<td style="width: 20%" align="left" valign="top"></td>
				<td style="width: 60%" align="center"></td>
				<td style="width: 20%" align="right" valign="top">Contributors</td>
			</tr>
		</table><hr class="navigation-separator"/>
		<h1 id="EGit">EGit</h1>
		<h2 id="Features">Features</h2>
		<ul>
			<li>Adapt NetUtil.setSslVerification to honor http.&lt;url&gt;.sslVerify</li>
			<li>Log warnings about invalid git config entries</li>
			<li>Log warnings about invalid RefSpecs in git config files</li>
			<li>Add ICommitMessageProvider2 for caret positioning in commit messages (Bug 516867)</li>
		</ul>
		<h2 id="Usability">Usability</h2>
		<ul>
			<li>Enable editing RefSpec on double click</li>
			<li>Trim history search pattern</li>
			<li>Added context actions 'Assume Unchanged' and 'Untrack' in staging view</li>
			<li>Improve commit message validation &amp; remove leading whitespace</li>
			<li>Remove trailing whitespace from commit messages</li>
			<li>Use verbs for button labels in UnmergedBranchDialog</li>
			<li>Detached HEAD dialog should use verbs (close) instead of OK</li>
			<li>Commit is not possible should use verbs instead of OK</li>
			<li>Use verbs in RebaseResultDialog instead of OK 
				<a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=518467" target="egit_external">bug 518467</a>
			</li>
			<li>New toolbar button to switch repositories in git views. This enables the user to quickly switch the staging view, the reflog view, the rebase interactive view, or the history view from one repository to another via a toolbar button with a drop-down menu listing all currently configured git repositories. (Bug 518607)</li>
			<li>Add descriptive message to directory browsers</li>
			<li>Set window title in smart import wizard</li>
			<li>Make EGit text viewers react to font and color preference changes 
				<a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=484097" target="egit_external">bug 484097</a>
			</li>
			<li>Improve hard-wrapping in commit messages to properly show long URLs 
				<a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=519012" target="egit_external">bug 519012</a>
			</li>
			<li>FetchGerritChangePage: supply patch set number in the background 
				<a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=518492" target="egit_external">bug 518492</a>
			</li>
			<li>FetchGerritChangePage: auto-supply highest patch set number 
				<a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=518492" target="egit_external">bug 518492</a>
			</li>
			<li>Use "Check Out" for the default button in checkout confirmation dialog 
				<a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=511691" target="egit_external">bug 511691</a>
			</li>
			<li>Improve texts shown for the copy command</li>
			<li>FetchGerritChangePage: also try to determine the patch set number 
				<a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=518492" target="egit_external">bug 518492</a>
			</li>
			<li>FetchGerritChangePage: validate change ref a little more strictly</li>
			<li>FetchGerritChangePage: validate change ref against advertised refs</li>
			<li>FetchGerritChangePage: improve content assist 
				<a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=518495" target="egit_external">bug 518495</a>
			</li>
			<li>Use verbs in PushResultDialog instead of OK 
				<a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=518044" target="egit_external">bug 518044</a>
			</li>
			<li>Staging view: add toolbar buttons to stage/unstage all files 
				<a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=518213" target="egit_external">bug 518213</a>
			</li>
			<li>Contextual actions in tooltips in staging view. If there are selected elements in the staged/unstaged viewers, provide quick access to the "Add to index"/"Remove from index" actions via a toolbar shown in a tooltip when the mouse pointer hovers over a selected element. 
				<a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=516969" target="egit_external">bug 516969</a>
			</li>
			<li>Change the icons for clone, add, and create repositories 
				<a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=347081" target="egit_external">bug 347081</a>
			</li>
			<li>Icon clean-up: use PNG, provide HiDPI versions, include SVG sources 
				<a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=517169" target="egit_external">bug 517169</a>, 
				<a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=517171" target="egit_external">bug 517171</a>
			</li>
			<li>Improve Decoration Support for ResourceMappings 
				<a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=498546" target="egit_external">bug 498546</a>
			</li>
		</ul>
		<h2 id="Performance_Improvements">Performance Improvements</h2>
		<ul>
			<li>Reduce allocations in git decorator 
				<a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=500106" target="egit_external">bug 500106</a>
			</li>
		</ul>
		<h2 id="Bug_Fixes">Bug Fixes</h2>
		<p>
			<a href="https://bugs.eclipse.org/bugs/buglist.cgi?resolution=FIXED&amp;resolution=DUPLICATE&amp;classification=Technology&amp;list_id=10006180&amp;order=Importance&amp;product=EGit&amp;query_format=advanced&amp;target_milestone=4.9.0" target="egit_external"> 30 Bugs and 7 enhancement requests</a> were closed
		</p>
		<ul>
			<li>Fix NPE in ConnectProviderOperation 
				<a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=522423" target="egit_external">bug 522423</a>
			</li>
			<li>Fix NotHandledException for "org.eclipse.egit.ui.RepositoriesViewOpen" 
				<a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=521824" target="egit_external">bug 521824</a>
			</li>
			<li>Prevent NPE below RemoveOrDeleteRepositoryCommand 
				<a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=521826" target="egit_external">bug 521826</a>
			</li>
			<li>GitHistoryPage: search widget must not react to font changes</li>
			<li>Update submodule nodes when submodule refs change 
				<a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=520694" target="egit_external">bug 520694</a>
			</li>
			<li>Correct listeners in IndexDiffCacheEntry 
				<a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=520694" target="egit_external">bug 520694</a>
			</li>
			<li>Prevent NPE in LaunchFinder 
				<a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=520033" target="egit_external">bug 520033</a>
			</li>
			<li>Adds mnemonic to "Select" button in the "Create Branch" Dialog 
				<a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=517776" target="egit_external">bug 517776</a>
			</li>
			<li>Ensure DiffFormatter is closed in CreatePatchOperation</li>
			<li>Restrict resizing of the "Fetch from Gerrit" Dialog 
				<a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=519335" target="egit_external">bug 519335</a>
			</li>
			<li>Disable delete config section button in the configuration preference page for no selection</li>
			<li>Reflog view: serialize asynchronous loading jobs 
				<a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=519431" target="egit_external">bug 519431</a>
			</li>
			<li>Fix alignment of buttons in git config preference pages</li>
			<li>Git history: preference-based actions must run in the UI thread.</li>
			<li>Fix history view showing only commit IDs sometimes 
				<a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=519104" target="egit_external">bug 519104</a>
			</li>
			<li>Correct synchronize info calculation for folders 
				<a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=518646" target="egit_external">bug 518646</a>
			</li>
			<li>Staging View tooltips: work around bug 330384 on GTK 
				<a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=516969" target="egit_external">bug 516969</a>
			</li>
			<li>Fix the icon for the PullWithOptions command</li>
		</ul>
		<h2 id="Build_and_Release_Engineering">Build and Release Engineering</h2>
		<ul>
			<li>Add org.apache.commons.codec 1.9.0 to target platform</li>
			<li>Update args4j to 2.33</li>
			<li>Remove mylyn "latest" p2 repository from target platform</li>
			<li>Remove easymport p2 repository from Mars target platform</li>
			<li>Remove Mylyn wikitext from target platforms</li>
			<li>Remove unnecessary runtime dependency on wikitext for doc bundle (Bug 519248)</li>
			<li>Update Mylyn Wikitext update site URL</li>
			<li>Oomph setup: include TPD and AnyEditTools</li>
			<li>Update Oomph setup</li>
			<li>Update mylyn github feature to require egit from same release</li>
		</ul><hr class="navigation-separator"/>
		<table class="navigation" style="width: 100%;" border="0" summary="navigation">
			<tr>
				<td style="width: 20%" align="left"></td>
				<td style="width: 60%" align="center"></td>
				<td style="width: 20%" align="right">
					<a href="Contributors.html" title="Contributors">
						<img alt="Next" border="0" src="../../../images/next.gif"/>
					</a>
				</td>
			</tr>
			<tr>
				<td style="width: 20%" align="left" valign="top"></td>
				<td style="width: 60%" align="center"></td>
				<td style="width: 20%" align="right" valign="top">Contributors</td>
			</tr>
		</table>
	</body>
</html>