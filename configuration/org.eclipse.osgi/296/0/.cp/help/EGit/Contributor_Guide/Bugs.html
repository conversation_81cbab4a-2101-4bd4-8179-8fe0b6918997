<?xml version='1.0' encoding='utf-8' ?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
		<title>EGit Contributor Guide - Bugs</title>
		<link type="text/css" rel="stylesheet" href="../../book.css"/>
	</head>
	<body>
		<table class="navigation" style="width: 100%;" border="0" summary="navigation">
			<tr>
				<th style="width: 100%" align="center" colspan="3">Bugs</th>
			</tr>
			<tr>
				<td style="width: 20%" align="left">
					<a href="Tests.html" title="Tests">
						<img alt="Previous" border="0" src="../../images/prev.gif"/>
					</a>
				</td>
				<td style="width: 60%" align="center"></td>
				<td style="width: 20%" align="right">
					<a href="Website.html" title="Website">
						<img alt="Next" border="0" src="../../images/next.gif"/>
					</a>
				</td>
			</tr>
			<tr>
				<td style="width: 20%" align="left" valign="top">Tests</td>
				<td style="width: 60%" align="center"></td>
				<td style="width: 20%" align="right" valign="top">Website</td>
			</tr>
		</table><hr class="navigation-separator"/>
		<h1 id="Bugs">Bugs</h1>
		<p>If you are looking for bugs/enhancements to start contributing, they have the keyword "helpwanted" or "bugday":</p>
		<p>
			<a href="https://bugs.eclipse.org/bugs/buglist.cgi?keywords=helpwanted%2C%20bugday%2C%20&amp;keywords_type=anywords&amp;list_id=7364111&amp;resolution=---&amp;query_format=advanced&amp;product=EGit" target="egit_external">EGit bugs with helpwanted or bugday</a>
		</p>
		<p>
			<a href="https://bugs.eclipse.org/bugs/buglist.cgi?keywords=helpwanted%2C%20bugday%2C%20&amp;keywords_type=anywords&amp;list_id=8951656&amp;product=JGit&amp;query_format=advanced&amp;resolution=---" target="egit_external">JGit bugs with helpwanted or bugday</a>
		</p>
		<h2 id="Links">Links</h2>
		<h3 id="Filing_Bugs">Filing Bugs</h3>
		<h4 id="How_to_file_bugs">How to file bugs</h4>
		<ul>
			<li>
				<a href="http://wiki.eclipse.org/FAQ_How_do_I_report_a_bug_in_Eclipse%3F" title="FAQ_How_do_I_report_a_bug_in_Eclipse%3F" target="egit_external">How do I report a bug in Eclipse?</a> 
			</li>
			<li>
				<a href="https://bugs.eclipse.org/bugs/page.cgi?id=bug-writing.html" target="egit_external">Bug Writing Guidelines</a>
			</li>
			<li>
				<a href="http://www.chiark.greenend.org.uk/~sgtatham/bugs.html" target="egit_external">How to Report Bugs Effectively</a> by Simon Tatham
			</li>
		</ul>
		<h4 id="File_a_bug">File a bug</h4>
		<ul>
			<li>
				<a href="https://bugs.eclipse.org/bugs/enter_bug.cgi?product=EGit&amp;rep_platform=All&amp;op_sys=All" target="egit_external">File a bug for EGit</a>
			</li>
			<li>
				<a href="https://bugs.eclipse.org/bugs/enter_bug.cgi?product=JGit&amp;rep_platform=All&amp;op_sys=All" target="egit_external">File a bug for JGit</a>
			</li>
		</ul>
		<h3 id="Bug_Reports_and_Links">Bug Reports and Links</h3>
		<table border="1" cellpadding="3" cellspacing="0">
			<tr>
				<th>Trends (bugs and enhancements)</th>
				<th>EGit </th>
				<th>JGit</th>
			</tr>
			<tr>
				<td>Open by component (date range editable)</td>
				<td>
					<a href="https://bugs.eclipse.org/bugs/chart.cgi?category=EGit&amp;datefrom=2011-01-01&amp;dateto=&amp;gt=1&amp;label0=EGit%20Core%20Open&amp;label1=EGit%20UI%20Open&amp;labelgt=Grand%20Total&amp;line0=1480&amp;line1=1478&amp;name=1478&amp;subcategory=UI&amp;action=wrap&amp;width=1000&amp;height=500" target="egit_external">Open</a> 
				</td>
				<td>
					<a href="https://bugs.eclipse.org/bugs/chart.cgi?category=JGit&amp;datefrom=2011-01-01&amp;dateto=&amp;label0=JGit%20Open&amp;line0=1592&amp;name=1592&amp;subcategory=JGit&amp;action=wrap&amp;width=1000&amp;height=500" target="egit_external">Open</a>
				</td>
			</tr>
			<tr>
				<td>Open by status </td>
				<td>
					<a href="https://bugs.eclipse.org/bugs/reports.cgi?product=EGit&amp;datasets=NEW&amp;datasets=REOPENED&amp;datasets=UNCONFIRMED&amp;datasets=ASSIGNED" target="egit_external">Open</a>
				</td>
				<td>
					<a href="https://bugs.eclipse.org/bugs/reports.cgi?product=JGit&amp;datasets=NEW&amp;datasets=REOPENED&amp;datasets=UNCONFIRMED&amp;datasets=ASSIGNED" target="egit_external">Open</a>
				</td>
			</tr>
			<tr>
				<td>Assigned </td>
				<td>
					<a href="https://bugs.eclipse.org/bugs/reports.cgi?product=EGit&amp;datasets=ASSIGNED" target="egit_external">Assigned</a> 
				</td>
				<td>
					<a href="https://bugs.eclipse.org/bugs/reports.cgi?product=JGit&amp;datasets=ASSIGNED" target="egit_external">Assigned</a>
				</td>
			</tr>
			<tr>
				<td>Open and closed by status </td>
				<td>
					<a href="https://bugs.eclipse.org/bugs/reports.cgi?product=EGit&amp;datasets=NEW&amp;datasets=REOPENED&amp;datasets=UNCONFIRMED&amp;datasets=VERIFIED&amp;datasets=CLOSED&amp;datasets=RESOLVED" target="egit_external">All</a>
				</td>
				<td>
					<a href="https://bugs.eclipse.org/bugs/reports.cgi?product=JGit&amp;datasets=NEW&amp;datasets=REOPENED&amp;datasets=UNCONFIRMED&amp;datasets=VERIFIED&amp;datasets=CLOSED&amp;datasets=RESOLVED" target="egit_external">All</a>
				</td>
			</tr>
			<tr>
				<th>Lists</th>
				<th>EGit</th>
				<th>JGit</th>
			</tr>
			<tr>
				<td><span style="color:red">Unresolved for passed target milestones</span> </td>
				<td>
					<a href="https://bugs.eclipse.org/bugs/buglist.cgi?f1=OP&amp;list_id=7727637&amp;f0=OP&amp;classification=Technology&amp;f4=CP&amp;query_format=advanced&amp;j1=OR&amp;f3=CP&amp;f2=everconfirmed&amp;bug_status=UNCONFIRMED&amp;bug_status=NEW&amp;bug_status=ASSIGNED&amp;bug_status=REOPENED&amp;product=EGit&amp;target_milestone=0.10.0&amp;target_milestone=0.10.0-M1&amp;target_milestone=0.10.0-M2&amp;target_milestone=0.10.0-M3&amp;target_milestone=0.11&amp;target_milestone=0.11-M1&amp;target_milestone=0.11-M2&amp;target_milestone=0.12&amp;target_milestone=0.12-M1&amp;target_milestone=0.12-M2&amp;target_milestone=0.6.0-M1&amp;target_milestone=0.6.0-M2&amp;target_milestone=0.6.0-M3&amp;target_milestone=0.7.0&amp;target_milestone=0.8.0&amp;target_milestone=0.9.0&amp;target_milestone=0.9.0-M1&amp;target_milestone=0.9.0-M2&amp;target_milestone=0.9.0-M3&amp;target_milestone=1.0.0&amp;target_milestone=1.1&amp;target_milestone=1.1-M1&amp;target_milestone=1.1-M2&amp;target_milestone=1.1-M3&amp;target_milestone=1.2&amp;target_milestone=1.2-M1&amp;target_milestone=1.2-M2&amp;target_milestone=1.3&amp;target_milestone=1.3-M1&amp;target_milestone=2.0&amp;target_milestone=2.0-M1&amp;target_milestone=2.0-M2&amp;target_milestone=2.1&amp;target_milestone=2.1-M1&amp;target_milestone=2.2&amp;target_milestone=2.2-M1&amp;target_milestone=2.2-M2&amp;target_milestone=2.3&amp;target_milestone=2.4&amp;target_milestone=3.0&amp;target_milestone=3.0.1&amp;target_milestone=3.0.2&amp;target_milestone=3.1&amp;target_milestone=3.2" target="egit_external">Open</a>
				</td>
				<td>
					<a href="https://bugs.eclipse.org/bugs/buglist.cgi?f1=OP&amp;list_id=7727591&amp;f0=OP&amp;classification=Technology&amp;f4=CP&amp;query_format=advanced&amp;j1=OR&amp;f3=CP&amp;f2=everconfirmed&amp;bug_status=UNCONFIRMED&amp;bug_status=NEW&amp;bug_status=ASSIGNED&amp;bug_status=REOPENED&amp;product=JGit&amp;target_milestone=0.10.0&amp;target_milestone=0.10.0-M1&amp;target_milestone=0.10.0-M2&amp;target_milestone=0.10.0-M3&amp;target_milestone=0.11&amp;target_milestone=0.11-M1&amp;target_milestone=0.11-M2&amp;target_milestone=0.12&amp;target_milestone=0.12-M1&amp;target_milestone=0.12-M2&amp;target_milestone=0.6.0&amp;target_milestone=0.7.0&amp;target_milestone=0.8.0&amp;target_milestone=0.9.0&amp;target_milestone=0.9.0-M1&amp;target_milestone=0.9.0-M2&amp;target_milestone=0.9.0-M3&amp;target_milestone=1.0.0&amp;target_milestone=1.1&amp;target_milestone=1.1-M1&amp;target_milestone=1.1-M2&amp;target_milestone=1.1-M3&amp;target_milestone=1.2&amp;target_milestone=1.2-M1&amp;target_milestone=1.2-M2&amp;target_milestone=1.3&amp;target_milestone=1.3-M1&amp;target_milestone=2.0&amp;target_milestone=2.0-M1&amp;target_milestone=2.0-M2&amp;target_milestone=2.1&amp;target_milestone=2.1-M1&amp;target_milestone=2.2&amp;target_milestone=2.2-M1&amp;target_milestone=2.2-M2&amp;target_milestone=2.3&amp;target_milestone=2.4&amp;target_milestone=3.0&amp;target_milestone=3.0.2&amp;target_milestone=3.1&amp;target_milestone=3.2" target="egit_external">Open</a>
				</td>
			</tr>
			<tr>
				<td>Open bugs</td>
				<td>
					<a href="https://bugs.eclipse.org/bugs/buglist.cgi?product=EGit&amp;bug_status=NEW&amp;bug_status=REOPENED&amp;bug_status=UNCONFIRMED&amp;bug_status=ASSIGNED&amp;bug_severity=blocker&amp;bug_severity=critical&amp;bug_severity=major&amp;bug_severity=normal&amp;bug_severity=minor&amp;bug_severity=trivial&amp;order=bug_severity" target="egit_external">Open</a>
				</td>
				<td>
					<a href="https://bugs.eclipse.org/bugs/buglist.cgi?product=JGit&amp;bug_status=NEW&amp;bug_status=REOPENED&amp;bug_status=UNCONFIRMED&amp;bug_status=ASSIGNED&amp;bug_severity=blocker&amp;bug_severity=critical&amp;bug_severity=major&amp;bug_severity=normal&amp;bug_severity=minor&amp;bug_severity=trivial&amp;order=bug_severity" target="egit_external">Open</a>
				</td>
			</tr>
			<tr>
				<td>Open enhancements</td>
				<td>
					<a href="https://bugs.eclipse.org/bugs/buglist.cgi?product=EGit&amp;bug_status=NEW&amp;bug_status=REOPENED&amp;bug_status=UNCONFIRMED&amp;bug_status=ASSIGNED&amp;bug_severity=enhancement&amp;order=bug_severity" target="egit_external">Open</a>
				</td>
				<td>
					<a href="https://bugs.eclipse.org/bugs/buglist.cgi?product=JGit&amp;bug_status=NEW&amp;bug_status=REOPENED&amp;bug_status=UNCONFIRMED&amp;bug_status=ASSIGNED&amp;bug_severity=enhancement&amp;order=bug_severity" target="egit_external">Open</a>
				</td>
			</tr>
			<tr>
				<td>Bugs with votes</td>
				<td>
					<a href="https://bugs.eclipse.org/bugs/buglist.cgi?f1=votes&amp;list_id=2849777&amp;columnlist=votes%2Cproduct%2Ccomponent%2Cassigned_to%2Cbug_status%2Cresolution%2Cshort_desc%2Cchangeddate&amp;o1=greaterthan&amp;resolution=---&amp;v1=1&amp;classification=Technology&amp;query_format=advanced&amp;product=EGit" target="egit_external">With Votes</a>
				</td>
				<td>
					<a href="https://bugs.eclipse.org/bugs/buglist.cgi?f1=votes&amp;list_id=2849777&amp;columnlist=votes%2Cproduct%2Ccomponent%2Cassigned_to%2Cbug_status%2Cresolution%2Cshort_desc%2Cchangeddate&amp;o1=greaterthan&amp;resolution=---&amp;v1=1&amp;classification=Technology&amp;query_format=advanced&amp;product=JGit" target="egit_external">With Votes</a>
				</td>
			</tr>
			<tr>
				<td>Assigned bugs and enhancements </td>
				<td>
					<a href="https://bugs.eclipse.org/bugs/buglist.cgi?product=EGit&amp;bug_status=ASSIGNED&amp;order=bug_severity" target="egit_external">Assigned</a>
				</td>
				<td>
					<a href="https://bugs.eclipse.org/bugs/buglist.cgi?product=JGit&amp;bug_status=ASSIGNED&amp;order=bug_severity" target="egit_external">Assigned</a>
				</td>
			</tr>
			<tr>
				<th>Reports</th>
				<th>EGit and JGit</th>
			</tr>
			<tr>
				<td>Open EGit and JGit bugs</td>
				<td>
					<a href="https://bugs.eclipse.org/bugs/report.cgi?y_axis_field=bug_status&amp;cumulate=1&amp;format=bar&amp;x_axis_field=product&amp;query_format=report-graph&amp;short_desc_type=allwordssubstr&amp;product=EGit&amp;product=JGit&amp;longdesc_type=allwordssubstr&amp;bug_file_loc_type=allwordssubstr&amp;status_whiteboard_type=allwordssubstr&amp;keywords_type=allwords&amp;bug_status=UNCONFIRMED&amp;bug_status=NEW&amp;bug_status=REOPENED&amp;bug_status=ASSIGNED&amp;bug_severity=blocker&amp;bug_severity=critical&amp;bug_severity=major&amp;bug_severity=normal&amp;bug_severity=minor&amp;bug_severity=trivial&amp;emailtype1=substring&amp;emailtype2=substring&amp;bug_id_type=anyexact&amp;chfieldto=Now&amp;action=wrap&amp;field0-0-0=noop&amp;type0-0-0=noop" target="egit_external">Open</a>
				</td>
			</tr>
			<tr>
				<td>Assigned EGit and JGit bugs</td>
				<td>
					<a href="https://bugs.eclipse.org/bugs/report.cgi?y_axis_field=bug_status&amp;cumulate=1&amp;format=bar&amp;x_axis_field=product&amp;query_format=report-graph&amp;short_desc_type=allwordssubstr&amp;product=EGit&amp;product=JGit&amp;longdesc_type=allwordssubstr&amp;bug_file_loc_type=allwordssubstr&amp;status_whiteboard_type=allwordssubstr&amp;keywords_type=allwords&amp;bug_status=ASSIGNED&amp;bug_severity=blocker&amp;bug_severity=critical&amp;bug_severity=major&amp;bug_severity=normal&amp;bug_severity=minor&amp;bug_severity=trivial&amp;emailtype1=substring&amp;emailtype2=substring&amp;bug_id_type=anyexact&amp;chfieldto=Now&amp;action=wrap&amp;field0-0-0=noop&amp;type0-0-0=noop" target="egit_external">Assigned</a>
				</td>
			</tr>
			<tr>
				<td>New bugs opened</td>
				<td>
					<a href="https://bugs.eclipse.org/bugs/report.cgi?x_axis_field=bug_status&amp;y_axis_field=product&amp;z_axis_field=&amp;query_format=report-table&amp;short_desc_type=allwordssubstr&amp;short_desc=&amp;classification=Technology&amp;product=EGit&amp;product=JGit&amp;longdesc_type=allwordssubstr&amp;longdesc=&amp;bug_file_loc_type=allwordssubstr&amp;bug_file_loc=&amp;status_whiteboard_type=allwordssubstr&amp;status_whiteboard=&amp;keywords_type=allwords&amp;keywords=&amp;bug_severity=blocker&amp;bug_severity=critical&amp;bug_severity=major&amp;bug_severity=normal&amp;bug_severity=minor&amp;bug_severity=trivial&amp;emailtype1=substring&amp;email1=&amp;emailtype2=substring&amp;email2=&amp;bug_id_type=anyexact&amp;bug_id=&amp;votes=&amp;chfieldfrom=-1d&amp;chfieldto=Now&amp;chfield=%5BBug+creation%5D&amp;chfieldvalue=&amp;format=table&amp;action=wrap&amp;field0-0-0=noop&amp;type0-0-0=noop&amp;value0-0-0=" target="egit_external">Last day</a>
				</td>
				<td>
					<a href="https://bugs.eclipse.org/bugs/report.cgi?x_axis_field=bug_status&amp;y_axis_field=product&amp;z_axis_field=&amp;query_format=report-table&amp;short_desc_type=allwordssubstr&amp;short_desc=&amp;classification=Technology&amp;product=EGit&amp;product=JGit&amp;longdesc_type=allwordssubstr&amp;longdesc=&amp;bug_file_loc_type=allwordssubstr&amp;bug_file_loc=&amp;status_whiteboard_type=allwordssubstr&amp;status_whiteboard=&amp;keywords_type=allwords&amp;keywords=&amp;bug_severity=blocker&amp;bug_severity=critical&amp;bug_severity=major&amp;bug_severity=normal&amp;bug_severity=minor&amp;bug_severity=trivial&amp;emailtype1=substring&amp;email1=&amp;emailtype2=substring&amp;email2=&amp;bug_id_type=anyexact&amp;bug_id=&amp;votes=&amp;chfieldfrom=-1w&amp;chfieldto=Now&amp;chfield=%5BBug+creation%5D&amp;chfieldvalue=&amp;format=table&amp;action=wrap&amp;field0-0-0=noop&amp;type0-0-0=noop&amp;value0-0-0=" target="egit_external">Last week</a>
				</td>
				<td>
					<a href="https://bugs.eclipse.org/bugs/report.cgi?x_axis_field=bug_status&amp;y_axis_field=product&amp;z_axis_field=&amp;query_format=report-table&amp;short_desc_type=allwordssubstr&amp;short_desc=&amp;classification=Technology&amp;product=EGit&amp;product=JGit&amp;longdesc_type=allwordssubstr&amp;longdesc=&amp;bug_file_loc_type=allwordssubstr&amp;bug_file_loc=&amp;status_whiteboard_type=allwordssubstr&amp;status_whiteboard=&amp;keywords_type=allwords&amp;keywords=&amp;bug_severity=blocker&amp;bug_severity=critical&amp;bug_severity=major&amp;bug_severity=normal&amp;bug_severity=minor&amp;bug_severity=trivial&amp;emailtype1=substring&amp;email1=&amp;emailtype2=substring&amp;email2=&amp;bug_id_type=anyexact&amp;bug_id=&amp;votes=&amp;chfieldfrom=-1m&amp;chfieldto=Now&amp;chfield=%5BBug+creation%5D&amp;chfieldvalue=&amp;format=table&amp;action=wrap&amp;field0-0-0=noop&amp;type0-0-0=noop&amp;value0-0-0=" target="egit_external">Last month</a>
				</td>
				<td>
					<a href="https://bugs.eclipse.org/bugs/report.cgi?x_axis_field=bug_status&amp;y_axis_field=product&amp;z_axis_field=&amp;query_format=report-table&amp;short_desc_type=allwordssubstr&amp;short_desc=&amp;classification=Technology&amp;product=EGit&amp;product=JGit&amp;longdesc_type=allwordssubstr&amp;longdesc=&amp;bug_file_loc_type=allwordssubstr&amp;bug_file_loc=&amp;status_whiteboard_type=allwordssubstr&amp;status_whiteboard=&amp;keywords_type=allwords&amp;keywords=&amp;bug_severity=blocker&amp;bug_severity=critical&amp;bug_severity=major&amp;bug_severity=normal&amp;bug_severity=minor&amp;bug_severity=trivial&amp;emailtype1=substring&amp;email1=&amp;emailtype2=substring&amp;email2=&amp;bug_id_type=anyexact&amp;bug_id=&amp;votes=&amp;chfieldfrom=-1y&amp;chfieldto=Now&amp;chfield=%5BBug+creation%5D&amp;chfieldvalue=&amp;format=table&amp;action=wrap&amp;field0-0-0=noop&amp;type0-0-0=noop&amp;value0-0-0=" target="egit_external">Last year</a>
				</td>
			</tr>
			<tr>
				<td>Bugs closed</td>
				<td>
					<a href="https://bugs.eclipse.org/bugs/report.cgi?x_axis_field=bug_status&amp;y_axis_field=product&amp;z_axis_field=&amp;query_format=report-table&amp;short_desc_type=allwordssubstr&amp;short_desc=&amp;classification=Technology&amp;product=EGit&amp;product=JGit&amp;longdesc_type=allwordssubstr&amp;longdesc=&amp;bug_file_loc_type=allwordssubstr&amp;bug_file_loc=&amp;status_whiteboard_type=allwordssubstr&amp;status_whiteboard=&amp;keywords_type=allwords&amp;keywords=&amp;bug_status=RESOLVED&amp;bug_status=VERIFIED&amp;bug_status=CLOSED&amp;bug_severity=blocker&amp;bug_severity=critical&amp;bug_severity=major&amp;bug_severity=normal&amp;bug_severity=minor&amp;bug_severity=trivial&amp;emailtype1=substring&amp;email1=&amp;emailtype2=substring&amp;email2=&amp;bug_id_type=anyexact&amp;bug_id=&amp;votes=&amp;chfieldfrom=-1d&amp;chfieldto=Now&amp;chfield=bug_status&amp;chfieldvalue=&amp;format=table&amp;action=wrap&amp;field0-0-0=noop&amp;type0-0-0=noop&amp;value0-0-0=" target="egit_external">Last day</a>
				</td>
				<td>
					<a href="https://bugs.eclipse.org/bugs/report.cgi?x_axis_field=bug_status&amp;y_axis_field=product&amp;z_axis_field=&amp;query_format=report-table&amp;short_desc_type=allwordssubstr&amp;short_desc=&amp;classification=Technology&amp;product=EGit&amp;product=JGit&amp;longdesc_type=allwordssubstr&amp;longdesc=&amp;bug_file_loc_type=allwordssubstr&amp;bug_file_loc=&amp;status_whiteboard_type=allwordssubstr&amp;status_whiteboard=&amp;keywords_type=allwords&amp;keywords=&amp;bug_status=RESOLVED&amp;bug_status=VERIFIED&amp;bug_status=CLOSED&amp;bug_severity=blocker&amp;bug_severity=critical&amp;bug_severity=major&amp;bug_severity=normal&amp;bug_severity=minor&amp;bug_severity=trivial&amp;emailtype1=substring&amp;email1=&amp;emailtype2=substring&amp;email2=&amp;bug_id_type=anyexact&amp;bug_id=&amp;votes=&amp;chfieldfrom=-1w&amp;chfieldto=Now&amp;chfield=bug_status&amp;chfieldvalue=&amp;format=table&amp;action=wrap&amp;field0-0-0=noop&amp;type0-0-0=noop&amp;value0-0-0=" target="egit_external">Last week</a>
				</td>
				<td>
					<a href="https://bugs.eclipse.org/bugs/report.cgi?x_axis_field=bug_status&amp;y_axis_field=product&amp;z_axis_field=&amp;query_format=report-table&amp;short_desc_type=allwordssubstr&amp;short_desc=&amp;classification=Technology&amp;product=EGit&amp;product=JGit&amp;longdesc_type=allwordssubstr&amp;longdesc=&amp;bug_file_loc_type=allwordssubstr&amp;bug_file_loc=&amp;status_whiteboard_type=allwordssubstr&amp;status_whiteboard=&amp;keywords_type=allwords&amp;keywords=&amp;bug_status=RESOLVED&amp;bug_status=VERIFIED&amp;bug_status=CLOSED&amp;bug_severity=blocker&amp;bug_severity=critical&amp;bug_severity=major&amp;bug_severity=normal&amp;bug_severity=minor&amp;bug_severity=trivial&amp;emailtype1=substring&amp;email1=&amp;emailtype2=substring&amp;email2=&amp;bug_id_type=anyexact&amp;bug_id=&amp;votes=&amp;chfieldfrom=-1m&amp;chfieldto=Now&amp;chfield=bug_status&amp;chfieldvalue=&amp;format=table&amp;action=wrap&amp;field0-0-0=noop&amp;type0-0-0=noop&amp;value0-0-0=" target="egit_external">Last month</a>
				</td>
				<td>
					<a href="https://bugs.eclipse.org/bugs/report.cgi?x_axis_field=bug_status&amp;y_axis_field=product&amp;z_axis_field=&amp;query_format=report-table&amp;short_desc_type=allwordssubstr&amp;short_desc=&amp;classification=Technology&amp;product=EGit&amp;product=JGit&amp;longdesc_type=allwordssubstr&amp;longdesc=&amp;bug_file_loc_type=allwordssubstr&amp;bug_file_loc=&amp;status_whiteboard_type=allwordssubstr&amp;status_whiteboard=&amp;keywords_type=allwords&amp;keywords=&amp;bug_status=RESOLVED&amp;bug_status=VERIFIED&amp;bug_status=CLOSED&amp;bug_severity=blocker&amp;bug_severity=critical&amp;bug_severity=major&amp;bug_severity=normal&amp;bug_severity=minor&amp;bug_severity=trivial&amp;emailtype1=substring&amp;email1=&amp;emailtype2=substring&amp;email2=&amp;bug_id_type=anyexact&amp;bug_id=&amp;votes=&amp;chfieldfrom=-1y&amp;chfieldto=Now&amp;chfield=bug_status&amp;chfieldvalue=&amp;format=table&amp;action=wrap&amp;field0-0-0=noop&amp;type0-0-0=noop&amp;value0-0-0=" target="egit_external">Last year</a>
				</td>
			</tr>
		</table>
		<p>
			<br/>
		</p>
		<p>To get notified of bugs, go to your e-mail preferences and add &lt;product&gt;.&lt;component&gt;-<EMAIL> to your watch list. For example to get notified of EGit UI bugs, add 
			<i><EMAIL></i>.
		</p>
		<h2 id="Keywords">Keywords</h2>
		<p>To simplify bug management we started to tag EGit bugs with additional pseudo keywords (not normal Bugzilla keywords). The tags are prepended to the bug's summary field. Since we use these tags for internal bug management reporters of a bug should not add any pseudo keywords when filing the bug. The owner of the component bucket is responsible to add the keywords.</p>
		<p>Keywords are used to group bugs without assigning them to a developer. So with the introduction of the keywords it is easy to search for all bugs belonging to a specific sub component. For example to get an overview of all open refactoring issues search for new, assigned or reopened bugs containing the word [refactoring] in the summary field.</p>
		<p>Be aware that not all bugs are tagged with keywords, only bugs that belong to a certain sub group may have a tag attached. The following lists some of the currently used tags.</p>
		<table border="1" cellpadding="3" cellspacing="0">
			<tr>
				<th>Tag</th>
				<th>Description</th>
				<th>Link</th>
			</tr>
			<tr>
				<td>[sync]</td>
				<td>everything related to Synchronize / Synchronize View</td>
				<td>
					<a href="https://bugs.eclipse.org/bugs/buglist.cgi?short_desc=%5Bsync%5D;query_format=advanced;bug_status=UNCONFIRMED;bug_status=NEW;bug_status=ASSIGNED;bug_status=REOPENED;short_desc_type=allwordssubstr;product=EGit" target="egit_external">View bugs</a>
				</td>
			</tr>
			<tr>
				<td>[repoView]</td>
				<td>everything related to the Git Repository View</td>
				<td>
					<a href="https://bugs.eclipse.org/bugs/buglist.cgi?short_desc=%5BrepoView%5D;query_format=advanced;bug_status=UNCONFIRMED;bug_status=NEW;bug_status=ASSIGNED;bug_status=REOPENED;short_desc_type=allwordssubstr;product=EGit" target="egit_external">View bugs</a>
				</td>
			</tr>
			<tr>
				<td>[releng]</td>
				<td>everything related to release engineering and build</td>
				<td>
					<a href="https://bugs.eclipse.org/bugs/buglist.cgi?short_desc=%5Breleng%5D;query_format=advanced;bug_status=UNCONFIRMED;bug_status=NEW;bug_status=ASSIGNED;bug_status=REOPENED;short_desc_type=allwordssubstr;product=EGit" target="egit_external">View bugs</a>
				</td>
			</tr>
			<tr>
				<td>[historyView]</td>
				<td>everything related to the Git History View</td>
				<td>
					<a href="https://bugs.eclipse.org/bugs/buglist.cgi?short_desc=%5BhistoryView%5D;query_format=advanced;bug_status=UNCONFIRMED;bug_status=NEW;bug_status=ASSIGNED;bug_status=REOPENED;short_desc_type=allwordssubstr;product=EGit" target="egit_external">View bugs</a>
				</td>
			</tr>
		</table>
		<p>
			<br/>
		</p>
		<h2 id="Spam_Bugs">Spam Bugs</h2>
		<p>If you come across spam bugs you can request webmaster to delete them by marking them as duplicate of bug 442999.</p>
		<p>Also see 
			<a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=502814" target="egit_external">bug 502814</a>
		</p><hr class="navigation-separator"/>
		<table class="navigation" style="width: 100%;" border="0" summary="navigation">
			<tr>
				<td style="width: 20%" align="left">
					<a href="Tests.html" title="Tests">
						<img alt="Previous" border="0" src="../../images/prev.gif"/>
					</a>
				</td>
				<td style="width: 60%" align="center">
					<a href="Contributor-Guide.html" title="EGit Contributor Guide">
						<img alt="EGit Contributor Guide" border="0" src="../../images/home.gif"/>
					</a>
				</td>
				<td style="width: 20%" align="right">
					<a href="Website.html" title="Website">
						<img alt="Next" border="0" src="../../images/next.gif"/>
					</a>
				</td>
			</tr>
			<tr>
				<td style="width: 20%" align="left" valign="top">Tests</td>
				<td style="width: 60%" align="center"></td>
				<td style="width: 20%" align="right" valign="top">Website</td>
			</tr>
		</table>
	</body>
</html>