<?xml version='1.0' encoding='utf-8' ?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
		<title>EGit Contributor Guide - Website</title>
		<link type="text/css" rel="stylesheet" href="../../book.css"/>
	</head>
	<body>
		<table class="navigation" style="width: 100%;" border="0" summary="navigation">
			<tr>
				<th style="width: 100%" align="center" colspan="3">Website</th>
			</tr>
			<tr>
				<td style="width: 20%" align="left">
					<a href="Bugs.html" title="Bugs">
						<img alt="Previous" border="0" src="../../images/prev.gif"/>
					</a>
				</td>
				<td style="width: 60%" align="center"></td>
				<td style="width: 20%" align="right">
					<a href="Contributing-Patches.html" title="Contributing Patches">
						<img alt="Next" border="0" src="../../images/next.gif"/>
					</a>
				</td>
			</tr>
			<tr>
				<td style="width: 20%" align="left" valign="top">Bugs</td>
				<td style="width: 60%" align="center"></td>
				<td style="width: 20%" align="right" valign="top">Contributing Patches</td>
			</tr>
		</table><hr class="navigation-separator"/>
		<h1 id="Website">Website</h1>
		<p>The EGit and JGit websites are located in Git repositories which are configured for Gerrit code review.</p>
		<p>
			<b>egit</b>
		</p>
		<ul>
			<li>File &gt; Import &gt; Git &gt; Projects from Git</li>
			<li>Select URL 
				<ul>
					<li>HTTPS protocol: 
						<b>
							<a href="https://git.eclipse.org/r/p/www.eclipse.org/egit.git" target="egit_external">https://git.eclipse.org/r/p/www.eclipse.org/egit.git</a>
						</b>
					</li>
					<li>SSH protocol: 
						<b>ssh://<EMAIL>:29418/www.eclipse.org/egit.git</b>
					</li>
				</ul>
			</li>
			<li>in Repositories View on node "origin" click "Gerrit Configuration..." and select branch "master", then changes you push to upstream will end up in Gerrit for review and can be submitted there</li>
		</ul>
		<p>
			<b>jgit</b>
		</p>
		<ul>
			<li>File &gt; Import &gt; Git &gt; Projects from Git</li>
			<li>Select URL 
				<ul>
					<li>HTTP protocol: 
						<b>
							<a href="https://git.eclipse.org/r/p/www.eclipse.org/jgit.git" target="egit_external">https://git.eclipse.org/r/p/www.eclipse.org/jgit.git</a>
						</b>
					</li>
					<li>SSH protocol: 
						<b>ssh://<EMAIL>:29418/www.eclipse.org/jgit.git</b>
					</li>
				</ul>
			</li>
			<li>in Repositories View on node "origin" click "Gerrit Configuration..." and select branch "master", then changes you push to upstream will end up in Gerrit for review and can be submitted there</li>
		</ul>
		<p>
			<br/>
		</p><hr class="navigation-separator"/>
		<table class="navigation" style="width: 100%;" border="0" summary="navigation">
			<tr>
				<td style="width: 20%" align="left">
					<a href="Bugs.html" title="Bugs">
						<img alt="Previous" border="0" src="../../images/prev.gif"/>
					</a>
				</td>
				<td style="width: 60%" align="center">
					<a href="Contributor-Guide.html" title="EGit Contributor Guide">
						<img alt="EGit Contributor Guide" border="0" src="../../images/home.gif"/>
					</a>
				</td>
				<td style="width: 20%" align="right">
					<a href="Contributing-Patches.html" title="Contributing Patches">
						<img alt="Next" border="0" src="../../images/next.gif"/>
					</a>
				</td>
			</tr>
			<tr>
				<td style="width: 20%" align="left" valign="top">Bugs</td>
				<td style="width: 60%" align="center"></td>
				<td style="width: 20%" align="right" valign="top">Contributing Patches</td>
			</tr>
		</table>
	</body>
</html>