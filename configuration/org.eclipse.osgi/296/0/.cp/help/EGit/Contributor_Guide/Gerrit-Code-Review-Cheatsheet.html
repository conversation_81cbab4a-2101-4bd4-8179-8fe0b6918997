<?xml version='1.0' encoding='utf-8' ?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
		<title>EGit Contributor Guide - Gerrit Code Review Cheatsheet</title>
		<link type="text/css" rel="stylesheet" href="../../book.css"/>
	</head>
	<body>
		<table class="navigation" style="width: 100%;" border="0" summary="navigation">
			<tr>
				<th style="width: 100%" align="center" colspan="3">Gerrit Code Review Cheatsheet</th>
			</tr>
			<tr>
				<td style="width: 20%" align="left">
					<a href="Contributing-Patches.html" title="Contributing Patches">
						<img alt="Previous" border="0" src="../../images/prev.gif"/>
					</a>
				</td>
				<td style="width: 60%" align="center"></td>
				<td style="width: 20%" align="right">
					<a href="Updating-This-Document.html" title="Updating This Document">
						<img alt="Next" border="0" src="../../images/next.gif"/>
					</a>
				</td>
			</tr>
			<tr>
				<td style="width: 20%" align="left" valign="top">Contributing Patches</td>
				<td style="width: 60%" align="center"></td>
				<td style="width: 20%" align="right" valign="top">Updating This Document</td>
			</tr>
		</table><hr class="navigation-separator"/>
		<h1 id="Gerrit_Code_Review_Cheatsheet">Gerrit Code Review Cheatsheet</h1>
		<h2 id="Install_the_commit-msg_hook_in_your_repository">Install the commit-msg hook in your repository</h2>
		<pre style="width: 60em;">scp -p -P 29418 <EMAIL>:hooks/commit-msg .git/hooks/
</pre>
		<p> 
			This will ask for a password. It is the password that you have to generate in the 
			<i>SSH Keys</i> section of settings in your Gerrit account.
		</p>
		<p>You can alternatively 
			<a href="https://git.eclipse.org/r/tools/hooks/commit-msg" target="egit_external">download the file</a>. The 
			<a href="http://gerrit.googlecode.com/svn/documentation/2.1.2/cmd-hook-commit-msg.html" target="egit_external">hook</a> helps append a Change-Id to your commit message.
		</p>
		<p>EGit can also generate a Gerrit Change-Id into your commit message both 
			<a href="../../EGit/User_Guide/Tasks.html#Commit_Message" title="EGit/User_Guide#Commit_Message">manually</a> or in an 
			<a href="../../EGit/User_Guide/Tasks.html#Gerrit_Configuration" title="EGit/User_Guide#Gerrit_Configuration">automated</a> way.
		</p>
		<h2 id="To_create_a_new_change">To create a new change</h2>
		<ul>
			<li>JGit</li>
		</ul>
		<pre style="width: 60em;">git push ssh://<EMAIL>:29418/jgit/jgit.git HEAD:refs/for/master
</pre>
		<p> </p>
		<ul>
			<li>EGit</li>
		</ul>
		<pre style="width: 60em;">git push ssh://<EMAIL>:29418/egit/egit.git HEAD:refs/for/master
</pre>
		<p> 
			Or, if you've followed the instructions on 
			<a href="Contributing-Patches.html#Adding_a_dedicated_remote">Adding_a_dedicated_remote</a> you can simply do: 
		</p>
		<pre style="width: 60em;">git push review
</pre>
		<p> 
			Since the current repository has the right definition for 'review', you won't need to remember the canonical push URL</p>
		<h2 id="To_update_an_existing_change_with_a_new_commit">To update an existing change with a new commit</h2>
		<pre style="width: 60em;">git push ssh://<EMAIL>:29418/egit/egit.git HEAD:refs/for/master
</pre>
		<p> 
			This works because Gerrit links the new commit to the prior change based upon the Change-Id footer in the commit message. (This is automatically generated by the commit-msg hook you installed above.) If you refuse to use the commit-msg hook, or don't have a Change-Id footer, you should read the Gerrit documentation on 
			<a href="https://git.eclipse.org/r/Documentation/user-changeid.html" target="egit_external">change-id lines</a> and 
			<a href="https://git.eclipse.org/r/Documentation/user-upload.html#push_replace" target="egit_external">replacing changes</a>.
		</p>
		<p>
			<b>Note:</b> To be picked up by Gerrit, a Change-Id line must be in the bottom portion (last paragraph) of a commit message, and may be mixed together with the Signed-off-by, Acked-by, or other such footers. So if your Change-Id line is ignored it's probably not in the last paragraph :).
		</p>
		<h2 id="To_compare_bulk_diffs_using_Git">To compare bulk diffs using Git</h2>
		<p>Since each Gerrit review patchset actually commits its own tree, you can pull out the trees and compare them. </p>
		<p>If you've got a large changeset, and you want to be able to do diffs between them via (command line) git instead of browsing on the web, then you can fetch the individual changes and then perform a diff. For example, 
			<a href="http://git.eclipse.org/r/2" target="egit_external">http://git.eclipse.org/r/2</a> shows the 'download' section for each patchset. In this case, it looks like: 
		</p>
		<ul>
			<li>Patch Set 1 <code>git pull ssh://<EMAIL>/jgit refs/changes/02/2/1 (1d3331a91bd477d3f70cde9613576cf9688ac358)</code> </li>
			<li>Patch Set 2 <code>git pull ssh://<EMAIL>/jgit refs/changes/02/2/2 (13ab9a43d4d512963556a92e889b1204d32f8e68)</code> </li>
			<li>Patch Set 3 <code>git pull ssh://<EMAIL>/jgit refs/changes/02/2/3 (d14cc645655683ba3e30a35833fb2282142e898f)</code> </li>
			<li>Patch Set 4 <code>git pull ssh://<EMAIL>/jgit refs/changes/02/2/4 (43de8d385b614c72fd796e17da75d381f6e0cc25)</code></li>
		</ul>
		<p>Performing a <code>git pull</code> will both get the bits and merge them into your tree, which won't do what you want for comparison. So, in order to get the bits (but not merge), you need to do a <code>git fetch</code> instead. Let's say we want to diff the last two patches against each other rather than reviewing the entire patchset again: </p>
		<pre>git fetch ssh://<EMAIL>/jgit refs/changes/02/2/3
git fetch ssh://<EMAIL>/jgit refs/changes/02/2/4

git diff d14cc645655683ba3e30a35833fb2282142e898f 43de8d385b614c72fd796e17da75d381f6e0cc25

# or git diff d14cc6 43de8d
</pre>
		<p> 
			If you're doing this from within an already checked out project, you can do <code>git fetch origin</code> (or any other remote name in <code>.git/config}</code>. </p>
		<p>Git fetched data will stay around in your repository, but will be 'orphaned' if no references point to it. To clean up, you can run <code>git gc</code> or wait until this happens automatically.</p>
		<h2 id="To_trigger_Hudson_build_for_a_change">To trigger Hudson build for a change</h2>
		<p>We have build jobs 
			<b>jgit.gerrit</b> on 
			<a href="https://hudson.eclipse.org/jgit/" target="egit_external">https://hudson.eclipse.org/jgit/</a>, and 
			<b>egit.gerrit</b> and 
			<b>egit-github.gerrit</b> on 
			<a href="https://hudson.eclipse.org/egit/" target="egit_external">https://hudson.eclipse.org/egit/</a> which are triggered automatically when a new change or a new patchset for an existing change is pushed for review. These jobs will comment on the respective change when the build is started and when it's finished and vote on the change according to the build and test results.
		</p>
		<p>Sometimes you may want to retrigger such a build e.g. because it may have failed due to some temporary problem.
			Committers can manually trigger these jobs in the following way:</p>
		<ul>
			<li>Go to 
				<a href="https://hudson.eclipse.org/sandbox/gerrit_manual_trigger/" target="egit_external">Trigger a Gerrit event manually</a> page 
			</li>
			<li>Search for a change you'd like to build</li>
			<li>Select the patch set(s) you want to trigger</li>
			<li>Press 
				<b>Trigger Selected</b> button
			</li>
		</ul>
		<p>If you are not a committer and need to retrigger a build ask for that on the mailing list.</p>
		<h2 id="To_approve_a_change">To approve a change</h2>
		<ul>
			<li>Click on Publish Comments </li>
			<li>Vote with the radio buttons</li>
		</ul>
		<h2 id="To_add_a_reviewer">To add a reviewer</h2>
		<p>Once you've pushed your commit to Gerrit for review, you can go to the web page (
			<a href="https://git.eclipse.org/r/" target="egit_external">https://git.eclipse.org/r/</a>) and see your changes. By clicking on the review, there's an option to add a reviewer by e-mail address; they'll then be sent a message indicating that they'd like your review on the item.
		</p>
		<p>It's usually not necessary to add any reviewers, it should be reviewed by the committers sooner or later. If this hasn't happened, you can look for people that did changes in the same area and add them as reviewers. It's also ok to comment on a change to "bump" its visibility.</p>
		<h2 id="Code_Review">Code Review</h2>
		<p>The code review category indicates your opinion on the quality of the code, and how well it fits within the purpose of the existing surrounding code. A +2 vote from any committer is required before submission can occur. A -2 vote from any committer will block submission. </p>
		<h2 id="IP_Review">IP Review</h2>
		<p>The IP review category indicates whether or not the change has been properly logged under the 
			<a href="http://www.eclipse.org/legal/EclipseLegalProcessPoster.pdf" target="egit_external">Eclipse IP Process</a>. Under that process, any committer should mark his/her change +1 if they were the sole author of the change. For any other change, a committer should only mark +1 after ensuring the 
			<a href="Contributing-Patches.html#Legal_Paperwork">Legal Paperwork</a> has been done. A +1 vote is required to submit a change, while a -1 vote will block submission.
		</p>
		<h2 id="Submission_Guidelines">Submission Guidelines</h2>
		<p>We strive to use Gerrit to improve our understanding of the code base and improve quality. </p>
		<p>In order to ensure a proper review happens, some simple guidelines should be followed:</p>
		<ul>
			<li>vote 0/-1 for not-ready-to-submit (AKA WIP) own proposals, +1 otherwise;</li>
			<li>If a changeset is not-ready-to-submit, please put [RFC] or [DRAFT] in the message to let people know</li>
			<li>let non-trivial changes be in review for at least 24 hours</li>
			<li>if you want your changeset reviewed by someone, please add them as a reviewer</li>
		</ul>
		<h2 id="Tips_.26_Tricks">Tips &amp; Tricks</h2>
		<h3 id="Class_Loading_Issues">Class Loading Issues</h3>
		<p>If you encounter strange class loading issues during runtime (e.g. on UI test executions) the following might help:</p>
		<p>Enable tracing in your launch configuration to get information how imported packages are resolved at runtime. Select the Tracing tab in your launch configuration, select "Enable tracing", select plug-in org.eclipse.osgi, select category resolver/wiring on the right side.</p>
		<p>
			<a href="http://wiki.eclipse.org/Category:Draft_Documentation" title="Category:Draft_Documentation" target="egit_external">Category:Draft_Documentation</a>
		</p><hr class="navigation-separator"/>
		<table class="navigation" style="width: 100%;" border="0" summary="navigation">
			<tr>
				<td style="width: 20%" align="left">
					<a href="Contributing-Patches.html" title="Contributing Patches">
						<img alt="Previous" border="0" src="../../images/prev.gif"/>
					</a>
				</td>
				<td style="width: 60%" align="center">
					<a href="Contributor-Guide.html" title="EGit Contributor Guide">
						<img alt="EGit Contributor Guide" border="0" src="../../images/home.gif"/>
					</a>
				</td>
				<td style="width: 20%" align="right">
					<a href="Updating-This-Document.html" title="Updating This Document">
						<img alt="Next" border="0" src="../../images/next.gif"/>
					</a>
				</td>
			</tr>
			<tr>
				<td style="width: 20%" align="left" valign="top">Contributing Patches</td>
				<td style="width: 60%" align="center"></td>
				<td style="width: 20%" align="right" valign="top">Updating This Document</td>
			</tr>
		</table>
	</body>
</html>