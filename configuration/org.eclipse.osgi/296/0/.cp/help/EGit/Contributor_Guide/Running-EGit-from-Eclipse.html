<?xml version='1.0' encoding='utf-8' ?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
		<title>EGit Contributor Guide - Running EGit from Eclipse</title>
		<link type="text/css" rel="stylesheet" href="../../book.css"/>
	</head>
	<body>
		<table class="navigation" style="width: 100%;" border="0" summary="navigation">
			<tr>
				<th style="width: 100%" align="center" colspan="3">Running EGit from Eclipse</th>
			</tr>
			<tr>
				<td style="width: 20%" align="left">
					<a href="Manual-Developer-Setup.html" title="Manual Developer Setup">
						<img alt="Previous" border="0" src="../../images/prev.gif"/>
					</a>
				</td>
				<td style="width: 60%" align="center"></td>
				<td style="width: 20%" align="right">
					<a href="Builds.html" title="Builds">
						<img alt="Next" border="0" src="../../images/next.gif"/>
					</a>
				</td>
			</tr>
			<tr>
				<td style="width: 20%" align="left" valign="top">Manual Developer Setup</td>
				<td style="width: 60%" align="center"></td>
				<td style="width: 20%" align="right" valign="top">Builds</td>
			</tr>
		</table><hr class="navigation-separator"/>
		<h1 id="Running_EGit_from_Eclipse">Running EGit from Eclipse</h1>
		<p>Now that everything builds, the next step is to run an Eclipse instance with the EGit/JGit code of the workspace:</p>
		<ul>
			<li>Right click on the 
				<i>org.eclipse.egit.ui</i> project
			</li>
			<li>Debug As &gt; Eclipse Application</li>
		</ul>
		<p>This should create a new launch configuration and start a new nested Eclipse instance in debug mode. The created launch configuration can be edited, e.g. to change where the workspace of the nested Eclipse should be located.</p>
		<p>The launch configuration can also be used in normal (non-debug) mode of course.</p>
		<p>Also see the 
			<a href="http://help.eclipse.org/juno/topic/org.eclipse.pde.doc.user/guide/tools/launchers/eclipse_application_launcher.htm" target="egit_external">reference on eclipse application launchers</a>.
		</p>
		<p>
			<br/>
		</p><hr class="navigation-separator"/>
		<table class="navigation" style="width: 100%;" border="0" summary="navigation">
			<tr>
				<td style="width: 20%" align="left">
					<a href="Manual-Developer-Setup.html" title="Manual Developer Setup">
						<img alt="Previous" border="0" src="../../images/prev.gif"/>
					</a>
				</td>
				<td style="width: 60%" align="center">
					<a href="Contributor-Guide.html" title="EGit Contributor Guide">
						<img alt="EGit Contributor Guide" border="0" src="../../images/home.gif"/>
					</a>
				</td>
				<td style="width: 20%" align="right">
					<a href="Builds.html" title="Builds">
						<img alt="Next" border="0" src="../../images/next.gif"/>
					</a>
				</td>
			</tr>
			<tr>
				<td style="width: 20%" align="left" valign="top">Manual Developer Setup</td>
				<td style="width: 60%" align="center"></td>
				<td style="width: 20%" align="right" valign="top">Builds</td>
			</tr>
		</table>
	</body>
</html>