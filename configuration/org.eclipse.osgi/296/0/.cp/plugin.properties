###############################################################################
# Copyright (c) 2010 <PERSON> and others.
# All rights reserved. This program and the accompanying materials
# are made available under the terms of the Eclipse Public License v1.0
# which accompanies this distribution, and is available at
# http://www.eclipse.org/legal/epl-v10.html
###############################################################################
Bundle-Vendor = Eclipse EGit
Bundle-Name = Git integration for Eclipse - Documentation
cheatsheet.category = Team/Git
cheatsheet.clone.name = Cloning a Git Repository
cheatsheet.clone.desc = With the Git Clone Wizard you can clone repositories using different transport protocols.
cheatsheet.push.name = Pushing to another Git Repository
cheatsheet.push.desc = With the Git Push Wizard you can push commits to another repository using different transport protocols.
