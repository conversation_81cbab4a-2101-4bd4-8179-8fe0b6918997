<?xml version="1.0" encoding="UTF-8"?>
<!--
   Copyright (C) 2010, <PERSON> <<EMAIL>>

   All rights reserved. This program and the accompanying materials
   are made available under the terms of the Eclipse Public License v1.0
   which accompanies this distribution, and is available at
   http://www.eclipse.org/legal/epl-v10.html
-->
<?eclipse version="3.4"?>
<contexts>
	<context id="GitCloneWizard" title="Clone Git Repository">
		<description>To be guided through this wizard start the cheat sheet 'Cloning a Git Repository' below.</description>
		<command
			serialization="org.eclipse.ui.cheatsheets.openCheatSheet(cheatSheetId=org.eclipse.egit.cheatsheets.clone)"
			label="Cloning a Git Repository" />
	</context>
	<context id="PushWizard" title="Push To Another Repository">
		<description>To be guided through this wizard start the cheat sheet 'Pushing to another Git Repository' below.</description>
		<command
			serialization="org.eclipse.ui.cheatsheets.openCheatSheet(cheatSheetId=org.eclipse.egit.cheatsheets.push)"
			label="Pushing to another Git Repository" />
	</context>
</contexts>
