<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title>Web Tools Project</title>

<style type="text/css">
body {margin: 0 auto; background-color: #eeecf0; padding: 0; height: 100%; width: 100%; border: none;}
a, img {border: none;}
#container {width: 100%; height: 701px;}
#back {position: absolute; width: auto; z-index: 0;}
#front {position: relative; width: auto; z-index: 1;}
</style>

<script type="text/javascript">
<!-- //
function initRollovers() {
	if (!document.getElementById) return
	
	var aPreLoad = new Array();
	var sTempSrc;
	var aImages = document.getElementsByTagName('img');

	for (var i = 0; i < aImages.length; i++) {		
		if (aImages[i].className == 'imgover') {
			var src = aImages[i].getAttribute('src');
			var ftype = src.substring(src.lastIndexOf('.'), src.length);
			var hsrc = src.replace(ftype, '_o'+ftype);

			aImages[i].setAttribute('hsrc', hsrc);
			
			aPreLoad[i] = new Image();
			aPreLoad[i].src = hsrc;
			
			aImages[i].onmouseover = function() {
				sTempSrc = this.getAttribute('src');
				this.setAttribute('src', this.getAttribute('hsrc'));
			}	
			
			aImages[i].onmouseout = function() {
				if (!sTempSrc) sTempSrc = this.getAttribute('src').replace('_o'+ftype, ftype);
				this.setAttribute('src', sTempSrc);
			}
		}
	}
}

window.onload = initRollovers;
// -->
</script>
</head>

<body>

<div id="container">
	<div id="back">
	<img src="../images/back.png" width="100%" height="701"/>
	</div>
	<div id="front">
		<table cellspacing="0" cellpadding="0" border="0" align="center">
		  <tbody>
			<tr>
				<td colspan="2"><img src="../images/01.png"/></td>
			</tr>
			<tr>
				<td colspan="2">
					<table cellspacing="0" cellpadding="0" border="0">
						<tr>
							<td><img src="../images/02a.png"/></td>
							<td><a href="http://www.eclipse.org/webtools/community/new"><img src="../images/02b.png" class="imgover"/></a></td>
							<td><img src="../images/02c.png"/></td>
							<td><a href="http://www.eclipse.org/webtools/community/education"><img src="../images/02d.png" class="imgover"/></a></td>
							<td><img src="../images/02e.png"/></td>
							<td><a href="http://org.eclipse.ui.intro/showPage?id=whatsnew"><img src="../images/02f.png" class="imgover"/></a></td>
							<td><a href="http://org.eclipse.ui.intro/showPage?id=samples"><img src="../images/02g.png" class="imgover"/></a></td>
							<td><a href="http://org.eclipse.ui.intro/showPage?id=tutorials"><img src="../images/02h.png" class="imgover"/></a></td>
							<td><a href="http://org.eclipse.ui.intro/close"><img src="../images/02i.png" class="imgover"/></a></td>
						</tr>
					</table>
				</td>
			</tr>
			<tr>
				<td colspan="2">
				<table cellspacing="0" cellpadding="0" border="0">
				<tr><td>
					<table cellspacing="0" cellpadding="0" border="0">
						<tr>
							<td colspan="2"><img src="../images/03a-top.png"/></td>
						</tr>
						<tr>
							<td><img src="../images/03a-leftbottom.png"/></td>
							<td><a href="http://www.eclipse.org/webtools/sse"><img src="../images/03a-rightbottom.png" class="imgover"/></a></td>
						</tr>
					</table>
				</td>
				<td><img src="../images/03b.png"/></td>
				<td>
					<table cellspacing="0" cellpadding="0" border="0">
						<tr>
							<td><a href="http://www.eclipse.org/webtools/ws"><img src="../images/03c.png" class="imgover"/></a></td>
							<td><img src="../images/03d.png"/></td>
						</tr>
						<tr>
							<td><img src="../images/03c-bottom.png"/></td>
							<td><img src="../images/03d-bottom.png"/></td>
						</tr>
					</table>
					</td></tr>
					</table>
				</td>
			</tr>
			<tr>
				<td colspan="2"><img src="../images/06.png"/></td>
			</tr>
			<tr>
				<td colspan="2">
					<table cellspacing="0" cellpadding="0" border="0">
						<tr>
							<td><img src="../images/07a.png"/></td>
							<!-- XML Tools -->
							<td><a href="http://www.eclipse.org/webtools/sse"><img src="../images/07b.png" class="imgover"/></a></td>
							<td><img src="../images/07c.png"/></td>
						</tr>
					</table>
				</td>
			</tr>
			<tr>
				<td colspan="2"><img src="../images/08.png"/></td>
			</tr>
			<tr>
				<td colspan="2">
					<table cellspacing="0" cellpadding="0" border="0">
						<tr>
							<td><img src="../images/09a.png"/></td>
							<!-- Server Tools -->
							<td><a href="http://www.eclipse.org/webtools/server"><img src="../images/09b.png" class="imgover"/></a></td>
							<td><img src="../images/09c.png"/></td>
						</tr>
					</table>
				</td>
			</tr>
			<tr>
				<td colspan="2"><img src="../images/10.png"/></td>
			</tr>
			<tr>
				<td colspan="2">
					<table cellspacing="0" cellpadding="0" border="0">
						<tr>
							<td><img src="../images/11a.png"/></td>
							<!-- Java EE -->
							<td><a href="http://www.eclipse.org/webtools/jee"><img src="../images/11b.png" class="imgover"/></a></td>
							<td><img src="../images/11c.png"/></td>
						</tr>
					</table>
				</td>
			</tr>
			<tr>
				<td colspan="2"><img src="../images/12.png"/></td>
			</tr>
			<tr>
				<td colspan="2">
					<table cellspacing="0" cellpadding="0" border="0">
						<tr>
							<td><img src="../images/13a.png"/></td>
							<!-- JSF -->
							<td><a href="http://www.eclipse.org/webtools/jsf"><img src="../images/13b.png" class="imgover"/></a></td>
							<td><img src="../images/13c.png"/></td>
						</tr>
					</table>
				</td>
			</tr>
			<tr>
				<td colspan="2"><img src="../images/14.png"/></td>
			</tr>
			<tr>
				<td colspan="2">
					<table cellspacing="0" cellpadding="0" border="0">
						<tr>
							<td><img src="../images/15a.png"/></td>
							<!-- JPA -->
							<td><a href="http://www.eclipse.org/webtools/dali"><img src="../images/15b.png" class="imgover"/></a></td>
							<td><img src="../images/15c.png"/></td>
						</tr>
					</table>
				</td>
			</tr>
			<tr>
				<td colspan="2"><img src="../images/16.png"/></td>
			</tr>
		  </tbody>
		</table>
	</div>
</div>

</body>
</html>