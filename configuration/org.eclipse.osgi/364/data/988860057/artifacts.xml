<?xml version='1.0' encoding='UTF-8'?>
<?artifactRepository version='1.1.0'?>
<repository name='C:\ZLSB\Eclipse2018\Eclipse2018\.eclipseextension' type='org.eclipse.equinox.p2.artifact.repository.simpleRepository' version='1.0.0'>
  <properties size='3'>
    <property name='p2.timestamp' value='1687761986683'/>
    <property name='org.eclipse.update.site.policy' value='USER-EXCLUDE'/>
    <property name='org.eclipse.update.site.list' value=''/>
  </properties>
  <mappings size='3'>
    <rule filter='(&amp; (classifier=osgi.bundle))' output='${repoUrl}/plugins/${id}_${version}.jar'/>
    <rule filter='(&amp; (classifier=binary))' output='${repoUrl}/binary/${id}_${version}'/>
    <rule filter='(&amp; (classifier=org.eclipse.update.feature))' output='${repoUrl}/features/${id}_${version}.jar'/>
  </mappings>
  <artifacts size='0'/>
</repository>
