#Eclipse modern messages class
#Tue Apr 22 18:28:44 EDT 2014
AddDependencyAction_error_msg=Can't add dependency to {0}
AddDependencyAction_error_title=Add Dependency
AddDependencyAction_searchDialog_title=Add Dependency
AddDependencyDialog_artifactId_error=Artifact Id cannot be empty
AddDependencyDialog_artifactId_label=Artifact Id\:
AddDependencyDialog_groupId_error=Group Id cannot be empty
AddDependencyDialog_groupId_label=Group Id\:
AddDependencyDialog_scope_label=Scope\:
AddDependencyDialog_version_label=Version\: 
AddPluginAction_searchDialog_title=Add Plugin
AssignWorkingSetDialog_btnAssign_text=Assign
AssignWorkingSetDialog_btnFilterAssignedProjects_text=Filter assigned projects
AssignWorkingSetDialog_btnFilterClosedProjects_text=Filter closed projects
AssignWorkingSetDialog_lblWorkingSet=Working set
AssignWorkingSetDialog_title=Add projects to working sets
BuildDebugView_actionClear=Clear
BuildDebugView_actionCollapseAll=Collapse All
BuildDebugView_actionSuspend=Suspend
BuildDebugView_columnBuildNumber=Build\#
BuildDebugView_columnName=Name
BuildDebugView_errorDescription=Could not collect build log
BuildDebugView_errorTitle=Build debug error
BuildDebugView_nodeDelta=delta
BuildDebugView_nodeExecutions=executions
ChangeNatureAction_job_changing=Changing nature
ChangeNatureAction_status_error=Can't change nature
CustomArchetypeDialog_error_artid=Archetype Artifact Id is required
CustomArchetypeDialog_error_grid=Archetype Group Id is required
CustomArchetypeDialog_error_version=Archetype Version is required
CustomArchetypeDialog_lblArchetypeartifactid=Archetype Artifact Id\:
CustomArchetypeDialog_lblArchetypegroupId=Archetype Group Id\:
CustomArchetypeDialog_lblArchetypeversion=Archetype Version\:
CustomArchetypeDialog_lblRepo=Repository URL\:
CustomArchetypeDialog_message=Specify Archetype and Maven repository URL
CustomRepositoriesNode_name=Custom Repositories
EditDependencyDialog_artifactId_label=Artifact Id\:
EditDependencyDialog_classifier_label=Classifier\:
EditDependencyDialog_groupId_label=Group Id\:
EditDependencyDialog_optional_checkbox=Optional
EditDependencyDialog_scope_label=Scope\:
EditDependencyDialog_systemPath_label=System Path\:
EditDependencyDialog_title=Dependency Properties
EditDependencyDialog_type_label=Type\:
EditDependencyDialog_version_label=Version\:
EnableNatureAction_job_enable=Enabling Maven Dependency Management
EnableNatureAction_wizard_shell=Create new POM
ExternalInstallPage_btnAddProject_text=Project...
ExternalInstallPage_btnDirectory_text=Directory...
ExternalInstallPage_btnDown_text=Down
ExternalInstallPage_btnRemove_text=Remove
ExternalInstallPage_btnRestoreDefault_text=Restore Default
ExternalInstallPage_btnUp_text=Up
ExternalInstallPage_description=Specify attributes for a Maven installation
ExternalInstallPage_lblInstallationLibraries_text=Additional extension libraries\:
ExternalInstallPage_lblInstallationLocation_text=Installation home\:
ExternalInstallPage_lblInstallationName_text=Installation name\:
ExternalInstallPage_pageName=External Installation
GlobalRepositoriesNode_name=Global Repositories
IndexedArtifactNode_no_pack=[No Packaging]
LifecycleMappingPage_actionColumnTitle=Action
LifecycleMappingPage_autoSelectButton=&Auto Select
LifecycleMappingPage_description=Discover and map Eclipse plugins to Maven plugin goal executions.
LifecycleMappingPage_descriptionLabel=Description
LifecycleMappingPage_deselectAllButton=&Resolve All Later
LifecycleMappingPage_doNotExecuteParent=Do Not Execute (add to parent)
LifecycleMappingPage_doNotExecuteParentDescription=Places metadata which tells m2e to ignore the execution in the pom(s) that adds the Maven plugin to the build.
LifecycleMappingPage_doNotExecutePom=Do Not Execute (add to pom)
LifecycleMappingPage_doNotExecutePomDescription=Places metadata which tells m2e to ignore the execution in the pom(s) that executes the Maven goal.
LifecycleMappingPage_doNotExecuteWorkspace=Do Not Execute (add to workspace)
LifecycleMappingPage_doNotExecuteWorkspaceDescription=Places metadata which tells m2e to ignore the execution in the entire workspace
LifecycleMappingPage_errorMavenBuild={0} ({1} errors)
LifecycleMappingPage_installDescription=Install {0}
LifecycleMappingPage_licenseLabel=License
LifecycleMappingPage_mavenBuildColumnTitle=Maven Build
LifecycleMappingPage_noMarketplaceEntryDescription=No marketplace entries found to handle {0} in Eclipse.  Please see Help for more information.
LifecycleMappingPage_numErrors={0} errors
LifecycleMappingPage_resolveLaterDescription=Resolve Later
LifecycleMappingPage_title=Setup Maven plugin connectors
LifecycleMappingPreferencePage_Browse=Browse...
LifecycleMappingPreferencePage_ChangeLocation=Change mapping file location\:
LifecycleMappingPreferencePage_ChooseNewLocation=Choose a new location for the workspace Lifecycle Mappings file
LifecycleMappingPreferencePage_LifecycleMapping=Lifecycle Mappings
LifecycleMappingPreferencePage_WorkspaceMappingsDescription=Edit the lifecycle mappings for the entire workspace.\nWarning\: improperly editing this file may cause problems with the m2e builder.
LifecycleMappingPreferencePage_WorkspaceMappingsOpen=Open workspace lifecycle mappings metadata
LifecycleMappingPreferencePage_btnRefreshLifecycles_text=Reload workspace lifecycle mappings metadata
LifecycleMappingPreferencePage_this_message=Lifecycle Mappings (experimental)
LifecycleMappingPropertyPage_copyToClipboard=Copy to clipboard
LifecycleMappingPropertyPage_mapping=Mapping
LifecycleMappingPropertyPage_mntmCollapseAll_text=Collapse All
LifecycleMappingPropertyPage_mntmExpandAll_text=Expand All
LifecycleMappingPropertyPage_mntmShowIgnoredExecutions_text=Show ignored executions
LifecycleMappingPropertyPage_pageMessage=Lifecycle Mapping
LifecycleMappingPropertyPage_pluginExecution=Plugin execution
LifecycleMappingPropertyPage_showLIfecyclePhases=Show lifecycle phases
LifecycleMappingPropertyPage_invalidPom=Can not read pom.xml
LifecycleMappingsViewer_trclmnSource_text=Source
LocalArchetypeCatalogDialog_btnBrowse=&Browse...
LocalArchetypeCatalogDialog_dialog_title=Select Archetype catalog
LocalArchetypeCatalogDialog_error=Archetype catalog can not be loaded: {0}
LocalArchetypeCatalogDialog_error_empty=Archetype catalog is empty
LocalArchetypeCatalogDialog_error_exist=Archetype catalog does not exist
LocalArchetypeCatalogDialog_error_no_location=Archetype catalog location is required
LocalArchetypeCatalogDialog_lblCatalog=&Catalog File\:
LocalArchetypeCatalogDialog_lblDesc=Description\:
LocalArchetypeCatalogDialog_message=Specify catalog location and description
LocalArchetypeCatalogDialog_title=Local Archetype Catalog
LocalRepositoryNode_local=Local Repository
LocalRepositoryRootNode_name=Local Repositories
M2EErrorDialog_column_error=Error
M2EErrorDialog_column_name=Project Name
MarkerResolutionGenerator_desc=M2E updates the configuration of the project to align with current content of pom.xml file.
MarkerResolutionGenerator_label=Update project configuration
MavenArchetypesPreferencePage_btnAddLocal=Add &Local Catalog...
MavenArchetypesPreferencePage_btnAddRemote=Add &Remote Catalog...
MavenArchetypesPreferencePage_btnEdit=&Edit...
MavenArchetypesPreferencePage_btnRemove=&Remove
MavenArchetypesPreferencePage_error=Can't save archetype catalog configuration\n{0}
MavenArchetypesPreferencePage_link=Add, remove or edit <a href\="\#">Maven Archetype catalogs</a>\:
MavenArchetypesPreferencePage_local=Local\: {0}
MavenArchetypesPreferencePage_packaged=Packaged\: {0}
MavenArchetypesPreferencePage_remote=Remote\: {0}
MavenArchetypesPreferencePage_title=Maven Archetype Catalogs
MavenConsoleImpl_title=Maven Console
MavenConsolePageParticipant_any=Show Console on Any Output
MavenConsolePageParticipant_error=Show Console on Error
MavenConsoleRemoveAction_tooltip=Close Maven Console
MavenDebugOutputAction_0=Debug Output
MavenDependenciesWizardPage_lblArtifacts=Maven Artifacts\:
MavenDependenciesWizardPage_searchDialog_title=Add Dependency
MavenDiscoveryProposalWizard_title=Discover m2e connectors
MavenGoalSelectionDialog_btnQualified=Use &Qualified Name
MavenGoalSelectionDialog_error=Should select at least one goal
MavenGoalSelectionDialog_lblSelect=&Select Goal\:
MavenGoalSelectionDialog_message=Select goal\:
MavenImportWizardPage_btnDeselectTree_text=Deselect Tree
MavenImportWizardPage_btnSelectTree_text=Select Tree
MavenImportWizardPage_createWorkingSet=Add project(s) to working set
MavenImportWizardPage_desc=Select Maven projects
MavenImportWizardPage_forbiddenImportFromRoot=Importing projects from the root directory is not permitted.
MavenImportWizardPage_inherited=[inherited]
MavenImportWizardPage_mntmDeselectTree_text=Deselect Tree
MavenImportWizardPage_mntmSelectTree_text=Select Tree
MavenImportWizardPage_title=Maven Projects
MavenImportWizard_hideWarningMessage=Hide this warning in future
MavenImportWizard_job=Importing Maven projects
MavenImportWizard_messageIncompleteMapping=Continuing with import will result in projects with build errors, please see help for more information
MavenImportWizard_searchingTaskTitle=Searching m2e marketplace
MavenImportWizard_title=Import Maven Projects
MavenImportWizard_titleIncompleteMapping=Incomplete Maven Goal Execution
MavenInstallFileArtifactWizardPage_btnChecksum=Create C&hecksum
MavenInstallFileArtifactWizardPage_btnFilename=&Browse...
MavenInstallFileArtifactWizardPage_btnGenerate=Gen&erate POM
MavenInstallFileArtifactWizardPage_btnPom=B&rowse...
MavenInstallFileArtifactWizardPage_desc=Install file in local repository
MavenInstallFileArtifactWizardPage_error_artifactid=Artifact Id must be specified
MavenInstallFileArtifactWizardPage_error_groupid=Group Id must be specified
MavenInstallFileArtifactWizardPage_error_missing=Artifact file does not exist
MavenInstallFileArtifactWizardPage_error_missingpom=POM file does not exist
MavenInstallFileArtifactWizardPage_error_no_name=Artifact file name must be specified
MavenInstallFileArtifactWizardPage_error_packaging=Packaging must be specified
MavenInstallFileArtifactWizardPage_error_version=Version must be specified
MavenInstallFileArtifactWizardPage_file_title=Select file
MavenInstallFileArtifactWizardPage_lblArtifact=&Artifact Id\:
MavenInstallFileArtifactWizardPage_lblClassifier=&Classifier\:
MavenInstallFileArtifactWizardPage_lblFileName=Artifact &file\:
MavenInstallFileArtifactWizardPage_lblPackaging=&Packaging\:
MavenInstallFileArtifactWizardPage_lblPom=&POM file\:
MavenInstallFileArtifactWizardPage_lblVersion=&Version\:
MavenInstallFileArtifactWizardPage_lblgroupid=&Group Id\:
MavenInstallFileArtifactWizardPage_message=Selected artifact corresponds to {0}
MavenInstallFileArtifactWizardPage_title=Install file in local repository
MavenInstallFileWizard_error=Execution error
MavenInstallFileWizard_job=Installing artifact
MavenInstallFileWizard_title=Install artifact
MavenInstallationWizardPage_btnExternal_text_1=External
MavenInstallationWizardPage_btnWorkspace_text=Workspace
MavenInstallationWizardPage_lblInstallationType_text=Installation type\:
MavenInstallationWizardPage_messageDuplicateInstallationName=The Maven installation name is already in use
MavenInstallationWizardPage_messageHomeDirectoryIsNotMavenInstll=Target is not a supported Maven Home
MavenInstallationWizardPage_messageSelectHomeDirectory=Enter the home directory of the Maven Installation
MavenInstallationWizardPage_messageSelectInstallatonName=Enter a name for the Maven Installation
MavenInstallationWizardPage_selectProjectMessage=Select projects to add\:
MavenInstallationWizardPage_selectProjectTitle=Project selection
MavenInstallationWizard_titleAddInstallation=Edit Maven Runtime
MavenInstallationWizard_titleNewInstallation=New Maven Runtime
MavenInstallationsPreferencePage_btnAdd=&Add...
MavenInstallationsPreferencePage_btnEdit=&Edit...
MavenInstallationsPreferencePage_btnRemove=&Remove
MavenInstallationsPreferencePage_dialog_install_message=Select Maven installation directory
MavenInstallationsPreferencePage_dialog_install_title=Maven Installation
MavenInstallationsPreferencePage_lblNote=Note\: Embedded runtime is always used for dependency resolution
MavenInstallationsPreferencePage_link=Select the installation used to launch Maven\:
MavenInstallationsPreferencePage_runtimeUnavailable=NOT AVAILABLE 
MavenInstallationsPreferencePage_tblclmnDetails_text=Details
MavenInstallationsPreferencePage_tblclmnName_text=Name
MavenInstallationsPreferencePage_title=Maven Installations
MavenModuleWizardParentPage_error=The parent project must have a packaging type of POM
MavenPomSelectionComponent_detail1={0} ({1})
MavenPomSelectionComponent_details2={0}, size\: {1} b
MavenPomSelectionComponent_error=Search error\: {0}
MavenPomSelectionComponent_lblResults=&Search Results\:
MavenPomSelectionComponent_managed_decoration=\  (managed)
MavenPomSelectionComponent_nosel=No selection
MavenPomSelectionComponent_results=Results for ''{0}'' ({1})
MavenPomSelectionComponent_searchJob=Repository search
MavenPomSelectionComponent_search_title=&Enter groupId, artifactId or sha1 prefix or pattern (*)\:
MavenPomSelectionComponent_searching=Searching ''{0}''...
MavenPomSelectionComponent_selected=Selected {0}
MavenPomSelectionComponent_toomany=Too many results to display. Enter a more specific search term.
MavenPomSelectionComponent_UnavailableRemoteRepositoriesIndexes=Index downloads are disabled, search results may be incomplete.
MavenPomWizardPage_desc=This wizard creates a new POM (pom.xml) descriptor for Maven.
MavenPomWizardPage_dialog_title=Select project
MavenPomWizardPage_error_artid=Artifact Id must be specified
MavenPomWizardPage_error_folder=Project or folder must be specified
MavenPomWizardPage_error_folder2=Folder must exist
MavenPomWizardPage_error_folder_write=Folder must be writable
MavenPomWizardPage_error_grid=Group Id must be specified
MavenPomWizardPage_error_pack=Packaging must be specified
MavenPomWizardPage_error_version=Version must be specified
MavenPomWizardPage_lblProject=&Project\:
MavenPomWizardPage_title=Maven POM
MavenPomWizard_error_exists=POM already exists
MavenPomWizard_error_title=Error
MavenPomWizard_status_not_exists=Folder "{0}" does not exist.
MavenPomWizard_task=Creating POM
MavenPomWizard_title=Maven POM wizard
MavenPreferencePage_download=Download repository index updates on startup
MavenPreferencePage_hide=Hide folders of physically nested modules (experimental)
MavenPreferencePage_update=Update Maven projects on startup
MavenPreferencePage_autoUpdateProjectConfiguration=Automatically update Maven projects configuration (experimental)
MavenPreferencePage_updateProjectRequired_title=Update project required
MavenPreferencePage_warnIncompleteMapping=Hide warning for incomplete mapping
MavenPreferencePage_changingPreferencesRequiresProjectUpdate=Changing the checksum policy requires updating the Maven projects for the changes to take effect. Do you want to update projects now?
MavenProjectPreferencePage_btnResolve=Resolve dependencies from &Workspace projects
MavenProjectPreferencePage_dialog_message=Maven settings have changed. Do you want to update project configuration?
MavenProjectPreferencePage_dialog_title=Maven Settings
MavenProjectPreferencePage_job=Updating {0} Sources
MavenProjectPreferencePage_lblProfiles=Active Maven &Profiles (comma separated)\:
MavenProjectPreferencePage_title=Maven
MavenProjectWizardArchetypePage_add_title=Add Archetype
MavenProjectWizardArchetypePage_all=All Catalogs
MavenProjectWizardArchetypePage_btnAdd=&Add Archetype...
MavenProjectWizardArchetypePage_btnConfigure=&Configure...
MavenProjectWizardArchetypePage_btnLast=&Show the last version of Archetype only
MavenProjectWizardArchetypePage_btnSnapshots=&Include snapshot archetypes
MavenProjectWizardArchetypePage_error_emptyNexusIndexer=No archetypes currently available. The archetype list will refresh when the indexes finish updating.
MavenProjectWizardArchetypePage_error_emptyCatalog=No archetypes available for this catalog.
MavenProjectWizardArchetypePage_error_read=Unable to read catalog factory.
MavenProjectWizardArchetypePage_error_resolve=The archetype {0} could not be resolved.
MavenProjectWizardArchetypePage_error_resolve2=Can't resolve Archetype {0}
MavenProjectWizardArchetypePage_ErrorRetrievingArchetypes=Error retrieving archetypes
MavenProjectWizardArchetypePage_lblCatalog=Ca&talog\:
MavenProjectWizardArchetypePage_lblFilter=&Filter\:
MavenProjectWizardArchetypePage_task_downloading=Downloading Archetype 
MavenProjectWizardArchetypePage_task_indexing=indexing...
MavenProjectWizardArchetypePage_task_reading=reading project...
MavenProjectWizardArchetypePage_task_resolving=resolving POM...
MavenProjectWizardArchetypePage_task_resolving2=resolving JAR...
MavenProjectWizardArchetypeParametersPage_btnAdd=&Add...
MavenProjectWizardArchetypeParametersPage_btnRemove=&Remove
MavenProjectWizardArchetypeParametersPage_columnName=Name
MavenProjectWizardArchetypeParametersPage_columnValue=Value
MavenProjectWizardArchetypeParametersPage_error_download=Error downloading archetype {0}
MavenProjectWizardArchetypeParametersPage_error_package=Invalid package name
MavenProjectWizardArchetypeParametersPage_lblProps=Properties available from archetype\:
MavenProjectWizardArchetypeParametersPage_task=Downloading Archetype {0}
MavenProjectWizardArtifactPage_searchDialog_title=Select Parent Artifact
MavenProjectWizardLocationPage_btnLocation=Brows&e...
MavenProjectWizardLocationPage_btnUserDefault=Use default &Workspace location
MavenProjectWizardLocationPage_dialog_location=Select Location
MavenProjectWizardLocationPage_lblLocation=&Location\:
MavenRepositoryView_action_copy=Copy URL
MavenRepositoryView_action_copy_tooltip=Copy URL to Clipboard
MavenRepositoryView_action_disable_tooltip=Disable repository index
MavenRepositoryView_action_enableFull_tooltip=Enable full repository index
MavenRepositoryView_action_enable_tooltip=Enable minimal repository index
MavenRepositoryView_action_open=Open POM
MavenRepositoryView_action_open_tooltip=Open Maven POM
MavenRepositoryView_action_rebuild=Rebuild Index
MavenRepositoryView_action_rebuild_tooltip=Force a rebuild of the maven index
MavenRepositoryView_action_reload=Reload settings.xml
MavenRepositoryView_action_update=Update Index
MavenRepositoryView_btnCollapse=Collapse All
MavenRepositoryView_btnCollapse_tooltip=Collapse All
MavenRepositoryView_btnUpdate_tooltip=Update repository index
MavenRepositoryView_details_disabled=Index Details Disabled
MavenRepositoryView_disable_details=Disable Index Details
MavenRepositoryView_enable_full=Enable Full Index
MavenRepositoryView_enable_minimum=Enable Minimum Index
MavenRepositoryView_enabled_full=Full Index Enabled
MavenRepositoryView_error_message=Unable to set the index details due to the following error\:\n
MavenRepositoryView_error_title=Error Setting Index Details
MavenRepositoryView_job_reloading=Reloading settings.xml
MavenRepositoryView_minimum_enabled=Minimum Index Enabled
MavenRepositoryView_rebuild_indexes=Rebuilding Indexes
MavenRepositoryView_rebuild_many=Rebuild Indexes
MavenRepositoryView_rebuild_msg=Are you sure you want to rebuild the index ''{0}''
MavenRepositoryView_rebuild_msg2=Are you sure you want to rebuild the selected indexes?
MavenRepositoryView_rebuild_one=Rebuild Index
MavenRepositoryView_rebuild_title=Rebuild Index
MavenRepositoryView_reload_msg=This will reload the settings.xml and rebuild the indexes for the repositories. Are you sure you want to reload the settings?
MavenRepositoryView_reload_title=Reload settings.xml
MavenRepositoryView_update_more=Update Indexes
MavenRepositoryView_update_one=Update Index
MavenSettingsPreferencePage_userSettingsBrowseButton_text=Browse...
MavenSettingsPreferencePage_btnUpdate=Update Settings
MavenSettingsPreferencePage_error_globalSettingsMissing=Global settings file doesn't exist
MavenSettingsPreferencePage_error_userSettingsMissing=User settings file doesn't exist
MavenSettingsPreferencePage_error_globalSettingsParse=Unable to parse global settings file; {0}
MavenSettingsPreferencePage_error_userSettingsParse=Unable to parse user settings file; {0}
MavenSettingsPreferencePage_globalSettingsBrowseButton_text=Browse...
MavenSettingsPreferencePage_job_indexing=Indexing Local Repository...
MavenSettingsPreferencePage_job_updating=Updating Maven settings
MavenSettingsPreferencePage_lblLocal=Local Repository (From merged user and global settings)\:
MavenSettingsPreferencePage_userSettingslink1=User &Settings\:
MavenSettingsPreferencePage_userSettingslink2=User &Settings (<a href\="\#">open file</a>)\:
MavenSettingsPreferencePage_userSettingslink_tooltip=Open editor for user settings
MavenSettingsPreferencePage_globalSettingslink1=Global &Settings\:
MavenSettingsPreferencePage_globalSettingslink2=Global &Settings (<a href\="\#">open file</a>)\:
MavenSettingsPreferencePage_globalSettingslink_tooltip=Open editor for global settings
MavenSettingsPreferencePage_task_updating=Updating progress for {0}
MavenSettingsPreferencePage_title=Maven User Settings
MavenWarningsPreferencePage_Error=Error
MavenWarningsPreferencePage_Ignore=Ignore
MavenWarningsPreferencePage_Warning=Warning
MavenWarningsPreferencePage_OutOfDate_Project_Config=Out-of-date project configuration
MavenWarningsPreferencePage_groupidDupParent="groupId" duplicate of parent groupId
MavenWarningsPreferencePage_notCoveredMojoExecution=Plugin execution not covered by lifecycle configuration
MavenWarningsPreferencePage_versionDupParent="version" duplicate of parent version
MavenWarningsPreferencePage_overridingManagedPreferences=Overriding managed version
MavenWarningsPreferencePage_changingProblemSeveritiesRequiresProjectUpdate=Changing problems severity requires updating the Maven projects for the changes to take effect. Do you want to update projects now?
NestedProjectsComposite_Add_OutOfDate=Add out-of-date
NestedProjectsComposite_Multiple_OOD_Projects_Link={0} unselected projects are out of date and should be updated. <A>Click here</A> to include them.
NestedProjectsComposite_OutOfDateProjectBtn_AddOneProject_Tooltip=Add out-of-date project to the selection
NestedProjectsComposite_OutOfDateProjectBtn_AddProjects_Tooltip=Add {0} out-of-date projects to the selection
NestedProjectsComposite_OutOfDateProjectBtn_Generic_Tooltip=Add out-of-date projects to the selection
NestedProjectsComposite_Single_OOD_Project_Link=One unselected project is out of date and should be updated. <A>Click here</A> to include it.
OpenPomAction_33=Can't open editor for {0}\n{1}
OpenPomAction_error_download=Can't download {0}
OpenPomAction_error_download_source=Can't download sources for {0}
OpenPomAction_error_open_editor=Can't open editor for {0}
OpenPomAction_error_open_pom=Can't open pom file for {0}
OpenPomAction_job_opening=Opening POM
OpenPomAction_open_error_message=Unable to read Maven project
OpenPomAction_open_error_title=Open POM
OpenPomAction_open_title=Open Maven POM
OpenPomAction_title_pom=Search Maven POM
PomHelper_errorCreatingChange=An error occurred creating change
ProjectRepositoriesNode_name=Project Repositories
RemoteArchetypeCatalogDialog_btnVerify=&Verify...
RemoteArchetypeCatalogDialog_error_empty=Remote catalog is empty
RemoteArchetypeCatalogDialog_error_read=Unable to read remote catalog;\n{0}
RemoteArchetypeCatalogDialog_error_required=Archetype catalog url is required
RemoteArchetypeCatalogDialog_job_download=Downloading remote catalog
RemoteArchetypeCatalogDialog_lblCatalog=&Catalog File\:
RemoteArchetypeCatalogDialog_lblDesc=Description\:
RemoteArchetypeCatalogDialog_message=Specify catalog url and description
RemoteArchetypeCatalogDialog_message_found=Found {0} archetype(s)
RemoteArchetypeCatalogDialog_title=Remote Archetype Catalog
RepositoryNode_updating=\ [updating]
SelectionUtil_error_cannot_read=Can't read Maven project
UpdateDepenciesDialog_availableCodebasesLabel=Available Maven Codebases
UpdateDepenciesDialog_collapseAll=&Collapse All
UpdateDepenciesDialog_deselectAll=&Deselect All
UpdateDepenciesDialog_deselectTree=Deselect Tree
UpdateDepenciesDialog_expandAll=&Expand All
UpdateDepenciesDialog_forceUpdate=Force Update of Snapshots/Releases
UpdateDepenciesDialog_offline=&Offline
UpdateDepenciesDialog_selectAll=&Select All
UpdateDepenciesDialog_selectTree=Select Tree
UpdateMavenProjectDialog_btnCheckButton_text=Update dependencies
UpdateMavenProjectDialog_btnCleanProjects_text=Clean projects
UpdateMavenProjectDialog_btnUpdateProjectConfiguration_text=Update project configuration from pom.xml
UpdateMavenProjectDialog_dialogMessage=Select Maven projects and update options
UpdateMavenProjectDialog_title=Update Maven Project
UpdateMavenProjectsDialog_btnRefreshFromLocal_text=Refresh workspace resources from local filesystem
UpdateSourcesAction_error_cannot_update=Unable to update Maven configuration
UpdateSourcesAction_error_message=Unable to update maven configuration for the following projects\:
UpdateSourcesAction_error_title=Error Updating Maven Configuration
UpdateSourcesAction_job_update_conf=Updating Maven Project
WorkingSetGroup_btnAddSet=&Add project(s) to working set
WorkingSetGroup_btnMore=Mor&e...
WorkingSetGroup_lblSet=Wo&rking set\:
WorkspaceRepositoryNode_name=Workspace Projects
artifactComponentArtifact=Artifact
artifactComponentArtifactId=Artifact Id\:
artifactComponentDescription=Description\:
artifactComponentGroupId=Group Id\:
artifactComponentName=Name\:
artifactComponentPackage=Package\:
artifactComponentPackaging=Packaging\:
artifactComponentVersion=Version\:
launchGoalsDialogTitle=Goals
launchPropertyDialogName=Name\:
launchPropertyDialogValue=Value\:
locationComponentAtExternal=Create project at e&xternal location
locationComponentBrowse=B&rowse...
locationComponentDirectory=&Location\:
locationComponentInWorkspace=Create project in &workspace
locationComponentLocation=Location
locationComponentSelectLocation=Select Location
pomEditorDefaultPage=Open XML page in the POM editor by default
preferencesDebugOutput=Debu&g Output
preferencesDownloadJavadoc=Download Artifact &JavaDoc
preferencesDownloadSources=Do&wnload Artifact Sources
preferencesGlobalUpdateNever=Do not automatically update dependencies from remote repositories
preferencesOffline=&Offline
preferencesGlobalChecksumPolicy=Global Checksum Policy\:
preferencesGlobalChecksumPolicy_tooltip=What to do when artifact checksums don't match
preferencesGlobalChecksumPolicy_default=Default
preferencesGlobalChecksumPolicy_ignore=Ignore
preferencesGlobalChecksumPolicy_warn=Warn
preferencesGlobalChecksumPolicy_fail=Fail
preferencesReindexButton=Re&index
projectSelectionDialogTitle=Select a Maven project
resolverConfigurationAdvanced=Ad&vanced
resolverConfigurationProfiles=&Profiles\:
resolverConfigurationResolveWorkspaceProjects=Resolve &Workspace projects
resolverConfigurationTemplate=Name &template\:
resolverConfigurationTemplateDescription=Optional Eclipse project name template, e.g. "[groupId].[artifactId]-[version]"
wizardImportPageBrowse=&Browse...
wizardImportPageDeselectAll=&Deselect All
wizardImportPageProjects=&Projects\:
wizardImportPageRefresh=&Refresh
wizardImportPageRoot=&Root Directory\:
wizardImportPageScanningErrors=Scanning errors ({0})\:
wizardImportPageSelectAll=Select &All
wizardImportPageSelectRootFolder=Select Root Folder
wizardImportValidatorProjectExists=Project {0} already exists\n Add a version or custom suffix using "Name template" in "Advanced" settings
wizardImportValidatorProjectImported=Project {0} is already imported into workspace
wizardImportValidatorWorkspaceFolder=Can't import project {0} from an existing workspace folder
wizardModulePageArchetypeTitle=New Maven Module
wizardModulePageArtifactTitle=New Maven Module
wizardModulePageParametersTitle=New Maven Module
wizardModulePageParentBrowse=Br&owse...
wizardModulePageParentDescription=Select the module name and parent
wizardModulePageParentModuleName=&Module Name\:
wizardModulePageParentParentProject=Parent Project\:
wizardModulePageParentTitle=New Maven Module
wizardModulePageParentValidatorModuleName=Enter a module name.
wizardModulePageParentValidatorNameExists=A resource with this name already exists.
wizardModulePageParentValidatorParentProject=Select a parent project.
wizardModuleTitle=New Maven Module
wizardProjectErrorPomAlreadyExists=A pom.xml file already exists in the destination folder.
wizardProjectJobCreating=Creating {0}
wizardProjectJobCreatingProject=Creating project "{0}"
wizardProjectJobFailed=Failed to create project "{0}"
wizardProjectPageArchetypeColumnArtifactId=Artifact Id
wizardProjectPageArchetypeColumnGroupId=Group Id
wizardProjectPageArchetypeColumnVersion=Version
wizardProjectPageArchetypeDescription=Select an Archetype
wizardProjectPageArchetypeRetrievingArchetypes=Retrieving archetypes\:
wizardProjectPageArchetypeTitle=New Maven project
wizardProjectPageArtifactParentArtifactId=Artifact Id\:
wizardProjectPageArtifactParentBrowse=Browse...
wizardProjectPageArtifactParentClear=Clear
wizardProjectPageArtifactParentGroupId=Group Id\:
wizardProjectPageArtifactParentTitle=Parent Project
wizardProjectPageArtifactParentVersion=Version\:
wizardProjectPageDependenciesAdd=&Add...
wizardProjectPageDependenciesDescription=Add additional dependencies to the project.
wizardProjectPageDependenciesRemove=&Remove
wizardProjectPageDependenciesTitle=Select additional dependencies
wizardProjectPageMaven2ArchetypeParametersDescription=Specify Archetype parameters
wizardProjectPageMaven2Description=Configure project
wizardProjectPageMaven2Title=New Maven project
wizardProjectPageMaven2ValidatorArtifactID=Enter an artifact id.
wizardProjectPageMaven2ValidatorArtifactIDinvalid=Invalid artifact id\: {0}
wizardProjectPageMaven2ValidatorArtifactIDnospaces=Artifact id cannot contain spaces.
wizardProjectPageMaven2ValidatorGroupID=Enter a group id for the artifact.
wizardProjectPageMaven2ValidatorGroupIDinvalid=Invalid group id\: {0}
wizardProjectPageMaven2ValidatorGroupIDnospaces=Group id cannot contain spaces.
wizardProjectPageMaven2ValidatorPackaging=Enter a packaging for the artifact.
wizardProjectPageMaven2ValidatorParent=To specify a parent project, set the parent group id, artifact id and version.
wizardProjectPageMaven2ValidatorProjectNameInvalid=Invalid project name\: {0}
wizardProjectPageMaven2ValidatorRequiredProperty=Required property "{0}" is not set.
wizardProjectPageMaven2ValidatorVersion=Enter a version for the artifact.
wizardProjectPageProjectDescription=Select project name and location
wizardProjectPageProjectSimpleProject=Create a &simple project (skip archetype selection)
wizardProjectPageProjectTitle=New Maven project
wizardProjectPageProjectValidatorInvalidLocation=Invalid project location path
wizardProjectPageProjectValidatorProjectLocation=Enter a location for the project.
wizardProjectTitle=New Maven Project
