#
# Copyright (c) 2007, 2013 Sonatype, Inc.
# All rights reserved. This program and the accompanying materials
# are made available under the terms of the Eclipse Public License v1.0
# which accompanies this distribution, and is available at
# http://www.eclipse.org/legal/epl-v10.html
#
# Contributors:
#      Sonatype, Inc. - initial API and implementation
#      Rob Newton - added warning preferences page for disabling warnings

Bundle-Vendor = Eclipse.org - m2e
Bundle-Name = Maven Integration for Eclipse

m2.decorator.name=Maven Decorator
m2.decorator.description=Adds an icon decoration to \
  projects with Maven dependency management enabled.

m2.decorator.version.name=Maven Version Decorator
m2.decorator.version.description=Shows version for managed projects.

m2.popup.project.label=&Maven
m2.popup.project.enable.label=Enable Maven Support
m2.popup.project.disable.label=Disable Maven Support
m2.popup.project.includeModules.label=Enable Nested Modules
m2.popup.project.workspaceResolution.label=Enable Workspace Resolution
m2.popup.project.add-dependency.label=Add Dependency
m2.popup.project.update-sources.label=Update Source Folders
m2.popup.ModuleProjectWizardAction=New &Maven Module Project
m2.popup.AddPluginAction=Add &Plugin
m2.popup.AddDependencyAction=Add &Dependency
m2.popup.UpdateMavenProjectAction=&Update Project...
m2.popup.OpenUrlAction.openCiPage=Open Continuous Integration
m2.popup.OpenUrlAction.openScmPage=Open Source Control
m2.popup.OpenUrlAction.openIssuesPage=Open Issue Tracker
m2.popup.OpenUrlAction.openProjectPage=Open Project Page
m2.popup.OpenPomAction=Open POM
m2.popup.DisableNatureAction=Disable Maven &Nature
m2.popup.ChangeNatureAction.disableWorkspaceResolution=Disable Workspace &Resolution
m2.popup.ChangeNatureAction.enableWorkspaceResolution=Enable Workspace &Resolution

m2.shortcut.open.pom=Open Maven POM
m2.problem.category=Maven Problems
m2.preferences.page.name=Maven

m2.wizard.name=Maven
m2.wizard.pom.name=Maven POM file
m2.wizard.project.name=Maven Project

m2.wizard.project.description=Create a Maven Project

m2.wizard.import.name=Existing Maven Projects
m2.wizard.import.description=Import Existing Maven Projects

m2.wizard.module.name=Maven Module
m2.wizard.module.description=Create a Maven module

m2.editor.pom.name=Maven POM Editor

m2.shortcut.addDependency=Add Maven Dependency
m2.shortcut.addPlugin=Add Maven Plugin
m2.shortcut.updateProject=Update Project

openpomaction.label = Open POM
openpomaction.label2 = Open POM
openpomaction.label3 = Open POM
openpomaction.label4 = Open POM
openprojectaction.label = Open Project Page
openprojectaction.label2 = Open Project Page
openprojectaction.label3 = Open Project Page
openprojectaction.label4 = Open Project Page
command.opentype.description = Open Maven Type
command.adddependency.description = Add Maven Dependency
command.addplugin.description = Add Maven Plugin
command.updateproject.description = Update Maven Project configuration and dependencies
keyword.label = maven
page.installations.name = Installations
page.usersettings.name = User Settings
page.archetypes.name = Archetypes
page.userinterface.name = User Interface
page.warnings.name = Errors/Warnings
page.lifecyclemapping.name = Lifecycle Mappings
category.general.name = General
wizard.install.name = Install or deploy an artifact to a Maven repository
wizard.install.description = Install or Deploy an artifact (archive) into a Maven repository
consoleFactory.label = Maven Console
actionSet.navigation.label = Maven Navigation
action.openpom.label = Open Maven POM...
action.openpom.tooltip = Open Maven POM
action.updatedeps.label = Update All Maven Dependencies
action.updatedeps.tooltip = Refresh Maven Models
actionSet.maven.label = Maven
action.adddep.label = Add Maven Dependency
action.adddep.tooltip = Add Maven Dependency
action.addplugin.label = Add Maven Plugin
action.add.plugin.tooltip = Add Maven Plugin
action.assignWorkingSet.label=Assign Working Sets...
category.maven.name = Maven
view.repos.name = Maven Repositories
view.build.name = Maven Workspace Build
page.maven.name = Maven
page.mappings.name = Lifecycle Mapping
lifecycleMapping.custom.name = Customizable Lifecycle Mapping
lifecycleMapping.empty.name = Empty Lifecycle Mapping
extension-point.indexes.name = Maven Indexes
extension-point.scmhandlers.name = SCM Handlers
extension-point.scmhandlersui.name = SCM Handlers UI
extension-point.archetypes.name = Maven Archetypes
extension-point.configurators.name = Project Configurators
extension-point.m2menu.name = m2 menu items
extension-point.lifecyclemappings.name = Maven Lifecycle Mappings
extension-point.mapping.proppage.name = Maven Lifecycle Mapping Property Page
extension-point.mappingdefault.name = Default Maven Lifecycle Mappings
extension-point.component.name = Maven Core Component Contributor
extension-point.changed.name = mavenProjectChangedListeners
extension-point.lifecycleMappingMetadataSource.name = Lifecycle Mapping Metadata Source
extension-point.conversionEnabler.name = Project Conversion Enabler
convert.to.maven.name=Convert to Maven Project

m2.build.directory.filter.name=Maven build folder
m2.build.directory.filter.description=Hides the Maven build folder
