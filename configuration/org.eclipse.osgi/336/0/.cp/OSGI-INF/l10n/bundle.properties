###############################################################################
# Copyright (c) 2010, 2016 The Eclipse Foundation and others.
# All rights reserved. This program and the accompanying materials
# are made available under the terms of the Eclipse Public License v1.0
# which accompanies this distribution, and is available at
# http://www.eclipse.org/legal/epl-v10.html
#
# Contributors:
# 	The Eclipse Foundation - initial API and implementation
#   <PERSON><PERSON> (Red Hat Inc.) - [480176] ask MPC for unassociated files
###############################################################################
Bundle-Vendor = Eclipse Marketplace Client
Bundle-Name = Marketplace Client
command.open.description = Show the Eclipse Marketplace wizard
command.open.name = Eclipse Marketplace
command.open.label = Eclipse &Marketplace...
command.open.tooltip = Open the Eclipse Marketplace wizard
command.importFavorites.description = Import another user's Marketplace Favorites List
command.importFavorites.name = Import Marketplace Favorites
command.installed.description = Update or uninstall plug-ins installed from the Marketplace
command.installed.name = Manage installed plug-ins
command.favorites.description = Open Marketplace Favorites
command.favorites.name = Eclipse Marketplace Favorites
command.favorites.label = Eclipse Marketplace &Favorites...
command.favorites.tooltip = Open your personal Eclipse Marketplace favorites
command.account.favorites.label = Open Marketplace &Favorites...
command.account.favorites.tooltip = Open your personal Eclipse Marketplace favorites
catalog.description = Eclipse Marketplace (MP) is a place to find and keep track of Eclipse-based solutions.
catalog.label = Eclipse Marketplace
extension-point.name = Marketplace Catalog
unassociatedStrategy.label=Search Marketplace
support.name = Marketplace Client
support.description = Eclipse Marketplace installation and wizard
keyword.marketplace = marketplace mpc
keyword.projectnature = project nature
keyword.discovery = discovery
projectNatures.preferencePage.label = Project Natures
