###############################################################################
# Copyright (c) 2010-2017 The Eclipse Foundation and others.
# All rights reserved. This program and the accompanying materials
# are made available under the terms of the Eclipse Public License v1.0
# which accompanies this distribution, and is available at
# http://www.eclipse.org/legal/epl-v10.html
#
# Contributors:
# 	The Eclipse Foundation - initial API and implementation
###############################################################################
ProjectNatures=Project Natures
CatalogExtensionPointReader_cannotFindResource=Cannot find resource {0}
CatalogExtensionPointReader_cannotRegisterCatalog_bundle_reason=Cannot register catalog for bundle {0}: {1}
CatalogExtensionPointReader_labelRequired=Must specify label
CatalogExtensionPointReader_urlRequired=Must specify url
MarketplaceClientUi_message_message2={0}: {1}
MarketplaceClientUi_unexpectedException_reason=Unexpected exception: {0}
MarketplaceClientUi_notFound=Resource not found: {0}
MarketplaceClientUi_unknownHost=Cannot resolve host\n\nThis is most often caused by a problem with your internet connection. Please check your internet connection and retry.
MarketplaceClientUi_connectionProblem=Connection failed\n\nThis is most often caused by a problem with your internet connection. Please check your internet connection and retry.
MarketplaceOrAssociateDialog_title=Editors available on the Marketplace
MarketplaceOrAssociateDialog_linkToPreferences=See also <a>Preferences for File Associations</a>
MarketplaceOrAssociateDialog_message=Better editor support for ''{0}'' files is available on the Marketplace.
MarketplaceOrAssociateDialog_showProposals=Show IDE extensions for this file type and let me install them
MarketplaceOrAssociateDialog_associate=Associate ''{0}'' files with current editor ({1}) and do not ask again
MarketplaceOrAssociateDialog_descriptionEmbeddedSystemEditor=Your ''{0}'' file was opened in an embedded system editor, which might not be fully integrated. Better editor support is available on the Marketplace.
MarketplaceOrAssociateDialog_descriptionExternalSystemEditor=Your ''{0}'' file was opened in an external system editor. Better editor support is available on the Marketplace.
MarketplaceOrAssociateDialog_descriptionSimpleTextEditor=Your ''{0}'' file was opened in a simple text editor. Better editor support is available on the Marketplace.
AskMarketPlaceForFileSupportStrategy_jobName=Search Marketplace for compatible editors ({0})
AskMerketplaceForFileSupportStrategy_dialogJobName=Proposals dialog
DiscoverFileSupportJob_discoveryFailed=File support discovery for {0} failed
LookupByNatureJob_discoveryFailed=Project nature discovery for {0} failed
MissingNatureDetector_Title=Marketplace solutions available
MissingNatureDetector_Desc=Some helpful solutions are available on the Marketplace
MissingNatureDetector_Message=Your IDE is missing natures to properly support your projects.\n\
Some extensions on the Eclipse Marketplace can be installed to support those natures.
MissingNatureDetector_jobName=Search Marketplace for Project Nature support ({0})
MissingNatureDetector_ShowSolutions=Show Solutions
MissingNatureDetector_enable=Automatically detect missing natures and propose IDE extensions from Marketplace
PreferencePage_linkToEditorSettings=Use the <a>File Associations</a> page to configure editor discovery for unassociated file types.
MissingNatureDetector_linkToPreferences=See also <a>Project Natures preferences</a>
