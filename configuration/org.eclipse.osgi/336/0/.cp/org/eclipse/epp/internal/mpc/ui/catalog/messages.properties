FavoriteListCatalogItem_defaultListName=Favorites for {0}
FavoritesDiscoveryStrategy_noFavoritesMessage=I'm sorry, this favorites list appears to be empty.
FavoritesDiscoveryStrategy_noFavoritesTitle=Empty favorites list
###############################################################################
# Copyright (c) 2010 The Eclipse Foundation and others.
# All rights reserved. This program and the accompanying materials
# are made available under the terms of the Eclipse Public License v1.0
# which accompanies this distribution, and is available at
# http://www.eclipse.org/legal/epl-v10.html
#
# Contributors:
# 	The Eclipse Foundation - initial API and implementation
###############################################################################
FavoritesDiscoveryStrategy_enterFavoritesUrlMessage=Please enter the URL to a Favorites list you wish to import. Or browse the Marketplace for more lists and drop one here.
FavoritesDiscoveryStrategy_enterFavoritesUrlTitle=Check out other users' favorites
FavoritesDiscoveryStrategy_favoritesCategoryTitle=Explore Favorite Lists
FavoritesDiscoveryStrategy_invalidUrl=Invalid favorites URL: {0} - {1}
MarketplaceCatalog_addedNullEntry={0} added a null item
MarketplaceCatalog_Checking_News=Checking news
MarketplaceCatalog_checkingForUpdates=Checking for updates
MarketplaceCatalog_Discovery_Error=Updating Marketplace catalog encountered problems
MarketplaceCatalog_ErrorReadingRepository=Skipping update check for repository ''{0}'' - failed to read repository. Affected entries: 
MarketplaceCatalog_failedWithError={0} failed with an error
MarketplaceCatalog_InvalidRepositoryUrl=Skipping update check for ''{0}'': Invalid repository url {1}
MarketplaceCatalog_queryFailed=Query failed to complete
MarketplaceCatalog_queryingMarketplace=Querying marketplace
MarketplaceDiscoveryStrategy_badUri=Bad URI for entry ''{0}'' ({1}): {2}
MarketplaceDiscoveryStrategy_catalogCategory=Catalog category
MarketplaceDiscoveryStrategy_ComputingInstalled=Finding installed solutions
MarketplaceDiscoveryStrategy_downloadError=Failed to download image for entry ''{0}'' ({1}) from {2}
MarketplaceDiscoveryStrategy_failedToSaveMarketplaceInfo=Unable to save local Marketplace data
MarketplaceDiscoveryStrategy_FavoritesRefreshing=Refreshing favorite status
MarketplaceDiscoveryStrategy_FavoritesRetrieve=Getting user favorites
MarketplaceDiscoveryStrategy_FavoritesRetrieveError=Failed to get user favorite information
MarketplaceDiscoveryStrategy_findingInstalled=Finding Installed
MarketplaceDiscoveryStrategy_invalidFilter=Invalid filter selection
MarketplaceDiscoveryStrategy_loadingMarketplace=Loading marketplace
MarketplaceDiscoveryStrategy_loadingResources=Loading resources
MarketplaceDiscoveryStrategy_Name_and_Version={0} {1}
MarketplaceDiscoveryStrategy_noNameMatch=No known item found for name '{0}'
MarketplaceDiscoveryStrategy_noUrlMatch=No known item found for url '{0}'
MarketplaceDiscoveryStrategy_ParseError=Error parsing search result entry {0}
MarketplaceDiscoveryStrategy_requestSource=entry ''{0}'' ({1})
MarketplaceDiscoveryStrategy_saveMarketplaceInfoJobName=Saving local Marketplace data
MarketplaceDiscoveryStrategy_searchingMarketplace=Searching Marketplace
MarketplaceDiscoveryStrategy_sendingErrorNotification=Sending Marketplace error notification
MarketplaceDiscoveryStrategy_unidentifiableItem=Unidentifiable item {0}: At least one of id, url or name must be set.
MarketplaceDiscoveryStrategy_unknownFilter=Unknown filter selection
MarketplaceInfo_LoadError=Error restoring marketplace info cache file
MarketplaceNodeCatalogItem_changeSupportAccessError=Error accessing change support
MarketplaceNodeCatalogItem_changeSupportError=Error initializing change support
ResourceProvider_downloadError=Failed to download resource for {0} from {1}
ResourceProvider_FailedCreatingTempDir=Unable to create temporary resource folder {0}
ResourceProvider_retrievingResource=Retrieving catalog resource
ResourceProvider_waitingForDownload=Waiting for catalog resource
