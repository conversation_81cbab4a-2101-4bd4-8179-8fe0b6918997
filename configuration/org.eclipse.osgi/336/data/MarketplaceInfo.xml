<?xml version="1.0" encoding="UTF-8"?>
<java version="1.8.0_152" class="java.beans.XMLDecoder">
 <object class="org.eclipse.epp.internal.mpc.ui.catalog.MarketplaceInfo" id="MarketplaceInfo0">
  <void property="iuToNodeKey">
   <void method="put">
    <string>org.eclipse.team.svn.revision.graph</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>http://marketplace.eclipse.org#1139</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>xtext.feature</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>http://marketplace.eclipse.org#147689</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>org.eclipse.emf.ecp.emfforms.sdk.feature</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>http://marketplace.eclipse.org#1767590</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>com.vaadin.designer.eclipse</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>http://marketplace.eclipse.org#1023</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>org.eclipse.mylyn.bugzilla_feature</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>http://marketplace.eclipse.org#206</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>core.feature</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>http://marketplace.eclipse.org#147689</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>org.eclipse.mylyn_feature</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>http://marketplace.eclipse.org#206</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>org.eclipse.sapphire.ui.swt.xml.editor</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>http://marketplace.eclipse.org#69895</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>org.eclipse.sapphire.sdk</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>http://marketplace.eclipse.org#69895</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>dakara.eclipse.commander.feature</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>http://marketplace.eclipse.org#3655937</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>org.eclipse.sapphire.source</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>http://marketplace.eclipse.org#69895</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>org.eclipse.sapphire.java.jdt</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>http://marketplace.eclipse.org#69895</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>org.eclipse.datatools.enablement.sdk.feature</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>http://marketplace.eclipse.org#1418115</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>org.eclipse.jdt.java8patch</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>http://marketplace.eclipse.org#1583480</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>de.uka.ipd.sdq.reliability.feature</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>http://marketplace.eclipse.org#1209764</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>com.collabnet.reviewboard.feature.feature.group</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>http://marketplace.eclipse.org#870</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>org.eclipse.sapphire.osgi</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>http://marketplace.eclipse.org#69895</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>org.tigris.subversion.subclipse.mylyn.feature.feature.group</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>http://marketplace.eclipse.org#979</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>org.eclipse.team.svn.mylyn</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>http://marketplace.eclipse.org#1139</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>org.antlr.v4</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>http://marketplace.eclipse.org#945</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>org.sqlproc.dsl.feature</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>http://marketplace.eclipse.org#677473</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>jsonedit-feature</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>http://marketplace.eclipse.org#945</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>org.eclipse.team.svn.nl1</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>http://marketplace.eclipse.org#1139</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>org.eclipse.sapphire.ui</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>http://marketplace.eclipse.org#69895</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>com.vaadin.integration.eclipse</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>http://marketplace.eclipse.org#1023</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>org.entirej.fx.feature</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>http://marketplace.eclipse.org#867720</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>com.ibm.ws.appconversion_feature.tomcat</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>http://marketplace.eclipse.org#1744576</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>net.sf.ttd.plugin.cdt</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>http://marketplace.eclipse.org#439</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>com.github.elucash.lambda4jdt.feature</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>http://marketplace.eclipse.org#850</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>org.severe.JRipplesFeature</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>http://marketplace.eclipse.org#17968</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>com.ibm.ws.appconversion_feature.was2was</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>http://marketplace.eclipse.org#1744576</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>com.vaadin.designer2.eclipse</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>http://marketplace.eclipse.org#1023</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>net.vtst.ow.eclipse.soy.feature</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>http://marketplace.eclipse.org#1316084</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>org.eclipse.sapphire.modeling.xml</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>http://marketplace.eclipse.org#69895</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>org.tigris.subversion.subclipse.feature.group</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>http://marketplace.eclipse.org#979</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>de.unkrig.subclipse.feature.group</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>http://marketplace.eclipse.org#914943</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>com.ibm.issw.migr.wcmt.feature.was</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>http://marketplace.eclipse.org#1744576</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>org.emftools.emf2gv</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>http://marketplace.eclipse.org#21954</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>com.ibm.ws.migrationtoolkit.cloud.feature</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>http://marketplace.eclipse.org#1744576</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>com.ibm.ws.appconversion_feature.oracle</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>http://marketplace.eclipse.org#1744576</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>argument.feature</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>http://marketplace.eclipse.org#147689</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>com.genuitec.eclipse.code.feature</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>http://marketplace.eclipse.org#4008412</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>com.triadsoft.properties.editor.feature</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>http://marketplace.eclipse.org#9285</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>org.zeroturnaround.eclipse.wtp.feature</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>http://marketplace.eclipse.org#29591</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>com.collabnet.gerrit.feature.feature.group</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>http://marketplace.eclipse.org#870</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>org.eclipse.sapphire.ui.swt.gef</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>http://marketplace.eclipse.org#69895</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>net.sf.ttd.plugin.jdt</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>http://marketplace.eclipse.org#439</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>ContextQuickie</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>http://marketplace.eclipse.org#2589579</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>org.entirej.ide.feature</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>http://marketplace.eclipse.org#867720</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>com.collabnet.git.feature.feature.group</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>http://marketplace.eclipse.org#870</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>AllInstancesFeature</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>http://marketplace.eclipse.org#57146</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>com.collabnet.desktop.feature.feature.group</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>http://marketplace.eclipse.org#870</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>org.eclipse.sapphire.java</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>http://marketplace.eclipse.org#69895</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>net.certiv.fluentmark.feature</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>http://marketplace.eclipse.org#3217596</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>live_py_feature</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>http://marketplace.eclipse.org#2806369</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>org.tigris.subversion.subclipse.graph.feature.feature.group</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>http://marketplace.eclipse.org#979</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>org.culturegraph.mf.ide.sdk</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>http://marketplace.eclipse.org#800079</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>org.zeroturnaround.eclipse.feature</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>http://marketplace.eclipse.org#29591</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>com.ibm.ws.appconversion_feature.weblogic</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>http://marketplace.eclipse.org#1744576</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>de.hackerdan.teatime</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>http://marketplace.eclipse.org#321</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>org.eclipse.team.svn.resource.ignore.rules.jdt</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>http://marketplace.eclipse.org#1139</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>de.uka.ipd.sdq.simucom.feature</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>http://marketplace.eclipse.org#1209764</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>com.ibm.issw.migr.wcmt.feature.wls</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>http://marketplace.eclipse.org#1744576</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>com.ibm.ws.configmigration.tomcat_feature</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>http://marketplace.eclipse.org#1744576</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>org.eclipse.team.svn.m2e</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>http://marketplace.eclipse.org#1139</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>org.eclipse.mylyn.ide_feature</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>http://marketplace.eclipse.org#206</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>com.ibm.issw.migr.wcmt.feature.jboss</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>http://marketplace.eclipse.org#1744576</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>de.uka.ipd.sdq.pcm.diagram.feature</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>http://marketplace.eclipse.org#1209764</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>tk.Knaup.psswfeature</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>http://marketplace.eclipse.org#2483041</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>org.eclipse.equinox.p2.replication.feature</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>http://marketplace.eclipse.org#1097</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>dailydilbert.feature</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>http://marketplace.eclipse.org#12216</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>de.uka.ipd.sdq.pcm.feature</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>http://marketplace.eclipse.org#1209764</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>name.schedenig.eclipse.quickbookmarks</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>http://marketplace.eclipse.org#1797751</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>org.eclipse.cft.server.core.feature</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>http://marketplace.eclipse.org#106257</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>org.eclipse.cft.server.ui.feature</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>http://marketplace.eclipse.org#106257</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>de.hackerdan.note</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>http://marketplace.eclipse.org#146</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>ontology.feature</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>http://marketplace.eclipse.org#147689</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>org.tigris.subversion.clientadapter.svnkit.feature.feature.group</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>http://marketplace.eclipse.org#979</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>com.javastud.studio</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>http://marketplace.eclipse.org#2892308</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>de.uka.ipd.sdq.prototype.feature</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>http://marketplace.eclipse.org#1209764</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>org.jenerate.feature</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>http://marketplace.eclipse.org#2294878</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>ecobertura</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>http://marketplace.eclipse.org#1179</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>org.eclipse.sapphire</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>http://marketplace.eclipse.org#69895</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>org.eclipse.sapphire.platform</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>http://marketplace.eclipse.org#69895</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>net.sourceforge.vrapper</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>http://marketplace.eclipse.org#881</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>org.eclipse.team.svn</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>http://marketplace.eclipse.org#1139</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>org.eclipse.mylyn.team_feature</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>http://marketplace.eclipse.org#206</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>net.sf.ttd.core</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>http://marketplace.eclipse.org#439</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>io.github.pyvesb.notepad4e</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>http://marketplace.eclipse.org#3108021</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>OcaIDEFeature</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>http://marketplace.eclipse.org#556</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>name.schedenig.eclipse.clippets</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>http://marketplace.eclipse.org#1628365</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>istar.feature</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>http://marketplace.eclipse.org#147689</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>event.feature</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>http://marketplace.eclipse.org#147689</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>org.eclipse.team.svn.source</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>http://marketplace.eclipse.org#1139</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>net.sf.ttd.plugin.dltk</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>http://marketplace.eclipse.org#439</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>problem.feature</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>http://marketplace.eclipse.org#147689</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>org.ant_vis</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>http://marketplace.eclipse.org#43849</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>org.tigris.subversion.clientadapter.javahl.feature.feature.group</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>http://marketplace.eclipse.org#979</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>com.aspose.ecplugin.newproject.feature</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>http://marketplace.eclipse.org#686636</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>com.ibm.ws.appconversion_feature.jboss</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>http://marketplace.eclipse.org#1744576</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>de.unkrig.subclipse.feature</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>http://marketplace.eclipse.org#914943</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>net.vtst.ow.eclipse.js.closure.feature</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>http://marketplace.eclipse.org#1316084</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>de.uka.ipd.sdq.simulation.abstractsimengine.feature</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>http://marketplace.eclipse.org#1209764</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>it.unitn.disi.sistar.feature</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>http://marketplace.eclipse.org#147689</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>org.eclipse.mylyn.context_feature</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>http://marketplace.eclipse.org#206</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>com.dubture.twig.feature</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>http://marketplace.eclipse.org#220370</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>de.uka.ipd.sdq.pcm.resultdecorator.feature</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>http://marketplace.eclipse.org#1209764</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>org.zeroturnaround.eclipse.m2e.feature</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>http://marketplace.eclipse.org#29591</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>de.uka.ipd.sdq.pcmsolver.feature</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>http://marketplace.eclipse.org#1209764</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>org.nodeclipse.pluginslist.feature</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>http://marketplace.eclipse.org#1473856</string>
     </void>
    </object>
   </void>
  </void>
  <void property="nodeKeyToIU">
   <void method="put">
    <string>http://marketplace.eclipse.org#3655937</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>dakara.eclipse.commander.feature</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>http://marketplace.eclipse.org#3217596</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>net.certiv.fluentmark.feature</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>http://marketplace.eclipse.org#1418115</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>org.eclipse.datatools.enablement.sdk.feature</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>http://marketplace.eclipse.org#2483041</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>tk.Knaup.psswfeature</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>http://marketplace.eclipse.org#686636</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>com.aspose.ecplugin.newproject.feature</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>http://marketplace.eclipse.org#69895</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>org.eclipse.sapphire.java.jdt</string>
     </void>
     <void method="add">
      <string>org.eclipse.sapphire.java</string>
     </void>
     <void method="add">
      <string>org.eclipse.sapphire.modeling.xml</string>
     </void>
     <void method="add">
      <string>org.eclipse.sapphire</string>
     </void>
     <void method="add">
      <string>org.eclipse.sapphire.osgi</string>
     </void>
     <void method="add">
      <string>org.eclipse.sapphire.platform</string>
     </void>
     <void method="add">
      <string>org.eclipse.sapphire.sdk</string>
     </void>
     <void method="add">
      <string>org.eclipse.sapphire.source</string>
     </void>
     <void method="add">
      <string>org.eclipse.sapphire.ui.swt.gef</string>
     </void>
     <void method="add">
      <string>org.eclipse.sapphire.ui.swt.xml.editor</string>
     </void>
     <void method="add">
      <string>org.eclipse.sapphire.ui</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>http://marketplace.eclipse.org#1139</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>org.eclipse.team.svn</string>
     </void>
     <void method="add">
      <string>org.eclipse.team.svn.resource.ignore.rules.jdt</string>
     </void>
     <void method="add">
      <string>org.eclipse.team.svn.revision.graph</string>
     </void>
     <void method="add">
      <string>org.eclipse.team.svn.mylyn</string>
     </void>
     <void method="add">
      <string>org.eclipse.team.svn.nl1</string>
     </void>
     <void method="add">
      <string>org.eclipse.team.svn.m2e</string>
     </void>
     <void method="add">
      <string>org.eclipse.team.svn.source</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>http://marketplace.eclipse.org#881</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>net.sourceforge.vrapper</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>http://marketplace.eclipse.org#321</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>de.hackerdan.teatime</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>http://marketplace.eclipse.org#146</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>de.hackerdan.note</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>http://marketplace.eclipse.org#21954</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>org.emftools.emf2gv</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>http://marketplace.eclipse.org#914943</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>de.unkrig.subclipse.feature.group</string>
     </void>
     <void method="add">
      <string>de.unkrig.subclipse.feature</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>http://marketplace.eclipse.org#1209764</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>de.uka.ipd.sdq.pcm.feature</string>
     </void>
     <void method="add">
      <string>de.uka.ipd.sdq.pcm.diagram.feature</string>
     </void>
     <void method="add">
      <string>de.uka.ipd.sdq.pcm.resultdecorator.feature</string>
     </void>
     <void method="add">
      <string>de.uka.ipd.sdq.pcmsolver.feature</string>
     </void>
     <void method="add">
      <string>de.uka.ipd.sdq.simucom.feature</string>
     </void>
     <void method="add">
      <string>de.uka.ipd.sdq.simulation.abstractsimengine.feature</string>
     </void>
     <void method="add">
      <string>de.uka.ipd.sdq.prototype.feature</string>
     </void>
     <void method="add">
      <string>de.uka.ipd.sdq.reliability.feature</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>http://marketplace.eclipse.org#800079</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>org.culturegraph.mf.ide.sdk</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>http://marketplace.eclipse.org#206</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>org.eclipse.mylyn_feature</string>
     </void>
     <void method="add">
      <string>org.eclipse.mylyn.bugzilla_feature</string>
     </void>
     <void method="add">
      <string>org.eclipse.mylyn.ide_feature</string>
     </void>
     <void method="add">
      <string>org.eclipse.mylyn.context_feature</string>
     </void>
     <void method="add">
      <string>org.eclipse.mylyn.team_feature</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>http://marketplace.eclipse.org#945</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>org.antlr.v4</string>
     </void>
     <void method="add">
      <string>jsonedit-feature</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>http://marketplace.eclipse.org#979</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>org.tigris.subversion.subclipse.feature.group</string>
     </void>
     <void method="add">
      <string>org.tigris.subversion.clientadapter.javahl.feature.feature.group</string>
     </void>
     <void method="add">
      <string>org.tigris.subversion.subclipse.graph.feature.feature.group</string>
     </void>
     <void method="add">
      <string>org.tigris.subversion.subclipse.mylyn.feature.feature.group</string>
     </void>
     <void method="add">
      <string>org.tigris.subversion.clientadapter.svnkit.feature.feature.group</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>http://marketplace.eclipse.org#1316084</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>net.vtst.ow.eclipse.soy.feature</string>
     </void>
     <void method="add">
      <string>net.vtst.ow.eclipse.js.closure.feature</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>http://marketplace.eclipse.org#106257</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>org.eclipse.cft.server.core.feature</string>
     </void>
     <void method="add">
      <string>org.eclipse.cft.server.ui.feature</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>http://marketplace.eclipse.org#29591</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>org.zeroturnaround.eclipse.feature</string>
     </void>
     <void method="add">
      <string>org.zeroturnaround.eclipse.wtp.feature</string>
     </void>
     <void method="add">
      <string>org.zeroturnaround.eclipse.m2e.feature</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>http://marketplace.eclipse.org#2806369</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>live_py_feature</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>http://marketplace.eclipse.org#1583480</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>org.eclipse.jdt.java8patch</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>http://marketplace.eclipse.org#2589579</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>ContextQuickie</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>http://marketplace.eclipse.org#9285</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>com.triadsoft.properties.editor.feature</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>http://marketplace.eclipse.org#1473856</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>org.nodeclipse.pluginslist.feature</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>http://marketplace.eclipse.org#147689</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>problem.feature</string>
     </void>
     <void method="add">
      <string>core.feature</string>
     </void>
     <void method="add">
      <string>argument.feature</string>
     </void>
     <void method="add">
      <string>event.feature</string>
     </void>
     <void method="add">
      <string>xtext.feature</string>
     </void>
     <void method="add">
      <string>ontology.feature</string>
     </void>
     <void method="add">
      <string>istar.feature</string>
     </void>
     <void method="add">
      <string>it.unitn.disi.sistar.feature</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>http://marketplace.eclipse.org#1023</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>com.vaadin.integration.eclipse</string>
     </void>
     <void method="add">
      <string>com.vaadin.designer2.eclipse</string>
     </void>
     <void method="add">
      <string>com.vaadin.designer.eclipse</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>http://marketplace.eclipse.org#2294878</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>org.jenerate.feature</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>http://marketplace.eclipse.org#677473</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>org.sqlproc.dsl.feature</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>http://marketplace.eclipse.org#1628365</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>name.schedenig.eclipse.clippets</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>http://marketplace.eclipse.org#867720</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>org.entirej.ide.feature</string>
     </void>
     <void method="add">
      <string>org.entirej.fx.feature</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>http://marketplace.eclipse.org#870</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>com.collabnet.desktop.feature.feature.group</string>
     </void>
     <void method="add">
      <string>com.collabnet.gerrit.feature.feature.group</string>
     </void>
     <void method="add">
      <string>com.collabnet.git.feature.feature.group</string>
     </void>
     <void method="add">
      <string>com.collabnet.reviewboard.feature.feature.group</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>http://marketplace.eclipse.org#850</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>com.github.elucash.lambda4jdt.feature</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>http://marketplace.eclipse.org#1744576</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>com.ibm.ws.appconversion_feature.tomcat</string>
     </void>
     <void method="add">
      <string>com.ibm.ws.configmigration.tomcat_feature</string>
     </void>
     <void method="add">
      <string>com.ibm.ws.appconversion_feature.weblogic</string>
     </void>
     <void method="add">
      <string>com.ibm.ws.appconversion_feature.jboss</string>
     </void>
     <void method="add">
      <string>com.ibm.ws.appconversion_feature.oracle</string>
     </void>
     <void method="add">
      <string>com.ibm.ws.migrationtoolkit.cloud.feature</string>
     </void>
     <void method="add">
      <string>com.ibm.ws.appconversion_feature.was2was</string>
     </void>
     <void method="add">
      <string>com.ibm.issw.migr.wcmt.feature.wls</string>
     </void>
     <void method="add">
      <string>com.ibm.issw.migr.wcmt.feature.jboss</string>
     </void>
     <void method="add">
      <string>com.ibm.issw.migr.wcmt.feature.was</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>http://marketplace.eclipse.org#556</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>OcaIDEFeature</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>http://marketplace.eclipse.org#220370</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>com.dubture.twig.feature</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>http://marketplace.eclipse.org#439</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>net.sf.ttd.plugin.cdt</string>
     </void>
     <void method="add">
      <string>net.sf.ttd.plugin.jdt</string>
     </void>
     <void method="add">
      <string>net.sf.ttd.plugin.dltk</string>
     </void>
     <void method="add">
      <string>net.sf.ttd.core</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>http://marketplace.eclipse.org#43849</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>org.ant_vis</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>http://marketplace.eclipse.org#17968</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>org.severe.JRipplesFeature</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>http://marketplace.eclipse.org#12216</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>dailydilbert.feature</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>http://marketplace.eclipse.org#4008412</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>com.genuitec.eclipse.code.feature</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>http://marketplace.eclipse.org#1797751</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>name.schedenig.eclipse.quickbookmarks</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>http://marketplace.eclipse.org#57146</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>AllInstancesFeature</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>http://marketplace.eclipse.org#2892308</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>com.javastud.studio</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>http://marketplace.eclipse.org#1179</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>ecobertura</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>http://marketplace.eclipse.org#1097</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>org.eclipse.equinox.p2.replication.feature</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>http://marketplace.eclipse.org#1767590</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>org.eclipse.emf.ecp.emfforms.sdk.feature</string>
     </void>
    </object>
   </void>
   <void method="put">
    <string>http://marketplace.eclipse.org#3108021</string>
    <object class="java.util.ArrayList">
     <void method="add">
      <string>io.github.pyvesb.notepad4e</string>
     </void>
    </object>
   </void>
  </void>
 </object>
</java>
