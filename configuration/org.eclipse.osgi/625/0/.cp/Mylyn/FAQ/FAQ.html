<?xml version='1.0' encoding='utf-8' ?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
		<title>Mylyn FAQ</title>
		<link type="text/css" rel="stylesheet" href="../../book.css"/>
	</head>
	<body>
		<table class="navigation" style="width: 100%;" border="0" summary="navigation">
			<tr>
				<th style="width: 100%" align="center" colspan="3">Mylyn FAQ</th>
			</tr>
			<tr>
				<td style="width: 20%" align="left"></td>
				<td style="width: 60%" align="center"></td>
				<td style="width: 20%" align="right">
					<a href="Installation.html" title="Installation">
						<img alt="Next" border="0" src="../../images/next.gif"/>
					</a>
				</td>
			</tr>
			<tr>
				<td style="width: 20%" align="left" valign="top"></td>
				<td style="width: 60%" align="center"></td>
				<td style="width: 20%" align="right" valign="top">Installation</td>
			</tr>
		</table><hr class="navigation-separator"/>
		<p>
			<a href="../../Mylyn/User_Guide/User-Guide.html" title="Mylyn/User Guide">Mylyn/User Guide</a>, 

			<a href="http://eclipse.org/mylyn" target="mylyn_external">Mylyn Home</a>,

			<a href="http://wiki.eclipse.org/index.php?title=Special:Userlogin&amp;returnto=Special:Userlogout" target="mylyn_external">Log in to edit</a>
		</p>
		<p>For instructions on using Mylyn, see the 
			<a href="../../Mylyn/User_Guide/User-Guide.html" title="Mylyn/User Guide">Mylyn/User Guide</a>. For instructions on developing and contributing to Mylyn, see the 
			<a href="http://wiki.eclipse.org/Mylyn/Contributor_Reference" title="Mylyn/Contributor Reference" target="mylyn_external">Mylyn/Contributor Reference</a>.
		</p>
		<h1 id="What_is_Mylyn.3F">What is Mylyn?</h1>
		<p>For 
			<b>tutorials and articles on using Mylyn</b> refer to the 
			<b>
				<a href="http://www.eclipse.org/mylyn/start/" target="mylyn_external">Get Started</a>
			</b> page.
		</p>
		<p>Mylyn is a task-focused interface for Eclipse that makes working with very large workspaces as easy as working with small ones.  Mylyn extends Eclipse with mechanisms for keeping track of the tasks that you work on. A task is defined as any unit of work that you want to recall or share with others, such as a bug reported by a user or a note to yourself about improving a feature. You can store tasks locally in your workspace, or they can come from one or more task repositories. To connect to a task repository, you must have a connector that supports that repository. (A task repository is a bug/ticket/issue tracker such as Bugzilla, Trac, JIRA, and 
			<a href="http://wiki.eclipse.org/index.php/Mylyn/Extensions" target="mylyn_external">others</a>). 
		</p>
		<p>Once your tasks are integrated, Mylyn monitors your work activity on those tasks to identify information relevant to the task-at-hand.  Mylyn monitors Eclipse and captures your interaction in a task context. System artifacts such as files, types, methods, and fields get assigned a degree-of-interest based on how recently and frequently you interact with them.  This results in uninteresting elements being filtered from view within Eclipse, allowing you to focus in on what is important.  From this, Mylyn creates a task context, which is the set of all artifacts related to your task. These can include methods you have edited, APIs you have referred to, and documents you have browsed. Mylyn uses this task context to focus the Eclipse UI on interesting information, hide what's uninteresting, and automatically find what's related. Having the information you need to get your work done at your fingertips improves your productivity by reducing the time you spend searching, scrolling, and navigating. By making task context explicit, Mylyn also facilitates multitasking, planning, reusing past efforts, and sharing expertise.</p><hr class="navigation-separator"/>
		<table class="navigation" style="width: 100%;" border="0" summary="navigation">
			<tr>
				<td style="width: 20%" align="left"></td>
				<td style="width: 60%" align="center"></td>
				<td style="width: 20%" align="right">
					<a href="Installation.html" title="Installation">
						<img alt="Next" border="0" src="../../images/next.gif"/>
					</a>
				</td>
			</tr>
			<tr>
				<td style="width: 20%" align="left" valign="top"></td>
				<td style="width: 60%" align="center"></td>
				<td style="width: 20%" align="right" valign="top">Installation</td>
			</tr>
		</table>
	</body>
</html>