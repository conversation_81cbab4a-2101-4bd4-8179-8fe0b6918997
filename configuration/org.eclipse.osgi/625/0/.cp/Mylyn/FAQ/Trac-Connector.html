<?xml version='1.0' encoding='utf-8' ?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
		<title>Mylyn FAQ - Trac Connector</title>
		<link type="text/css" rel="stylesheet" href="../../book.css"/>
	</head>
	<body>
		<table class="navigation" style="width: 100%;" border="0" summary="navigation">
			<tr>
				<th style="width: 100%" align="center" colspan="3">Trac Connector</th>
			</tr>
			<tr>
				<td style="width: 20%" align="left">
					<a href="JIRA-Connector.html" title="JIRA Connector">
						<img alt="Previous" border="0" src="../../images/prev.gif"/>
					</a>
				</td>
				<td style="width: 60%" align="center"></td>
				<td style="width: 20%" align="right">
					<a href="Web-Templates-Connector.html" title="Web Templates Connector">
						<img alt="Next" border="0" src="../../images/next.gif"/>
					</a>
				</td>
			</tr>
			<tr>
				<td style="width: 20%" align="left" valign="top">JIRA Connector</td>
				<td style="width: 60%" align="center"></td>
				<td style="width: 20%" align="right" valign="top">Web Templates Connector</td>
			</tr>
		</table><hr class="navigation-separator"/>
		<h1 id="Trac_Connector">Trac Connector</h1>
		<h2 id="What_are_the_server_requirements.3F">What are the server requirements?</h2>
		<p>The Trac connector supports two different access methods: The recommended XML-RPC mode offers complete integration with Mylyn but requires additional setup and privileges which may not be available to all users. Web mode offers less functionality but will work with any public Trac repository. </p>
		<h4 id="XML-RPC_.28recommended.29">XML-RPC (recommended)</h4>
		<p>Requirements:</p>
		<ul>
			<li>Trac 0.10 or later</li>
			<li>
				<a href="http://trac-hacks.org/wiki/XmlRpcPlugin" target="mylyn_external">XmlRpcPlugin</a> rev. 1950 or later
			</li>
		</ul>
		<p>This access method offers editing of tasks in a rich editor, attachment support and offline editing. It requires the 
			<a href="http://trac-hacks.org/wiki/XmlRpcPlugin" target="mylyn_external">XmlRpcPlugin</a> for Trac to be enabled for the accessed repository. The XmlRpcPlugin provides a remote interface to the Trac repository and is distributed separately from Trac (see 
			<a href="http://trac.edgewall.org/ticket/217" target="mylyn_external">#217</a>). See these 
			<a href="http://trac-hacks.org/wiki/XmlRpcPlugin#Installation" target="mylyn_external">install instructions</a> for requirements and documentation.
		</p>
		<p>In order to access the repository through XML_RPC each user needs to have the corresponding permission which can be configured through Trac's admin tool:</p>
		<pre>trac-admin &lt;/path/to/projenv&gt; permission add &lt;user&gt; XML_RPC
</pre>
		<h4 id="Web">Web</h4>
		<p>Requirements:</p>
		<ul>
			<li>Trac 0.9 or later</li>
		</ul>
		<p>In this mode the standard Trac web interface is used for repository access. Tickets may be created and edited through a web browser. Offline editing and attachments are not supported.</p>
		<h2 id="Recommended_Trac_version">Recommended Trac version</h2>
		<p>Mylyn works with any stable Trac release that is version 0.9 or later (see below for known limitations). Mylyn works best when used with Trac’s XML-RPC Plugin (see above) but we do not recommend a particular Trac version. </p>
		<p>The Trac connector tests are run against these Trac versions:</p>
		<ul>
			<li>Trac 0.11.6, XML-RPC Plugin 1.0.6 (r7194)</li>
			<li>Trac 0.10.5, XML-RPC Plugin 0.0.2 (r2125)</li>
			<li>Trac 0.9.6 (support ended with Mylyn 3.3)</li>
		</ul>
		<h2 id="Does_Mylyn_support_Trac_0.11.3F">Does Mylyn support Trac 0.11?</h2>
		<p>When Mylyn 3.2.1 or later is used with the latest version of the 
			<a href="http://trac-hacks.org/wiki/XmlRpcPlugin" target="mylyn_external">Trac XmlRpcPlugin</a> from 
			<a href="http://trac-hacks.org/svn/xmlrpcplugin/trunk/" target="mylyn_external">SVN trunk</a> the Mylyn Trac connector is fully functional (
			<a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=175211" target="mylyn_external">bug 175211</a>.
		</p>
		<h2 id="Why_do_I_get_an_HTTP_Error_500_Internal_server_error_when_creating_a_ticket_that_contains_non-ASCII_characters.3F">Why do I get an HTTP Error 500 Internal server error when creating a ticket that contains non-ASCII characters?</h2>
		<p>Problems related to character encodings have been reported when Trac is run with Python 2.3. Upgrading to Python 2.4 may help to resolve this. Please comment on 
			<a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=188363" target="mylyn_external">bug #188363</a> if you encounter an internal server error when creating a ticket that contains non-ASCII characters.
		</p>
		<h2 id="Known_limitations_2">Known limitations</h2>
		<ul>
			<li>Trac 0.10.1 
				<ul>
					<li>Known incompatibility with Trac XML-RPC Plugin. See 
						<a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=164272" target="mylyn_external">bug 164272</a> for details.
					</li>
				</ul>
			</li>
			<li>Trac 0.10.3 
				<ul>
					<li>Known incompatibility with old versions of the Trac HttpAuthPlugin (fixed in revision 1890 or later). The plug-in enables basic auth for XML-RPC when Trac AccountManagerPlugin for form based authentication is used. See 
						<a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=168413" target="mylyn_external">bug 168413</a> for details.
					</li>
				</ul>
			</li>
			<li>Trac 0.11
				<ul>
					<li>Known incompatibility with XML-RPC Plugin version 1.0.2 - 1.0.5. Using 1.0.6 or later is recommended.</li>
				</ul>
			</li>
		</ul>
		<h2 id="Why_are_tasks_opened_in_a_web_browser_and_not_in_the_rich_editor.3F">Why are tasks opened in a web browser and not in the rich editor?</h2>
		<p>Please make sure that the access type in the repository properties is set to XML-RPC. This requires Trac 0.10 and XML-RPC (see above).</p>
		<h2 id="Which_URLs_does_Mylyn_access_in_a_Trac_repository.3F">Which URLs does Mylyn access in a Trac repository?</h2>
		<p>XML-RPC:</p>
		<ul>
			<li>The expected URL is either /xmlrpc or /login/xmlrpc (if login credentials are provided)</li>
		</ul>
		<p>Web:</p>
		<ul>
			<li>Authentication: /login </li>
			<li>Querying: /query?format=tab…</li>
			<li>Synchronizing ticket details: /ticket/…</li>
			<li>Getting repository configuration to populate query dialog: /query or /newticket</li>
		</ul>
		<p>The web mode relies on screen scraping and is likely to fail if the design
			(i.e. HTML output) of the Trac repository is heavily customized.</p>
		<h2 id="Problems_opening_the_web_editor_on_Linux">Problems opening the web editor on Linux</h2>
		<p>If you’re having problems opening the web task editor on Linux and the message 
			<b>Could not create Browser page: XPCOM error -**********</b> appears in the error log, try installing the packages 
			<b>xulrunner</b> and 
			<b>xulrunner-gnome-support</b> on your Linux distribution.
		</p>
		<h2 id="Which_Trac_Plugins_are_supported_by_Mylyn.3F">Which Trac Plugins are supported by Mylyn?</h2>
		<ul>
			<li>
				<a href="http://trac-hacks.org/wiki/XmlRpcPlugin" target="mylyn_external">XML-RPC Plugin</a> for rich-editing and attachment support
			</li>
			<li>
				<a href="http://trac-hacks.org/wiki/AccountManagerPlugin" target="mylyn_external">Account Manager Plugin</a> for form-based authentication (Mylyn 2.0 or higher)
			</li>
			<li>
				<a href="http://trac-hacks.org/wiki/MasterTicketsPlugin" target="mylyn_external">Master Tickets Plugin</a> for sub-task support (Mylyn 2.3 or higher)
			</li>
		</ul><hr class="navigation-separator"/>
		<table class="navigation" style="width: 100%;" border="0" summary="navigation">
			<tr>
				<td style="width: 20%" align="left">
					<a href="JIRA-Connector.html" title="JIRA Connector">
						<img alt="Previous" border="0" src="../../images/prev.gif"/>
					</a>
				</td>
				<td style="width: 60%" align="center">
					<a href="FAQ.html" title="Mylyn FAQ">
						<img alt="Mylyn FAQ" border="0" src="../../images/home.gif"/>
					</a>
				</td>
				<td style="width: 20%" align="right">
					<a href="Web-Templates-Connector.html" title="Web Templates Connector">
						<img alt="Next" border="0" src="../../images/next.gif"/>
					</a>
				</td>
			</tr>
			<tr>
				<td style="width: 20%" align="left" valign="top">JIRA Connector</td>
				<td style="width: 60%" align="center"></td>
				<td style="width: 20%" align="right" valign="top">Web Templates Connector</td>
			</tr>
		</table>
	</body>
</html>