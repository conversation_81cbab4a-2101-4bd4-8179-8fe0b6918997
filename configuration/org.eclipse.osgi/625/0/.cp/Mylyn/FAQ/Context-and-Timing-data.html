<?xml version='1.0' encoding='utf-8' ?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
		<title>Mylyn FAQ - Context and Timing data</title>
		<link type="text/css" rel="stylesheet" href="../../book.css"/>
	</head>
	<body>
		<table class="navigation" style="width: 100%;" border="0" summary="navigation">
			<tr>
				<th style="width: 100%" align="center" colspan="3">Context and Timing data</th>
			</tr>
			<tr>
				<td style="width: 20%" align="left">
					<a href="Task-Focused-UI.html" title="Task-Focused UI">
						<img alt="Previous" border="0" src="../../images/prev.gif"/>
					</a>
				</td>
				<td style="width: 60%" align="center"></td>
				<td style="width: 20%" align="right">
					<a href="Java-Development.html" title="Java Development">
						<img alt="Next" border="0" src="../../images/next.gif"/>
					</a>
				</td>
			</tr>
			<tr>
				<td style="width: 20%" align="left" valign="top">Task-Focused UI</td>
				<td style="width: 60%" align="center"></td>
				<td style="width: 20%" align="right" valign="top">Java Development</td>
			</tr>
		</table><hr class="navigation-separator"/>
		<h1 id="Context_and_Timing_data">Context and Timing data</h1>
		<h2 id="How_do_I_prevent_code_checked_out_from_polluting_my_task_context.3F">How do I prevent code checked out from polluting my task context?</h2>
		<p>When checking out a new project, you must first deactivate the active task. Otherwise all newly created files will become interesting. </p>
		<h2 id="Why_do_I_see_strange_elapsed_times_on_my_Planning_tab.3F">Why do I see strange elapsed times on my Planning tab?</h2>
		<p>Some platform/jvm combinations can fill with invalid timestamps. This is known to have happened on Mac OSX 10.4 with Java 1.5. See 
			<a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=207419" target="mylyn_external">bug 207419</a>.
			To resolve you can try and manually edit &lt;workspace&gt;/.metadata/.mylyn/contexts/activity.xml.zip.
		</p>
		<h2 id="Is_the_backwards_compatibility_and_refactoring_of_task_context_handled.3F">Is the backwards compatibility and refactoring of task context handled?</h2>
		<ul>
			<li>The Task List and Context Store are compatible across all currently-supported Eclipse versions.  This means that you can use the same .mylyn data in both Eclipse 3.x.  </li>
		</ul>
		<ul>
			<li>If elements have been renamed they may not appear as interesting when the context is activated.</li>
		</ul><hr class="navigation-separator"/>
		<table class="navigation" style="width: 100%;" border="0" summary="navigation">
			<tr>
				<td style="width: 20%" align="left">
					<a href="Task-Focused-UI.html" title="Task-Focused UI">
						<img alt="Previous" border="0" src="../../images/prev.gif"/>
					</a>
				</td>
				<td style="width: 60%" align="center">
					<a href="FAQ.html" title="Mylyn FAQ">
						<img alt="Mylyn FAQ" border="0" src="../../images/home.gif"/>
					</a>
				</td>
				<td style="width: 20%" align="right">
					<a href="Java-Development.html" title="Java Development">
						<img alt="Next" border="0" src="../../images/next.gif"/>
					</a>
				</td>
			</tr>
			<tr>
				<td style="width: 20%" align="left" valign="top">Task-Focused UI</td>
				<td style="width: 60%" align="center"></td>
				<td style="width: 20%" align="right" valign="top">Java Development</td>
			</tr>
		</table>
	</body>
</html>