<?xml version='1.0' encoding='utf-8' ?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
		<title>Mylyn User Guide - Task Repositories</title>
		<link type="text/css" rel="stylesheet" href="../../book.css"/>
	</head>
	<body>
		<table class="navigation" style="width: 100%;" border="0" summary="navigation">
			<tr>
				<th style="width: 100%" align="center" colspan="3">Task Repositories</th>
			</tr>
			<tr>
				<td style="width: 20%" align="left">
					<a href="User-Guide.html" title="Mylyn User Guide">
						<img alt="Previous" border="0" src="../../images/prev.gif"/>
					</a>
				</td>
				<td style="width: 60%" align="center"></td>
				<td style="width: 20%" align="right">
					<a href="Task-Editor.html" title="Task Editor">
						<img alt="Next" border="0" src="../../images/next.gif"/>
					</a>
				</td>
			</tr>
			<tr>
				<td style="width: 20%" align="left" valign="top">Mylyn User Guide</td>
				<td style="width: 60%" align="center"></td>
				<td style="width: 20%" align="right" valign="top">Task Editor</td>
			</tr>
		</table><hr class="navigation-separator"/>
		<h1 id="Task_Repositories">Task Repositories</h1>
		<p>
			<img border="0" src="images/Feature-Reference-3.0-Add-Task-Repository.png"/>
		</p>
		<p>Use the Task Repositories view to configure Mylyn to connect to your team's shared task repository (bug or issue tracker):</p>
		<ul>
			<li>Open the task repositories view by navigating to 
				<i>Window -&gt; Show View -&gt; Other -&gt; Mylyn -&gt; Task Repositories</i> 
			</li>
			<li>Click the "Add Task Repository" button located in the view's toolbar.</li>
			<li>Select the type of repository you wish to connect to and click "Next". If you don't see your repository type, you will need to install the appropriate connector.</li>
			<li>Enter the repository's address and your login credentials. After filling in these details, press the 
				<i>Validate</i> button to ensure the repository exists and your login credentials are valid. Once the settings validate, Click Finish. Note that the settings will vary somewhat depending on the type of repository that you are connecting to. The screenshot shows the settings fore connecting to a Bugzilla repository.
			</li>
			<li>You should now see the new repository in the 
				<i>Task Repositories</i> view.
			</li>
			<li>Now that you have created a repository, you may add queries to the Task List.</li>
		</ul><hr class="navigation-separator"/>
		<table class="navigation" style="width: 100%;" border="0" summary="navigation">
			<tr>
				<td style="width: 20%" align="left">
					<a href="User-Guide.html" title="Mylyn User Guide">
						<img alt="Previous" border="0" src="../../images/prev.gif"/>
					</a>
				</td>
				<td style="width: 60%" align="center">
					<a href="User-Guide.html" title="Mylyn User Guide">
						<img alt="Mylyn User Guide" border="0" src="../../images/home.gif"/>
					</a>
				</td>
				<td style="width: 20%" align="right">
					<a href="Task-Editor.html" title="Task Editor">
						<img alt="Next" border="0" src="../../images/next.gif"/>
					</a>
				</td>
			</tr>
			<tr>
				<td style="width: 20%" align="left" valign="top">Mylyn User Guide</td>
				<td style="width: 60%" align="center"></td>
				<td style="width: 20%" align="right" valign="top">Task Editor</td>
			</tr>
		</table>
	</body>
</html>