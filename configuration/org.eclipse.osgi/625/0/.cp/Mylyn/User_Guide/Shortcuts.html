<?xml version='1.0' encoding='utf-8' ?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
		<title>Mylyn User Guide - Shortcuts</title>
		<link type="text/css" rel="stylesheet" href="../../book.css"/>
	</head>
	<body>
		<table class="navigation" style="width: 100%;" border="0" summary="navigation">
			<tr>
				<th style="width: 100%" align="center" colspan="3">Shortcuts</th>
			</tr>
			<tr>
				<td style="width: 20%" align="left">
					<a href="Team-Support.html" title="Team Support">
						<img alt="Previous" border="0" src="../../images/prev.gif"/>
					</a>
				</td>
				<td style="width: 60%" align="center"></td>
				<td style="width: 20%" align="right">
					<a href="Preferences.html" title="Preferences">
						<img alt="Next" border="0" src="../../images/next.gif"/>
					</a>
				</td>
			</tr>
			<tr>
				<td style="width: 20%" align="left" valign="top">Team Support</td>
				<td style="width: 60%" align="center"></td>
				<td style="width: 20%" align="right" valign="top">Preferences</td>
			</tr>
		</table><hr class="navigation-separator"/>
		<h1 id="Shortcuts">Shortcuts</h1>
		<p><b>&nbsp;Task List view</b>
<ul>
<li>F2: rename</li>
<li>F4: show tooltip</li>
<li>F5: synchronize selected</li>
<li>Alt + Down: go to next unread</li>
<li>Alt + Up: go to previous unread</li>
<li>Alt + Shift + C: mark complete (local tasks only)</li>
<li>Alt + Shift + I: mark incomplete (local tasks only)</li>
<li>Alt + Shift + R: mark as read</li>
<li>Alt + Shift + U: mark as unread</li>
<li>Esc: hide tooltip</li>
<li>Enter: open task</li>
<li>Insert: new local task</li>
<li>Shift + Insert: new sub task</li>
<li>Alt + N (Mac only): new local task</li>
<li>Alt + Shift + N (Mac only): new sub task</li>
<li>Ctrl+C: Copy details</li>
<li>Ctrl+F: Find</li>
<li>When dragging URLs to the Task List: in Mozilla/Firefox just drag, in Internet Explorer <code>Ctrl+drag</code></li>
</ul></p>
		<p><b>&nbsp;Task Editor</b></p>
		<p><ul>
<li>Ctrl+F: Find text in comments, description, summary and private notes</li>
<li>Alt + Shift + C: mark complete (local tasks only)</li>
<li>Alt + Shift + I: mark incomplete (local tasks only)</li>
<li>Alt + Shift + R: mark as read</li>
<li>Alt + Shift + U: mark as unread</li>
</ul></p>
		<p><b>&nbsp;General</b>			
<ul>
<li><code>Alt+click</code> or <code>Alt+RightArrow</code>: show all children of an element in a focused view, then click to select. Hold down alt to keep drilling in, click on whitespace in view to show all root elements.</li> 
<li><code>Ctrl+Shift+Alt+RightArrow</code> Quick Context View</li> 
<li><code>Ctrl+F9</code>: activate task dialog</li>
<li><code>Ctrl+Shift+F9</code>: deactivate task</li>
<li><code>Ctrl+F12</code>: open task dialog</li>
<li><code>Ctrl+Shift+F12</code>: open repository task dialog</li>
</ul></p>
		<p><b>&nbsp;Interest manipulation</b>			
<ul>
<li><code>Ctrl+Shift+Alt+Up</code>: mark as landmark</li>
<li><code>Ctrl+Shift+Alt+Down</code>: mark less interesting</li>			
</ul></p>
		<p><b>&nbsp;Useful Eclipse shortcuts</b>			
<ul>
<li><code>Alt+Shift+Q, K</code>: show 
			<i>Task List</i> view</li>
<li><code>Ctrl+F10</code>: invoke view menu or ruler menu in editor</li>			
</ul>
		</p><hr class="navigation-separator"/>
		<table class="navigation" style="width: 100%;" border="0" summary="navigation">
			<tr>
				<td style="width: 20%" align="left">
					<a href="Team-Support.html" title="Team Support">
						<img alt="Previous" border="0" src="../../images/prev.gif"/>
					</a>
				</td>
				<td style="width: 60%" align="center">
					<a href="User-Guide.html" title="Mylyn User Guide">
						<img alt="Mylyn User Guide" border="0" src="../../images/home.gif"/>
					</a>
				</td>
				<td style="width: 20%" align="right">
					<a href="Preferences.html" title="Preferences">
						<img alt="Next" border="0" src="../../images/next.gif"/>
					</a>
				</td>
			</tr>
			<tr>
				<td style="width: 20%" align="left" valign="top">Team Support</td>
				<td style="width: 60%" align="center"></td>
				<td style="width: 20%" align="right" valign="top">Preferences</td>
			</tr>
		</table>
	</body>
</html>