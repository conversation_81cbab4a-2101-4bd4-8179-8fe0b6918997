/*******************************************************************************
 * Copyright (c) 2006 IBM Corporation and others.
 * All rights reserved. This program and the accompanying materials 
 * are made available under the terms of the Eclipse Public License v1.0
 * which accompanies this distribution, and is available at
 * http://www.eclipse.org/legal/epl-v10.html
 * 
 * Contributors:
 *     IBM Corporation - initial API and implementation
 *******************************************************************************/

/* Hide the extra div for links in the normal state. */
a .link-extra-div {
	display: none;
}

/* Link label properties */
#page-links a .link-label {
	margin-left: 7px;
	position: relative;
	top: -1.2em;
	font-weight : 600;
	margin-left : 13px;
	padding-left: 10px;
	padding-right: 10px;
	margin-bottom: -1.2pm;
	color : white;
}

#page-links a:active .link-label, 
#page-links a:focus .link-label,
#page-links a:hover .link-label {
	background-image: url("../graphics/icons/ctool/root_midhov2.gif");
	background-repeat: repeat-y;
	color : black;
}

#page-links a#overview .link-label {
	margin-left: 15px;
	margin-top: -50px;
	padding-left: 20px;
}

#page-links a:active#overview .link-label,
#page-links a:focus#overview .link-label,
#page-links a:hover#overview .link-label {
	padding-left: 10px;
	margin-top: 0px;
}

#page-links a#workbench .link-label {
	padding-left: 10px;
	margin-left: 0px;
}

/* Necessary for hover text to align in rtl mode */
#page-links a {
    direction: ltr;
}

/* Link description properties */
#page-links a p .text {
	margin-left: 3px;
	position: relative;
	top: -1.6em;
	padding-top: 5px;
	padding-left: 9px;
	padding-right: 10px;
	margin-bottom: -1.6em;
	font-weight : 500;
	padding-bottom: 5px;
	background-image: url("../graphics/icons/ctool/root_midhov2.gif");
	background-repeat: repeat-y;
}

#page-links a#overview p .text {
	margin-left: 5px;
}

#page-links a#workbench p .text {
	margin-left: -10px;
}

/* Some differences between Mozilla and IE here */

#page-links a#workbench > p .text {
	margin-left: -10px;
}


/*
 * Set up the content for the root page.
 */
html, body {

	
	background-image : url("../graphics/rootpage/welcomebckgrd.jpg");
	background-repeat : no-repeat;
	background-position : center center;
	background-attachment : fixed;
	overflow : auto;
	overflow-clip: rect(0, auto, auto, 0);
	background-color : #345365;
}
#root {
	min-height : 450px;
	height : expression(document.body.clientHeight < 450? "450px": "100%" );

    min-width : 940px;
	width:expression(document.body.clientWidth < 940? "940px": "auto" );

	overflow: hidden;
}

#branding {
	position: absolute;
	bottom : 20px;
	left : 20px;
}

/* 
 * Set up the navigation bar.  It should be centered in the middle
 * of the page
 */
#links-background {
	position: absolute;
	top: 50%;
	left: 50%;
	text-align : center;
}

#page-links {
	position : relative;
	left : 0px;
	top : 0x;
}

#page-links a {
	position : absolute;
	text-align : left;
	padding-bottom: 2px;
}

/* Position root links using absolute coordinates */
#page-links a img {
	height : 84px;
	width : 103px;
}

#page-links a:active img,
#page-links a:focus img,
#page-links a:hover img
{
	width : 215px;
}

#page-links a#overview {
	left: -444px;
	top: -188px;
}

#page-links a#overview:hover {
	left: -444px;
}

#page-links a#overview img {
	width : 217px;
	height : 149px;
}

#page-links a#overview:active img,
#page-links a#overview:focus img,
#page-links a#overview:hover img {
	width : 217px;
	height : 99px;
}

#page-links a#workbench img {
	width : 202px;
	background-position : right top;
	margin-left : 0px;
}

/* Absolute positions of root links */

#page-links a#whatsnew {
	left: -292px;
	top: -82px;
}

#page-links a#tutorials {
	left: -11px;
	top: 104px;
}

#page-links a#samples {
	left: -199px;
	top: 104px;
}

#page-links a#firststeps {
	left: -105px;
	top: -82px;
}

#page-links a#webresources {
	left: 82px;
	top: 11px;
}

#page-links a#migrate {
	left: 269px;
	top: 104px;
}

#page-links a#workbench {
	left: 271px;
	top: -82px;
}

/* Paint bottom edge of the text box as a background image */

#page-links a:hover,
#page-links a:focus,
#page-links a:active {
	background-image: url("../graphics/icons/ctool/root_bottomhov.gif");
	background-repeat: no-repeat;
	background-position: bottom left;
	z-index: 20;
}

#page-links a#overview:hover,
#page-links a#overview:focus,
#page-links a#overview:active {
	background-image: url("../graphics/icons/ctool/overview_bottomhov.gif"); 
}

#page-links a#workbench:hover,
#page-links a#workbench:focus,
#page-links a#workbench:active {
	background-image: url("../graphics/icons/ctool/workbench_bottomhov.gif");
	background-position: bottom left;
}

#page-links > a#workbench:hover,
#page-links > a#workbench:focus,
#page-links > a#workbench:active {
	background-position: bottom 2px;
}

/* Hide the text in the normal state */
#page-links a .text {
	display : none;
}

/* Hide the links in the normal state */
#page-links a .link-label {
    display : none;
}

/* Note that the links can be made to show by including the following lines for example in the css for an extension. 
#page-links a span.link-label {
    display : block;
}
*/

/* Show the link label and link text as block on hover */

#page-links a:hover .link-label,
#page-links a:focus .link-label,
#page-links a:active .link-label {
	display : block;
}

#page-links a:hover p .text,
#page-links a:hover p .text,
#page-links a:focus p .text,
#page-links a:active p .text {
	display : block;
	z-index: 35;
}

#page-links .link-label,
#page-links a:hover .link-label,
#page-links a:focus .link-label,
#page-links a:active .link-label {
	width: 202px;
}

#page-links a:hover p .text,
#page-links a:focus p .text,
#page-links a:active p .text {
	width: 202px;
}

#page-links a:hover > .link-label,
#page-links a:focus > .link-label,
#page-links a:active > .link-label {
	width: 192px;
}

#page-links a:hover > p .text,
#page-links a:focus > p .text,
#page-links a:active > p .text {
	width: 192px;
}

#page-links a#workbench:hover .link-label,
#page-links a#workbench:focus .link-label,
#page-links a#workbench:active .link-label {
	width: 202px;
}

#page-links a#workbench:hover p .text,
#page-links a#workbench:focus p .text,
#page-links a#workbench:active p .text {
	width: 202px;
}

#page-links a#workbench:hover > .link-label,
#page-links a#workbench:focus > .link-label,
#page-links a#workbench:active > .link-label {
	width: 182px;
}

#page-links a#workbench:hover p > .text,
#page-links a#workbench:focus p > .text,
#page-links a#workbench:active p > .text {
	width: 193px;
}

#page-links a .background-image {
	display: none;
}

#page-links a .link-extra-div {
	display :none;
}

/* Link images */
#page-links a#overview .content-img { background-image : url("../graphics/icons/ctool/overview.gif"); }
#page-links a:active#overview .content-img,
#page-links a:focus#overview .content-img ,
#page-links a:hover#overview .content-img { background-image : url("../graphics/icons/ctool/overview_tophov.gif"); }

#page-links a#tutorials .content-img { background-image : url("../graphics/icons/ctool/tutorials.gif"); }
#page-links a:active#tutorials .content-img,
#page-links a:focus#tutorials .content-img ,
#page-links a:hover#tutorials .content-img { background-image : url("../graphics/icons/ctool/tutorials_tophov.gif"); }

#page-links a#samples .content-img { background-image : url("../graphics/icons/ctool/samples.gif"); }
#page-links a:active#samples .content-img,
#page-links a:focus#samples .content-img ,
#page-links a:hover#samples .content-img { background-image : url("../graphics/icons/ctool/samples_tophov.gif"); }

#page-links a#whatsnew .content-img { background-image : url("../graphics/icons/ctool/whatsnew.gif"); }
#page-links a:active#whatsnew .content-img, 
#page-links a:focus#whatsnew .content-img,  
#page-links a:hover#whatsnew .content-img { background-image : url("../graphics/icons/ctool/whatsnew_tophov.gif"); }

#page-links a#firststeps .content-img { background-image : url("../graphics/icons/ctool/firststeps.gif"); }
#page-links a:active#firststeps .content-img,
#page-links a:focus#firststeps .content-img ,
#page-links a:hover#firststeps .content-img { background-image : url("../graphics/icons/ctool/firststeps_tophov.gif"); }

#page-links a#migrate .content-img { background-image : url("../graphics/icons/ctool/migrate.gif"); }
#page-links a:active#migrate .content-img,
#page-links a:focus#migrate .content-img ,
#page-links a:hover#migrate .content-img { background-image : url("../graphics/icons/ctool/migrate_tophov.gif"); }

#page-links a#webresources .content-img { background-image : url("../graphics/icons/ctool/webresources.gif"); }
#page-links a:active#webresources .content-img,
#page-links a:focus#webresources .content-img ,
#page-links a:hover#webresources .content-img { background-image : url("../graphics/icons/ctool/webresources_tophov.gif"); }

#page-links a#workbench .content-img { background-image : url("../graphics/icons/ctool/workbench.gif"); }
#page-links a:hover#workbench .content-img { background-image : url("../graphics/icons/ctool/workbench_tophov.gif"); }
#page-links a:active#workbench .content-img { background-image : url("../graphics/icons/ctool/workbench_tophov.gif"); }
#page-links a:focus#workbench .content-img { background-image : url("../graphics/icons/ctool/workbench_tophov.gif"); }

/*
 * Not using action links.
 */
#action-links {
	display: none;
}