###############################################################################
#  Copyright (c) 2005, 2009 IBM Corporation and others.
#  All rights reserved. This program and the accompanying materials
#  are made available under the terms of the Eclipse Public License v1.0
#  which accompanies this distribution, and is available at
#  http://www.eclipse.org/legal/epl-v10.html
# 
#  Contributors:
#     IBM Corporation - initial API and implementation
###############################################################################
theme=true
standby.links-background.page-links.overview.link-icon = ../graphics/icons/etool/overview72.gif
standby.links-background.page-links.tutorials.link-icon = ../graphics/icons/etool/tutorials72.gif
standby.links-background.page-links.samples.link-icon= ../graphics/icons/etool/samples72.gif
standby.links-background.page-links.whatsnew.link-icon = ../graphics/icons/etool/whatsnew72.gif
standby.links-background.page-links.firststeps.link-icon = ../graphics/icons/etool/firsteps72.gif
standby.links-background.page-links.migrate.link-icon = ../graphics/icons/etool/migrate72.gif
standby.links-background.page-links.webresources.link-icon = ../graphics/icons/etool/webrsrc72.gif
standby.action-links.workbench.link-icon = ../graphics/icons/etool/wb48.gif

standby.links-background.page-links.overview.hover-icon = ../graphics/icons/ctool/overview72.gif
standby.links-background.page-links.tutorials.hover-icon = ../graphics/icons/ctool/tutorials72.gif
standby.links-background.page-links.samples.hover-icon = ../graphics/icons/ctool/samples72.gif
standby.links-background.page-links.whatsnew.hover-icon = ../graphics/icons/ctool/whatsnew72.gif
standby.links-background.page-links.migrate.hover-icon = ../graphics/icons/ctool/migrate72.gif
standby.links-background.page-links.firststeps.hover-icon = ../graphics/icons/ctool/firsteps72.gif
standby.links-background.page-links.webresources.hover-icon = ../graphics/icons/ctool/webrsrc72.gif
standby.action-links.workbench.hover-icon = ../graphics/icons/ctool/wb48.gif

standby.links-background.page-links.layout.vspacing = 30
standby.layout.vspacing = 35
standby.show-link-description = false
standby.show-home-page-navigation = false