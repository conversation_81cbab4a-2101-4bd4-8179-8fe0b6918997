/*******************************************************************************
 * Copyright (c) 2005, 2006 IBM Corporation and others.
 * All rights reserved. This program and the accompanying materials 
 * are made available under the terms of the Eclipse Public License v1.0
 * which accompanies this distribution, and is available at
 * http://www.eclipse.org/legal/epl-v10.html
 * 
 * Contributors:
 *     IBM Corporation - initial API and implementation
 *******************************************************************************/

/* 
 * Set up general fonts, sizes and colors 
 */
body { font-family : Arial, sans-serif; }

H1, H2, H3, H4, p, a { color : #4D4D4D; }

.intro-header H1 {
	font-weight : normal;
	color : #E5E5E5;
}

h2 {
	font-weight : normal;
	color : #7B8694;
}
/* For regular div labels */
H4 .div-label {
	font-weight : bold;
}

/* The label part of the folding section */
.section-title-link .section-title {
	display : inline;
}

/* For separators */
HR {
	width: 90%;
	align: left;
	height : 1px;
	color :  #dfdfe4;
}

/* For the main page content's title */
#content-header H4 .div-label {
	font-weight : normal;
	color : #8C96A2;
	float : none;
	clear : both;
}

.page-description { 
	float : none;
	clear : both;
}

a {
	font-weight : bold;
	text-decoration : none;
	color : #4D4D4D;
}

a .link-label {
	font-weight : normal;
}

/* Hide the 'special-effect' extra div in links by default. */
.link-extra-div {
	display : none;
}

#navigation-links a .link-label {
	font-weight : normal;
	color : #E5E5E5;
}

a .text {
	font-weight : normal;
}

p .group-description {
	font-weight : normal;
}


/* 
 * Set up other general properties like padding/margins
 */
html, body { width : 100%; height : 100%; }

html, body, div, h1, h4, p, a { margin : 0px; padding : 0px; }

.intro-header H1 { padding-top : 10px; margin-left : 10px; }

.section { }
.section-body { display: none; padding : 0px; }

/* For regular div labels */
#page-content div H4 {
	padding : 10px;
	padding-bottom : 0px;
}

/* For the main page content's div label */
#page-content #content-header H4 {
	padding-bottom : 10px;
	padding-top : 0px;
}

/* special case for Mozilla's main content-header label.
   Mozilla 1.4 needs more room at the top */
#page-content > #content-header H4 { padding-top : 10px; }

/* Needed in IE to get shift+tab to show the active image properly */
a:active {
	border : solid 0px;
}

a img {
	border-width : 0;
	background-repeat : no-repeat;
}

/*
 * to get scrollbars working in both IE and Mozilla
 */
html,body { overflow: auto; }
html>body { overflow: visible; }

/*
 * Set up the body, decorative background, and navigation for the content 
 * pages. 
 * Note: the root page handles its own background and navigation; these
 * settings primarily apply to the content pages
 */
body {
	background-color : #FFFFFF;
	background-repeat : no-repeat;
	background-position : bottom right;
}

/*
 * We will use one of the general purpose groups to show
 * the curve image
 */
#extra-group1 { 
	width : 100%;
	height : 164px;
	position : absolute;
	top : 0px;
	background-image : url(../graphics/contentpage/backgroundcurve.gif);
	background-repeat : no-repeat;
	background-position : top center;
	margin : 0;
	padding : 0;
}

/*
 * Hide the other general-purpose groups
 */

#extra-group2,
#extra-group3,
#extra-group4,
#extra-group5 {
	display : none;
}

.intro-header {	background-color : transparent; z-index : 100;}

body, .page{
	min-width : 770px;
	/* since IE doesn't support min-width, try expression */
	width:expression(document.body.clientWidth < 770? "770px": "auto" );
	min-height : 425px;
	height : 100%;
	height : expression(document.body.clientHeight < 425? "425px": "100%" );
}

.page { 
	min-height : 475px;
	background-image : url(../graphics/contentpage/background.jpg);
	background-repeat : repeat-x;
	background-position : top left;
}

#page-content {
	background-repeat : no-repeat;
	background-position : bottom right;
	height : 70%;
}

/* 
 * Lay out the navigation links 
 * (Root page does something similar for its navigation)
 */
#navigation-links {
	position : relative;
	left : 10px;
	top : 5px;
	height : 60px;
	width : 98%;
}

#navigation-links a {
	padding-left : 5px;
	padding-right : 5px;
	float : left;
	text-align : center;
}

#navigation-links #customize {
	padding-left : 5px;
	padding-right : 5px;
	float : left;
	text-align : center;
}

#navigation-links a img {
	height : 52px;
	width : 52px;
	vertical-align : middle;
}

#navigation-links a .link-label { display : block; margin-top : 5px;}

#navigation-links a .text { display : none; }

#navigation-links a:hover, 
#navigation-links a:focus 
#navigation-links a:active { border-right : 0px;}

/* properties for each of the navigation-links  */
#navigation-links a#overview img { background-image : url(../graphics/icons/etool/overview48.gif); }
#navigation-links a#overview:hover img,
#navigation-links a#overview:focus img,
#navigation-links a#overview:active img { background-image : url(../graphics/icons/ctool/overview48.gif); }

#navigation-links a#tutorials img { background-image : url(../graphics/icons/etool/tutorials48.gif); }
#navigation-links a#tutorials:hover img,
#navigation-links a#tutorials:active img,
#navigation-links a#tutorials:focus img { background-image : url(../graphics/icons/ctool/tutorials48.gif); }

#navigation-links a#samples img { background-image : url(../graphics/icons/etool/samples48.gif); }
#navigation-links a#samples:hover img,
#navigation-links a#samples:active img,
#navigation-links a#samples:focus img { background-image : url(../graphics/icons/ctool/samples48.gif); }

#navigation-links a#whatsnew img { background-image : url(../graphics/icons/etool/whatsnew48.gif); }
#navigation-links a#whatsnew:hover img,
#navigation-links a#whatsnew:focus img,
#navigation-links a#whatsnew:active img { background-image : url(../graphics/icons/ctool/whatsnew48.gif); }

#navigation-links a#firststeps img { background-image : url(../graphics/icons/etool/firsteps48.gif); }
#navigation-links a#firststeps:hover img,
#navigation-links a#firststeps:focus img,
#navigation-links a#firststeps:active img { background-image : url(../graphics/icons/ctool/firsteps48.gif); }

#navigation-links a#webresources img { background-image : url(../graphics/icons/etool/webrsrc48.gif); }
#navigation-links a#webresources:hover img,
#navigation-links a#webresources:focus img,
#navigation-links a#webresources:active img { background-image : url(../graphics/icons/ctool/webrsrc48.gif); }

#navigation-links a#migrate img { background-image : url(../graphics/icons/etool/migrate48.gif); }
#navigation-links a#migrate:hover img,
#navigation-links a#migrate:focus img,
#navigation-links a#migrate:active img { background-image : url(../graphics/icons/ctool/migrate48.gif); }


#navigation-links a#workbench { position : absolute;  right : 0px; top : -35px; text-align : right;}
#navigation-links a#workbench .text { display : none; }
#navigation-links a#workbench img { background-image : url(../graphics/icons/etool/wb48.gif); width : 53px; height : 53px;}
#navigation-links a#workbench:hover img,
#navigation-links a#workbench:focus img,
#navigation-links a#workbench:active img { background-image : url(../graphics/icons/ctool/wb48.gif); }

/* 
 * Lay out the page title and description 
 */
h1, p { margin-left : 10px; } /* required in mozilla so the page description is properly indented */

/* position the page content so that the page title overlays the bottom
 * of the background image, but make sure the content is always on top 
 * (using z-index) */
#page-content {
	float : none;
	clear : both;
	text-align : center;
	margin-top : 35px;
}

.page > #page-content { margin-top : 50px; }

#page-content p { 
	padding-bottom : 15px; 
	text-align : left; 
	float : none;
	clear : both;
}

/* Page content bins */

#page-content #top-left {
  border: none; float: left; margin: 0px; padding: 0px; width: 49%;
  clear: left;
}
#page-content #top-right {
  border: none; float: right; margin: 0px; padding: 0px; width: 49%;
  clear: right;
}

/* top-bottom divider - runs the entire width to ensure
 * bottom boxes start at the same y
 */
#page-content #content-divider {
  border: none; float: none; margin: 0; padding: 0px; width: 100%;
  clear: both;
}

#page-content #bottom-left {
  border: none; float: left; margin: 0px; padding: 0px; width: 49%;
  clear: left;
}
#page-content #bottom-right {
  border: none; float: right; margin: 0px; padding: 0px; width: 49%;
  clear: right;
}

#page-content #content-header H4, .page-description {
	text-align : left;
	margin-right : 10px;
	float : none;
	clear : both;
}

#page-content #top-left > *, 
#page-content #top-right > *,
#page-content #bottom-left > *,
#page-content #bottom-right > * {
	display: block;
}

#page-content * > a {
	vertical-align : middle; 
}

#page-content * a img {
	height : 57px;
	width : 57px;
	vertical-align : middle;
}	

#page-content * a .link-label {
	display : block;
	position : relative;
	top : -50px;
	left : 60px;
	margin-right: 60px;
}

#page-content * a > .link-label { left: 65px; }

#page-content * a p .text {
	display : block;
	position : relative;
	top : -45px;
	margin-bottom: -25px;
	left : 53px;
	margin-right: 53px;
}

#page-content * a p > .text { left: 58px; }

#page-content * a:hover { border-right : 5px; }


/* The following rules are for extensions in all pages. Extensions should be placed in
 * groups with the style 'content-group' and contain links with the style 'content-link'.
 * Group is important so that importance mixin style can be applied to the group that
 * uses block display. We need to see a solid rectangle around the extension, not 
 * a tight polygon around the link perimeter.
 */
 
.content-group {
	margin-left: 10px;
	margin-right: 10px;
	padding-left: 10px;
	padding-right: 10px;
	float : none;
	clear : both;
	text-align : left;
}

.categoryContentnav {
	font-weight: bold; 
	color: #4A4D4A; 
}

.topicList {
	line-height:1.75;
	color: #00507C;
}

.content-link:hover { border-right : 0px; }

iframe {
	position:relative;
	top:16px;
	width:100%;
	height:100%;
	padding-left:10px;
	}

/* mozilla scrollbar appearing off page fix */
#page-content > iframe {
	width: 98%;
	padding-left: 2%;
}	
