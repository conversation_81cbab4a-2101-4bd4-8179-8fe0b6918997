<?xml version="1.0" encoding="utf-8" ?>
<!--
    Copyright (c) 2005, 2016 IBM Corporation and others.
    All rights reserved. This program and the accompanying materials
    are made available under the terms of the Eclipse Public License v1.0
    which accompanies this distribution, and is available at
    http://www.eclipse.org/legal/epl-v10.html
   
    Contributors:
         IBM Corporation - initial API and implementation
         Manumitting Technologies Inc - Added 'qroot' page (bug 466370)
 -->

<!-- 
	A content file for the Universal Welcome
-->
<introContent>
	<!-- Root page -->
	<page id="root" alt-style="$theme$/swt/root.properties" style="$theme$/html/root.css" style-id="page rootlike">
		<anchor id="head-anchor"/>
        <title style-id="intro-header">$introTitle$</title>
        <group id="links-background">
           <group id="page-links" computed="true"/>
        </group>
        <group id="action-links" computed="true">
        </group>        
        <group id="branding">
        	<img src="$introBrandingImage$" alt="$introBrandingImageText$"/>
        </group>
		<!-- General purpose groups for adding additional content -->
		<group id="extra-group1" filteredFrom="swt"><anchor id="anchor"/></group>
		<group id="extra-group2" filteredFrom="swt"><anchor id="anchor"/></group>
		<group id="extra-group3" filteredFrom="swt"><anchor id="anchor"/></group>
		<group id="extra-group4" filteredFrom="swt"><anchor id="anchor"/></group>
		<group id="extra-group5" filteredFrom="swt"><anchor id="anchor"/></group>
    </page>
	
	<!-- A root page that looks more like other quadranted pages, with Quicklinks -->
	<page id="qroot" alt-style="$theme$/swt/root.properties" style="$theme$/html/qroot.css" style-id="page rootlike">
		<anchor id="head-anchor" />
		<title style-id="intro-header">$introTitle$</title>
        <group id="branding">
        	<img src="$introBrandingImage$" alt="$introBrandingImageText$"/>
        </group>
		<group id="extra-group1" filteredFrom="swt" />
		<!-- navigation -->
        <group id="action-links" computed="true" />
		<!-- content -->
		<group id="page-content">
            <text style-id="page-title" id="page-title" filteredFrom="html">Get Started...</text>
            <text style-id="page-description" id="page-description">$introDescription-root$</text>
			<!-- panes -->
			<group id="top-left" computed="true">
				<contentProvider pluginId="org.eclipse.ui.intro.quicklinks"
					class="org.eclipse.ui.intro.quicklinks.QuicklinksViewer" id="quick-links">
				</contentProvider>
			</group>
			<group id="top-right" computed="true">
				<group id="page-links" computed="true"  filteredFrom="swt">
				</group>
			</group>
			<group id="content-divider" filteredFrom="swt" />
			<group id="bottom-left" computed="true" />
			<group id="bottom-right" computed="true" />
		</group>
		<!-- extra groups for additional effects -->
		<group id="extra-group2" filteredFrom="swt"><anchor id="anchor" /></group>
		<group id="extra-group3" filteredFrom="swt"><anchor id="anchor" /></group>
		<group id="extra-group4" filteredFrom="swt"><anchor id="anchor" /></group>
		<group id="extra-group5" filteredFrom="swt">
			<anchor id="anchor" />
			<contentProvider id="welcome-restart-check" pluginId="org.eclipse.ui.intro"
				class="org.eclipse.ui.intro.contentproviders.AlwaysWelcomeCheckbox" />
		</group>
	</page>
	
	<!-- Standby page -->
    <page id="standby" alt-style="$theme$/swt/standby.properties" style="$theme$/html/standby.css" style-id="page">
   		<anchor id="head-anchor"/>
        <title style-id="intro-header">$introTitle$</title>
        <group id="links-background">
            <group id="page-links" computed="true">
            </group>
        </group>
        <group id="action-links" computed="true">
        </group>        
        <group id="branding">
        	<img src="$introBrandingImage$" alt="$introBrandingImageText$"/>
        </group>
		<!-- General-purpose groups for additional content -->
		<group id="extra-group1" filteredFrom="swt"><anchor id="anchor"/></group>
		<group id="extra-group2" filteredFrom="swt"><anchor id="anchor"/></group>
		<group id="extra-group3" filteredFrom="swt"><anchor id="anchor"/></group>		
    </page>    
    
	<!-- Overview page -->
    <page id="overview" style="$theme$/html/overview.css" alt-style="$theme$/swt/overview.properties" style-id="page">
   		<anchor id="head-anchor"/>
        <title style-id="intro-header">$introTitle$</title> 
        <group id="extra-group1" filteredFrom="swt"/>
		<!-- navigation -->
        <group id="navigation-links" filteredFrom="swt">
            <group id="page-links" computed="true">
            </group>
            <group id="action-links">
                <link url="http://org.eclipse.ui.intro/switchToLaunchBar" label="Workbench" id="workbench" style-id="$high-contrast$">
                    <text>Go to the workbench</text>
                </link>
            </group>
        </group>
		<!-- content -->
        <group id="page-content">
            <group id="content-header" label="Overview" filteredFrom="swt">
            </group>
            <text style-id="page-title" id="page-title" filteredFrom="html">Overview</text>
            <text style-id="page-description" id="page-description">$introDescription-overview$</text>
			<!-- panes -->
 			<group id="top-left" computed="true"/>
			<group id="top-right" computed="true"/>
			<group id="content-divider" filteredFrom="swt"/>		
			<group id="bottom-left" computed="true"/>
			<group id="bottom-right" computed="true"/>
        </group>
		<!-- extra groups for additional effects -->
		<group id="extra-group2" filteredFrom="swt"><anchor id="anchor"/></group>
		<group id="extra-group3" filteredFrom="swt"><anchor id="anchor"/></group>
		<group id="extra-group4" filteredFrom="swt"><anchor id="anchor"/></group>
    </page>
    
    <!-- Tutorials page -->
	<page id="tutorials" style="$theme$/html/tutorials.css" alt-style="$theme$/swt/tutorials.properties" style-id="page">
		<anchor id="head-anchor"/>
        <title style-id="intro-header">$introTitle$</title>	
        <group id="extra-group1" filteredFrom="swt"/>
        <group id="navigation-links" filteredFrom="swt">
            <group id="page-links" computed="true">
            </group>
            <group id="action-links">
                <link url="http://org.eclipse.ui.intro/switchToLaunchBar" label="Workbench" id="workbench" style-id="$high-contrast$">
                    <text>Go to the workbench</text>
                </link>
            </group>
        </group>
        <group id="page-content">
            <group id="content-header" label="Tutorials" filteredFrom="swt">
            </group>
            <text style-id="page-title" id="page-title" filteredFrom="html">Tutorials</text>
            <text style-id="page-description" id="page-description">$introDescription-tutorials$</text>
 			<group id="top-left" computed="true"/>
			<group id="top-right" computed="true"/>
			<group id="content-divider" filteredFrom="swt"/>		
			<group id="bottom-left" computed="true"/>
			<group id="bottom-right" computed="true"/>
        </group>
		<group id="extra-group2" filteredFrom="swt"><anchor id="anchor"/></group>
		<group id="extra-group3" filteredFrom="swt"><anchor id="anchor"/></group>
		<group id="extra-group4" filteredFrom="swt"><anchor id="anchor"/></group>
    </page>
    
    <!-- Samples page -->
	 <page id="samples" style="$theme$/html/samples.css" alt-style="$theme$/swt/samples.properties" style-id="page">
 		<anchor id="head-anchor"/>
        <title style-id="intro-header">$introTitle$</title> 
        <group id="extra-group1" filteredFrom="swt"/>
        <group id="navigation-links" filteredFrom="swt">
            <group id="page-links" computed="true">
            </group>
            <group id="action-links">
                <link url="http://org.eclipse.ui.intro/switchToLaunchBar" label="Workbench" id="workbench" style-id="$high-contrast$">
                    <text>Go to the workbench</text>
                </link>
            </group>
        </group>
        <group id="page-content">
            <group id="content-header" label="Samples" filteredFrom="swt">
            </group>
            <text style-id="page-title" id="page-title" filteredFrom="html">Samples</text>
            <text style-id="page-description" id="page-description">$introDescription-samples$</text>
 			<group id="top-left" computed="true"/>
			<group id="top-right" computed="true"/>
			<group id="content-divider" filteredFrom="swt"/>		
			<group id="bottom-left" computed="true"/>
			<group id="bottom-right" computed="true"/>
        </group>
		<group id="extra-group2" filteredFrom="swt"><anchor id="anchor"/></group>
		<group id="extra-group3" filteredFrom="swt"><anchor id="anchor"/></group>
		<group id="extra-group4" filteredFrom="swt"><anchor id="anchor"/></group>		 
    </page>

	<!-- What's New page -->
    <page id="whatsnew" style="$theme$/html/whatsnew.css" alt-style="$theme$/swt/whatsnew.properties" style-id="page">
		<anchor id="head-anchor"/>    
        <title style-id="intro-header">$introTitle$</title> 
        <group id="extra-group1" filteredFrom="swt"/>
        <group id="navigation-links" filteredFrom="swt">
            <group id="page-links" computed="true">
            </group>
            <group id="action-links">
                <link url="http://org.eclipse.ui.intro/switchToLaunchBar" label="Workbench" id="workbench" style-id="$high-contrast$">
                    <text>Go to the workbench</text>
                </link>
            </group>
        </group>
        <group id="page-content">
            <group id="content-header" label="What's New" filteredFrom="swt">
            </group>
            <text style-id="page-title" id="page-title" filteredFrom="html">What's New</text>
            <text style-id="page-description" id="page-description">$introDescription-whatsnew$</text>
 			<group id="top-left" computed="true"/>
			<group id="top-right" computed="true"/>
			<group id="content-divider" filteredFrom="swt"/>			
			<group id="bottom-left" computed="true"/>
			<group id="bottom-right" computed="true"/>
        </group>
		<group id="extra-group2" filteredFrom="swt"><anchor id="anchor"/></group>
		<group id="extra-group3" filteredFrom="swt"><anchor id="anchor"/></group>
		<group id="extra-group4" filteredFrom="swt"><anchor id="anchor"/></group>		
    </page>
    
    <!-- First Steps page -->
    <page id="firststeps" style="$theme$/html/firststeps.css" alt-style="$theme$/swt/firststeps.properties" style-id="page">
		<anchor id="head-anchor"/>
        <title style-id="intro-header">$introTitle$</title> 
        <group id="extra-group1" filteredFrom="swt"/>
        <group id="navigation-links" filteredFrom="swt">
            <group id="page-links" computed="true">
            </group>
            <group id="action-links">
                <link url="http://org.eclipse.ui.intro/switchToLaunchBar" label="Workbench" id="workbench" style-id="$high-contrast$">
                    <text>Go to the workbench</text>
                </link>
            </group>
        </group>
        <group id="page-content">
            <group id="content-header" label="First Steps" filteredFrom="swt">
            </group>
            <text style-id="page-title" id="page-title" filteredFrom="html">First Steps</text>
            <text style-id="page-description" id="page-description">$introDescription-firststeps$</text>
 			<group id="top-left" computed="true"/>
			<group id="top-right" computed="true"/>
			<group id="content-divider" filteredFrom="swt"/>				
			<group id="bottom-left" computed="true"/>
			<group id="bottom-right" computed="true"/>
        </group>
		<group id="extra-group2" filteredFrom="swt"><anchor id="anchor"/></group>
		<group id="extra-group3" filteredFrom="swt"><anchor id="anchor"/></group>
		<group id="extra-group4" filteredFrom="swt"><anchor id="anchor"/></group>		
    </page>
    
    <!-- Web resources page -->
    <page id="webresources" style="$theme$/html/webresources.css" alt-style="$theme$/swt/webresources.properties" style-id="page">
		<anchor id="head-anchor"/>    
        <title style-id="intro-header">$introTitle$</title> 
        <group id="extra-group1" filteredFrom="swt"/>
        <group id="navigation-links" filteredFrom="swt">
            <group id="page-links" computed="true">
            </group>
            <group id="action-links">
                <link url="http://org.eclipse.ui.intro/switchToLaunchBar" label="Workbench" id="workbench" style-id="$high-contrast$">
                    <text>Go to the workbench</text>
                </link>
            </group>
        </group>
        <group id="page-content">
            <group id="content-header" label="Web Resources" filteredFrom="swt">
            </group>
            <text style-id="page-title" id="page-title" filteredFrom="html">Web Resources</text>
            <text style-id="page-description" id="page-description">$introDescription-webresources$</text>			
 			<group id="top-left" computed="true"/>
			<group id="top-right" computed="true"/>
			<group id="content-divider" filteredFrom="swt"/>			
			<group id="bottom-left" computed="true"/>
			<group id="bottom-right" computed="true"/>
        </group>
		<group id="extra-group2" filteredFrom="swt"><anchor id="anchor"/></group>
		<group id="extra-group3" filteredFrom="swt"><anchor id="anchor"/></group>
		<group id="extra-group4" filteredFrom="swt"><anchor id="anchor"/></group>		
    </page>
    
    
    <!-- Migrate page -->
    <page id="migrate" style="$theme$/html/migrate.css" alt-style="$theme$/swt/migrate.properties" style-id="page">
		<anchor id="head-anchor"/>    
        <title style-id="intro-header">$introTitle$</title> 
        <group id="extra-group1" filteredFrom="swt"/>
        <group id="navigation-links" filteredFrom="swt">
            <group id="page-links" computed="true">
            </group>
            <group id="action-links">
                <link url="http://org.eclipse.ui.intro/switchToLaunchBar" label="Workbench" id="workbench" style-id="$high-contrast$">
                    <text>Go to the workbench</text>
                </link>
            </group>
        </group>
        <group id="page-content">
            <group id="content-header" label="Migrate" filteredFrom="swt">
            </group>
            <text style-id="page-title" id="page-title" filteredFrom="html">Migrate</text>
            <text style-id="page-description" id="page-description">$introDescription-migrate$</text>			
 			<group id="top-left" computed="true"/>
			<group id="top-right" computed="true"/>
			<group id="content-divider" filteredFrom="swt"/>			
			<group id="bottom-left" computed="true"/>
			<group id="bottom-right" computed="true"/>
        </group>
		<group id="extra-group2" filteredFrom="swt"><anchor id="anchor"/></group>
		<group id="extra-group3" filteredFrom="swt"><anchor id="anchor"/></group>
		<group id="extra-group4" filteredFrom="swt"><anchor id="anchor"/></group>		
    </page>
</introContent>