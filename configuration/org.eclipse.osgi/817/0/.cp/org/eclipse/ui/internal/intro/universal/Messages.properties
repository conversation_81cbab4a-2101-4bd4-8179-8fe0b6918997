###############################################################################
# Copyright (c) 2000, 2009 IBM Corporation and others.
# All rights reserved. This program and the accompanying materials
# are made available under the terms of the Eclipse Public License v1.0
# which accompanies this distribution, and is available at
# http://www.eclipse.org/legal/epl-v10.html
#
# Contributors:
#     IBM Corporation - initial API and implementation
###############################################################################

# ==============================================
# Universal Welcome plugin properties file
# ==============================================

# Configurer
SharedIntroConfigurer_overview_name=Overview
SharedIntroConfigurer_overview_alt=Overview
SharedIntroConfigurer_overview_tooltip=Get an overview of the features
SharedIntroConfigurer_firststeps_name=First Steps
SharedIntroConfigurer_firststeps_alt=First Steps
SharedIntroConfigurer_firststeps_tooltip=Take your first steps
SharedIntroConfigurer_tutorials_name=Tutorials
SharedIntroConfigurer_tutorials_alt=Tutorials
SharedIntroConfigurer_tutorials_tooltip=Go through tutorials
SharedIntroConfigurer_samples_name=Samples
SharedIntroConfigurer_samples_alt=Samples
SharedIntroConfigurer_samples_tooltip=Try out the samples
SharedIntroConfigurer_whatsnew_name=What's New
SharedIntroConfigurer_whatsnew_alt=What's New
SharedIntroConfigurer_whatsnew_tooltip=Find out what is new
SharedIntroConfigurer_migrate_name=Migrate
SharedIntroConfigurer_migrate_alt=Migrate
SharedIntroConfigurer_migrate_tooltip=Migrate to the new release
SharedIntroConfigurer_customize_label=Customize
SharedIntroConfigurer_webresources_name=Web Resources
SharedIntroConfigurer_webresources_alt=Web Resources
SharedIntroConfigurer_webresources_tooltip=Read more on the Web
SharedIntroConfigurer_workbench_name = Workbench
SharedIntroConfigurer_workbench_alt = Go to the workbench
SharedIntroConfigurer_workbench_tooltip = Go to the workbench
SharedIntroConfigurer_overview_nav=Overview
SharedIntroConfigurer_firststeps_nav=First Steps
SharedIntroConfigurer_customize_text=Customize page
SharedIntroConfigurer_tutorials_nav=Tutorials
SharedIntroConfigurer_samples_nav=Samples
SharedIntroConfigurer_whatsnew_nav=What's New
SharedIntroConfigurer_migrate_nav=Migrate
SharedIntroConfigurer_webresources_nav=Web Resources

# Preference page
WelcomeCustomizationPreferencePage_left= &Left Column:
WelcomeCustomizationPreferencePage_up= Move Up
WelcomeCustomizationPreferencePage_down=Move Down
WelcomeCustomizationPreferencePage_home=&Home
WelcomeCustomizationPreferencePage_right= Right &Column:
WelcomeCustomizationPreferencePage_browse=Browse...
WelcomeCustomizationPreferencePage_browseTitle=Select the background image file
WelcomeCustomizationPreferencePage_preview= Preview:
WelcomeCustomizationPreferencePage_samples= &Samples
WelcomeCustomizationPreferencePage_migrate= &Migrate
WelcomeCustomizationPreferencePage_moveTo=Move To
WelcomeCustomizationPreferencePage_available= Ava&ilable Extensions:
WelcomeCustomizationPreferencePage_background= Hom&e Page Theme:
WelcomeCustomizationPreferencePage_firststeps= &First Steps
WelcomeCustomizationPreferencePage_applyToAll=Appl&y settings to all the products sharing this workbench
WelcomeCustomizationPreferencePage_useRelative=Use relative &fonts
WelcomeCustomizationPreferencePage_menu_top_right= Top Right
WelcomeCustomizationPreferencePage_webresources= Web &Resources
WelcomeCustomizationPreferencePage_addSeparator=Add Separator
WelcomeCustomizationPreferencePage_removeSeparator=Remove
WelcomeCustomizationPreferencePage_menu_bottom_left=Bottom Left
WelcomeCustomizationPreferencePage_menu_bottom_right=Bottom Right
WelcomeCustomizationPreferencePage_horizontalSeparator=[Horizontal separator]
WelcomeCustomizationPreferencePage_rootpages= Root Pa&ges:
WelcomeCustomizationPreferencePage_overview= &Overview
WelcomeCustomizationPreferencePage_tutorials= &Tutorials
WelcomeCustomizationPreferencePage_whatsnew= &What's New
WelcomeCustomizationPreferencePage_pageDesc=Use drag and drop to position extensions on the page.
WelcomeCustomizationPreferencePage_serialize = Sa&ve As...
WelcomeCustomizationPreferencePage_menu_top_left=Top Left
WelcomeCustomizationPreferencePage_serializeTitle=Save the page layout data as a file
WelcomeCustomizationPreferencePage_menu_available=Available
WelcomeCustomizationPreferencePage_NoMnemonic_overview= Overview
WelcomeCustomizationPreferencePage_NoMnemonic_firststeps= First Steps
WelcomeCustomizationPreferencePage_NoMnemonic_tutorials= Tutorials
WelcomeCustomizationPreferencePage_NoMnemonic_samples= Samples
WelcomeCustomizationPreferencePage_NoMnemonic_whatsnew= What's New
WelcomeCustomizationPreferencePage_NoMnemonic_webresources= Web Resources
WelcomeCustomizationPreferencePage_NoMnemonic_migrate= Migrate
WelcomeCustomizationPreferencePage_Customize= Customize

#Importance levels
ExtensionData_callout=Callout
ExtensionData_low=Low
ExtensionData_medium=Medium
ExtensionData_high=High
ExtensionData_new=New
