<?xml version='1.0' encoding='UTF-8'?>
<?metadataRepository version='1.1.0'?>
<repository name='update site: http://subclipse.tigris.org/update_1.8.x' type='org.eclipse.equinox.internal.p2.metadata.repository.LocalMetadataRepository' version='1'>
  <properties size='2'>
    <property name='p2.timestamp' value='1547633951129'/>
    <property name='site.checksum' value='3908607895'/>
  </properties>
  <references size='4'>
    <repository uri='http://subclipse.tigris.org/update_1.8.x' url='http://subclipse.tigris.org/update_1.8.x' type='0' options='0'/>
    <repository uri='http://subclipse.tigris.org/update_1.8.x' url='http://subclipse.tigris.org/update_1.8.x' type='1' options='0'/>
    <repository uri='http://eclipse.tmatesoft.com/svnkit/1.7.x/' url='http://eclipse.tmatesoft.com/svnkit/1.7.x/' type='0' options='0'/>
    <repository uri='http://eclipse.tmatesoft.com/svnkit/1.7.x/' url='http://eclipse.tmatesoft.com/svnkit/1.7.x/' type='1' options='0'/>
  </references>
  <units size='36'>
    <unit id='net.java.dev.jna.feature.jar' version='3.4.0.t20120117_1605'>
      <properties size='5'>
        <property name='org.eclipse.equinox.p2.name' value='JNA Library'/>
        <property name='org.eclipse.equinox.p2.description' value='JNA Library'/>
        <property name='org.eclipse.equinox.p2.description.url' value='http://jna.dev.java.net/'/>
        <property name='df_LT.license' value='Copyright (c) 2008 Timothy Wall, All Rights Reserved&#xA;&#xA;This library is free software; you can redistribute it and/or&#xA;modify it under the terms of the GNU Lesser General Public&#xA;License as published by the Free Software Foundation; either&#xA;version 2.1 of the License, or (at your option) any later version.&#xA;&#xA;This library is distributed in the hope that it will be useful,&#xA;but WITHOUT ANY WARRANTY; without even the implied warranty of&#xA;MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU&#xA;Lesser General Public License for more details.'/>
        <property name='df_LT.description' value='JNA provides Java programs easy access to native shared libraries (DLLs on Windows) without writing anything but Java code - no JNI or native code is required. This functionality is comparable to Windows&amp;apos; Platform/Invoke and Python&amp;apos;s ctypes. Access is dynamic at runtime without code generation.'/>
      </properties>
      <provides size='3'>
        <provided namespace='org.eclipse.equinox.p2.iu' name='net.java.dev.jna.feature.jar' version='3.4.0.t20120117_1605'/>
        <provided namespace='org.eclipse.equinox.p2.eclipse.type' name='feature' version='1.0.0'/>
        <provided namespace='org.eclipse.update.feature' name='net.java.dev.jna' version='3.4.0.t20120117_1605'/>
      </provides>
      <filter>
        (org.eclipse.update.install.features=true)
      </filter>
      <artifacts size='1'>
        <artifact classifier='org.eclipse.update.feature' id='net.java.dev.jna' version='3.4.0.t20120117_1605'/>
      </artifacts>
      <touchpoint id='org.eclipse.equinox.p2.osgi' version='1.0.0'/>
      <touchpointData size='1'>
        <instructions size='1'>
          <instruction key='zipped'>
            true
          </instruction>
        </instructions>
      </touchpointData>
      <licenses size='1'>
        <license uri='http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html' url='http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html'>
          %license
        </license>
      </licenses>
      <copyright uri='http://jna.dev.java.net/' url='http://jna.dev.java.net/'>
        Copyright (c) 2008 Timothy Wall, All Rights Reserved
      </copyright>
    </unit>
    <unit id='org.tigris.subversion.subclipse.graph.feature.feature.group' version='1.1.1' singleton='false'>
      <update id='org.tigris.subversion.subclipse.graph.feature.feature.group' range='[0.0.0,1.1.1)' severity='0'/>
      <properties size='7'>
        <property name='org.eclipse.equinox.p2.name' value='Subversion Revision Graph'/>
        <property name='org.eclipse.equinox.p2.description' value='Subversion Revision Graph for Subclipse'/>
        <property name='org.eclipse.equinox.p2.description.url' value='%changesURL'/>
        <property name='org.eclipse.equinox.p2.provider' value='tigris.org'/>
        <property name='org.eclipse.equinox.p2.type.group' value='true'/>
        <property name='df_LT.license' value='Subclipse Software User Agreement&#xA;11th April, 2006&#xA;&#xA;Subclipse is licensed under the terms of the Eclipse Public&#xA;License v1.0.  http://www.eclipse.org/legal/epl-v10.html&#xA;&#xA;Applicable Licenses&#xA;&#xA;Subclipse is built upon a number of other open source&#xA;technologies and products.  Here is a list of those products&#xA;with links to their licenses.&#xA;&#xA;svnClientAdapter:  Part of the overall Subclipse project,&#xA;svnClientAdapter presents a pluggable high-level interface&#xA;to the Subversion repository.  svnClientAdapter is licensed&#xA;under the Apache2 License.&#xA;http://www.apache.org/licenses/LICENSE-2.0&#xA;&#xA;Depending on the adapter you choose in your preferences, the&#xA;following products and licenses are involved.&#xA;&#xA;Subversion/JavaHL:  JavaHL is a high-level Java language binding&#xA;to the Subversion &quot;C&quot; libraries and is part of the official&#xA;Subversion source distribution.  Subversion is licensed under&#xA;the CollabNet license.&#xA;http://subversion.tigris.org/project_license.html&#xA;&#xA;SVNKit:  SVNKit is a &quot;pure Java&quot; implementation of the&#xA;Subversion network protocols and working copy formats.&#xA;SVNKit is licensed under the TMate license.&#xA;http://svnkit.com/licensing/index.html&#xA;&#xA;Ganymed SSH-2:  SVNKit uses the Ganymed SSH-2 library to support&#xA;the svn+ssh:// protocol.  Ganymed SSH-2 is licensed under the&#xA;Ganymed license.&#xA;http://www.ganymed.ethz.ch/ssh2/LICENSE.txt&#xA;&#xA;IT IS YOUR OBLIGATION TO READ AND ACCEPT ALL SUCH TERMS&#xA;AND CONDITIONS PRIOR TO USE OF THIS CONTENT.&#xA;'/>
        <property name='df_LT.description' value='Revision graph feature for Subclipse.'/>
      </properties>
      <provides size='2'>
        <provided namespace='org.eclipse.equinox.p2.iu' name='org.tigris.subversion.subclipse.graph.feature.feature.group' version='1.1.1'/>
        <provided namespace='org.eclipse.equinox.p2.localization' name='df_LT' version='1.0.0'/>
      </provides>
      <requires size='15'>
        <required namespace='org.eclipse.equinox.p2.iu' name='org.eclipse.ui' range='0.0.0'/>
        <required namespace='org.eclipse.equinox.p2.iu' name='org.eclipse.core.runtime' range='0.0.0'/>
        <required namespace='org.eclipse.equinox.p2.iu' name='org.eclipse.core.resources' range='0.0.0'/>
        <required namespace='org.eclipse.equinox.p2.iu' name='org.tigris.subversion.subclipse.core' range='1.7.0'/>
        <required namespace='org.eclipse.equinox.p2.iu' name='org.eclipse.team.core' range='0.0.0'/>
        <required namespace='org.eclipse.equinox.p2.iu' name='org.eclipse.team.ui' range='0.0.0'/>
        <required namespace='org.eclipse.equinox.p2.iu' name='org.eclipse.jface.text' range='0.0.0'/>
        <required namespace='org.eclipse.equinox.p2.iu' name='org.eclipse.ui.editors' range='0.0.0'/>
        <required namespace='org.eclipse.equinox.p2.iu' name='org.eclipse.ui.ide' range='0.0.0'/>
        <required namespace='org.eclipse.equinox.p2.iu' name='org.eclipse.ui.views' range='0.0.0'/>
        <required namespace='org.eclipse.equinox.p2.iu' name='org.tigris.subversion.subclipse.ui' range='1.4.6'/>
        <required namespace='org.eclipse.equinox.p2.iu' name='org.eclipse.draw2d' range='3.2.0'/>
        <required namespace='org.eclipse.equinox.p2.iu' name='org.eclipse.gef' range='3.2.0'/>
        <required namespace='org.eclipse.equinox.p2.iu' name='org.tigris.subversion.subclipse.graph' range='[1.1.1,1.1.1]'/>
        <required namespace='org.eclipse.equinox.p2.iu' name='org.tigris.subversion.subclipse.graph.feature.feature.jar' range='[1.1.1,1.1.1]'>
          <filter>
            (org.eclipse.update.install.features=true)
          </filter>
        </required>
      </requires>
      <touchpoint id='null' version='0.0.0'/>
      <licenses size='1'>
        <license uri='%25licenseURL' url='%25licenseURL'>
          %license
        </license>
      </licenses>
      <copyright uri='http://subclipse.tigris.org/' url='http://subclipse.tigris.org/'>
        http://subclipse.tigris.org/
      </copyright>
    </unit>
    <unit id='com.collabnet.subversion.merge.feature.feature.jar' version='3.0.13'>
      <properties size='5'>
        <property name='org.eclipse.equinox.p2.name' value='CollabNet Merge Client'/>
        <property name='org.eclipse.equinox.p2.description' value='The CollabNet Merge client provides powerful Subversion merge capabilities within the Eclipse environment.'/>
        <property name='org.eclipse.equinox.p2.description.url' value='http://desktop-eclipse.open.collab.net'/>
        <property name='org.eclipse.equinox.p2.provider' value='CollabNet'/>
        <property name='org.eclipse.update.feature.plugin' value='com.collabnet.subversion.merge'/>
      </properties>
      <provides size='3'>
        <provided namespace='org.eclipse.equinox.p2.iu' name='com.collabnet.subversion.merge.feature.feature.jar' version='3.0.13'/>
        <provided namespace='org.eclipse.equinox.p2.eclipse.type' name='feature' version='1.0.0'/>
        <provided namespace='org.eclipse.update.feature' name='com.collabnet.subversion.merge.feature' version='3.0.13'/>
      </provides>
      <filter>
        (org.eclipse.update.install.features=true)
      </filter>
      <artifacts size='1'>
        <artifact classifier='org.eclipse.update.feature' id='com.collabnet.subversion.merge.feature' version='3.0.13'/>
      </artifacts>
      <touchpoint id='org.eclipse.equinox.p2.osgi' version='1.0.0'/>
      <touchpointData size='1'>
        <instructions size='1'>
          <instruction key='zipped'>
            true
          </instruction>
        </instructions>
      </touchpointData>
      <licenses size='1'>
        <license uri='http://www.eclipse.org/legal/epl-v10.html' url='http://www.eclipse.org/legal/epl-v10.html'>
          Eclipse Public License - v 1.0&#xA;THE ACCOMPANYING PROGRAM IS PROVIDED UNDER THE TERMS OF THIS&#xA;ECLIPSE PUBLIC LICENSE (&quot;AGREEMENT&quot;). ANY USE, REPRODUCTION OR&#xA;DISTRIBUTION OF THE PROGRAM CONSTITUTES RECIPIENT&apos;S ACCEPTANCE&#xA;OF THIS AGREEMENT.&#xA;1. DEFINITIONS&#xA;&quot;Contribution&quot; means:&#xA;a) in the case of the initial Contributor, the initial code and&#xA;documentation distributed under this Agreement, and&#xA;b) in the case of each subsequent Contributor:&#xA;i) changes to the Program, and&#xA;ii) additions to the Program;&#xA;where such changes and/or additions to the Program originate&#xA;from and are distributed by that particular Contributor. A Contribution&#xA;&apos;originates&apos; from a Contributor if it was added to the Program&#xA;by such Contributor itself or anyone acting on such Contributor&apos;s&#xA;behalf. Contributions do not include additions to the Program&#xA;which: (i) are separate modules of software distributed in conjunction&#xA;with the Program under their own license agreement, and (ii)&#xA;are not derivative works of the Program.&#xA;&quot;Contributor&quot; means any person or entity that distributes the&#xA;Program.&#xA;&quot;Licensed Patents &quot; mean patent claims licensable by a Contributor&#xA;which are necessarily infringed by the use or sale of its Contribution&#xA;alone or when combined with the Program.&#xA;&quot;Program&quot; means the Contributions distributed in accordance with&#xA;this Agreement.&#xA;&quot;Recipient&quot; means anyone who receives the Program under this&#xA;Agreement, including all Contributors.&#xA;2. GRANT OF RIGHTS&#xA;a) Subject to the terms of this Agreement, each Contributor hereby&#xA;grants Recipient a non-exclusive, worldwide, royalty-free copyright&#xA;license to reproduce, prepare derivative works of, publicly display,&#xA;publicly perform, distribute and sublicense the Contribution&#xA;of such Contributor, if any, and such derivative works, in source&#xA;code and object code form.&#xA;b) Subject to the terms of this Agreement, each Contributor hereby&#xA;grants Recipient a non-exclusive, worldwide, royalty-free patent&#xA;license under Licensed Patents to make, use, sell, offer to sell,&#xA;import and otherwise transfer the Contribution of such Contributor,&#xA;if any, in source code and object code form. This patent license&#xA;shall apply to the combination of the Contribution and the Program&#xA;if, at the time the Contribution is added by the Contributor,&#xA;such addition of the Contribution causes such combination to&#xA;be covered by the Licensed Patents. The patent license shall&#xA;not apply to any other combinations which include the Contribution.&#xA;No hardware per se is licensed hereunder.&#xA;c) Recipient understands that although each Contributor grants&#xA;the licenses to its Contributions set forth herein, no assurances&#xA;are provided by any Contributor that the Program does not infringe&#xA;the patent or other intellectual property rights of any other&#xA;entity. Each Contributor disclaims any liability to Recipient&#xA;for claims brought by any other entity based on infringement&#xA;of intellectual property rights or otherwise. As a condition&#xA;to exercising the rights and licenses granted hereunder, each&#xA;Recipient hereby assumes sole responsibility to secure any other&#xA;intellectual property rights needed, if any. For example, if&#xA;a third party patent license is required to allow Recipient to&#xA;distribute the Program, it is Recipient&apos;s responsibility to acquire&#xA;that license before distributing the Program.&#xA;d) Each Contributor represents that to its knowledge it has sufficient&#xA;copyright rights in its Contribution, if any, to grant the copyright&#xA;license set forth in this Agreement.&#xA;3. REQUIREMENTS&#xA;A Contributor may choose to distribute the Program in object&#xA;code form under its own license agreement, provided that:&#xA;a) it complies with the terms and conditions of this Agreement;&#xA;and&#xA;b) its license agreement:&#xA;i) effectively disclaims on behalf of all Contributors all warranties&#xA;and conditions, express and implied, including warranties or&#xA;conditions of title and non-infringement, and implied warranties&#xA;or conditions of merchantability and fitness for a particular&#xA;purpose;&#xA;ii) effectively excludes on behalf of all Contributors all liability&#xA;for damages, including direct, indirect, special, incidental&#xA;and consequential damages, such as lost profits;&#xA;iii) states that any provisions which differ from this Agreement&#xA;are offered by that Contributor alone and not by any other party;&#xA;and&#xA;iv) states that source code for the Program is available from&#xA;such Contributor, and informs licensees how to obtain it in a&#xA;reasonable manner on or through a medium customarily used for&#xA;software exchange.&#xA;When the Program is made available in source code form:&#xA;a) it must be made available under this Agreement; and&#xA;b) a copy of this Agreement must be included with each copy of&#xA;the Program.&#xA;Contributors may not remove or alter any copyright notices contained&#xA;within the Program.&#xA;Each Contributor must identify itself as the originator of its&#xA;Contribution, if any, in a manner that reasonably allows subsequent&#xA;Recipients to identify the originator of the Contribution.&#xA;4. COMMERCIAL DISTRIBUTION&#xA;Commercial distributors of software may accept certain responsibilities&#xA;with respect to end users, business partners and the like. While&#xA;this license is intended to facilitate the commercial use of&#xA;the Program, the Contributor who includes the Program in a commercial&#xA;product offering should do so in a manner which does not create&#xA;potential liability for other Contributors. Therefore, if a Contributor&#xA;includes the Program in a commercial product offering, such Contributor&#xA;(&quot;Commercial Contributor&quot;) hereby agrees to defend and indemnify&#xA;every other Contributor (&quot;Indemnified Contributor&quot;) against any&#xA;losses, damages and costs (collectively &quot;Losses&quot;) arising from&#xA;claims, lawsuits and other legal actions brought by a third party&#xA;against the Indemnified Contributor to the extent caused by the&#xA;acts or omissions of such Commercial Contributor in connection&#xA;with its distribution of the Program in a commercial product&#xA;offering. The obligations in this section do not apply to any&#xA;claims or Losses relating to any actual or alleged intellectual&#xA;property infringement. In order to qualify, an Indemnified Contributor&#xA;must: a) promptly notify the Commercial Contributor in writing&#xA;of such claim, and b) allow the Commercial Contributor to control,&#xA;and cooperate with the Commercial Contributor in, the defense&#xA;and any related settlement negotiations. The Indemnified Contributor&#xA;may participate in any such claim at its own expense.&#xA;For example, a Contributor might include the Program in a commercial&#xA;product offering, Product X. That Contributor is then a Commercial&#xA;Contributor. If that Commercial Contributor then makes performance&#xA;claims, or offers warranties related to Product X, those performance&#xA;claims and warranties are such Commercial Contributor&apos;s responsibility&#xA;alone. Under this section, the Commercial Contributor would have&#xA;to defend claims against the other Contributors related to those&#xA;performance claims and warranties, and if a court requires any&#xA;other Contributor to pay any damages as a result, the Commercial&#xA;Contributor must pay those damages.&#xA;5. NO WARRANTY&#xA;EXCEPT AS EXPRESSLY SET FORTH IN THIS AGREEMENT, THE PROGRAM&#xA;IS PROVIDED ON AN &quot;AS IS&quot; BASIS, WITHOUT WARRANTIES OR CONDITIONS&#xA;OF ANY KIND, EITHER EXPRESS OR IMPLIED INCLUDING, WITHOUT LIMITATION,&#xA;ANY WARRANTIES OR CONDITIONS OF TITLE, NON-INFRINGEMENT, MERCHANTABILITY&#xA;OR FITNESS FOR A PARTICULAR PURPOSE. Each Recipient is solely&#xA;responsible for determining the appropriateness of using and&#xA;distributing the Program and assumes all risks associated with&#xA;its exercise of rights under this Agreement , including but not&#xA;limited to the risks and costs of program errors, compliance&#xA;with applicable laws, damage to or loss of data, programs or&#xA;equipment, and unavailability or interruption of operations.&#xA;6. DISCLAIMER OF LIABILITY&#xA;EXCEPT AS EXPRESSLY SET FORTH IN THIS AGREEMENT, NEITHER RECIPIENT&#xA;NOR ANY CONTRIBUTORS SHALL HAVE ANY LIABILITY FOR ANY DIRECT,&#xA;INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES&#xA;(INCLUDING WITHOUT LIMITATION LOST PROFITS), HOWEVER CAUSED AND&#xA;ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,&#xA;OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY&#xA;OUT OF THE USE OR DISTRIBUTION OF THE PROGRAM OR THE EXERCISE&#xA;OF ANY RIGHTS GRANTED HEREUNDER, EVEN IF ADVISED OF THE POSSIBILITY&#xA;OF SUCH DAMAGES.&#xA;7. GENERAL&#xA;If any provision of this Agreement is invalid or unenforceable&#xA;under applicable law, it shall not affect the validity or enforceability&#xA;of the remainder of the terms of this Agreement, and without&#xA;further action by the parties hereto, such provision shall be&#xA;reformed to the minimum extent necessary to make such provision&#xA;valid and enforceable.&#xA;If Recipient institutes patent litigation against any entity&#xA;(including a cross-claim or counterclaim in a lawsuit) alleging&#xA;that the Program itself (excluding combinations of the Program&#xA;with other software or hardware) infringes such Recipient&apos;s patent(s),&#xA;then such Recipient&apos;s rights granted under Section 2(b) shall&#xA;terminate as of the date such litigation is filed.&#xA;All Recipient&apos;s rights under this Agreement shall terminate if&#xA;it fails to comply with any of the material terms or conditions&#xA;of this Agreement and does not cure such failure in a reasonable&#xA;period of time after becoming aware of such noncompliance. If&#xA;all Recipient&apos;s rights under this Agreement terminate, Recipient&#xA;agrees to cease use and distribution of the Program as soon as&#xA;reasonably practicable. However, Recipient&apos;s obligations under&#xA;this Agreement and any licenses granted by Recipient relating&#xA;to the Program shall continue and survive.&#xA;Everyone is permitted to copy and distribute copies of this Agreement,&#xA;but in order to avoid inconsistency the Agreement is copyrighted&#xA;and may only be modified in the following manner. The Agreement&#xA;Steward reserves the right to publish new versions (including&#xA;revisions) of this Agreement from time to time. No one other&#xA;than the Agreement Steward has the right to modify this Agreement.&#xA;The Eclipse Foundation is the initial Agreement Steward. The&#xA;Eclipse Foundation may assign the responsibility to serve as&#xA;the Agreement Steward to a suitable separate entity. Each new&#xA;version of the Agreement will be given a distinguishing version&#xA;number. The Program (including Contributions) may always be distributed&#xA;subject to the version of the Agreement under which it was received.&#xA;In addition, after a new version of the Agreement is published,&#xA;Contributor may elect to distribute the Program (including its&#xA;Contributions) under the new version. Except as expressly stated&#xA;in Sections 2(a) and 2(b) above, Recipient receives no rights&#xA;or licenses to the intellectual property of any Contributor under&#xA;this Agreement, whether expressly, by implication, estoppel or&#xA;otherwise. All rights in the Program not expressly granted under&#xA;this Agreement are reserved.&#xA;This Agreement is governed by the laws of the State of New York&#xA;and the intellectual property laws of the United States of America.&#xA;No party to this Agreement will bring a legal action under this&#xA;Agreement more than one year after the cause of action arose.&#xA;Each party waives its rights to a jury trial in any resulting&#xA;litigation.
        </license>
      </licenses>
      <copyright>
        (c) 2011 CollabNet, Inc.
      </copyright>
    </unit>
    <unit id='org.tigris.subversion.subclipse.core' version='1.8.22' singleton='false'>
      <update id='org.tigris.subversion.subclipse.core' range='[0.0.0,1.8.22)' severity='0'/>
      <properties size='1'>
        <property name='org.eclipse.equinox.p2.partial.iu' value='true'/>
      </properties>
      <provides size='3'>
        <provided namespace='org.eclipse.equinox.p2.iu' name='org.tigris.subversion.subclipse.core' version='1.8.22'/>
        <provided namespace='osgi.bundle' name='org.tigris.subversion.subclipse.core' version='1.8.22'/>
        <provided namespace='org.eclipse.equinox.p2.eclipse.type' name='bundle' version='1.0.0'/>
      </provides>
      <artifacts size='1'>
        <artifact classifier='osgi.bundle' id='org.tigris.subversion.subclipse.core' version='1.8.22'/>
      </artifacts>
      <touchpoint id='org.eclipse.equinox.p2.osgi' version='1.0.0'/>
      <touchpointData size='1'>
        <instructions size='1'>
          <instruction key='manifest'>
            Bundle-SymbolicName: org.tigris.subversion.subclipse.core&#xA;Bundle-Version: 1.8.22&#xA;
          </instruction>
        </instructions>
      </touchpointData>
    </unit>
    <unit id='org.tigris.subversion.subclipse.ui' version='1.8.21' singleton='false'>
      <update id='org.tigris.subversion.subclipse.ui' range='[0.0.0,1.8.21)' severity='0'/>
      <properties size='1'>
        <property name='org.eclipse.equinox.p2.partial.iu' value='true'/>
      </properties>
      <provides size='3'>
        <provided namespace='org.eclipse.equinox.p2.iu' name='org.tigris.subversion.subclipse.ui' version='1.8.21'/>
        <provided namespace='osgi.bundle' name='org.tigris.subversion.subclipse.ui' version='1.8.21'/>
        <provided namespace='org.eclipse.equinox.p2.eclipse.type' name='bundle' version='1.0.0'/>
      </provides>
      <artifacts size='1'>
        <artifact classifier='osgi.bundle' id='org.tigris.subversion.subclipse.ui' version='1.8.21'/>
      </artifacts>
      <touchpoint id='org.eclipse.equinox.p2.osgi' version='1.0.0'/>
      <touchpointData size='1'>
        <instructions size='1'>
          <instruction key='manifest'>
            Bundle-SymbolicName: org.tigris.subversion.subclipse.ui&#xA;Bundle-Version: 1.8.21&#xA;
          </instruction>
        </instructions>
      </touchpointData>
    </unit>
    <unit id='http://subclipse.tigris.org/update_1.8.x/site.xml.Subclipse' version='1.0.0.7J7k8ccLKjfh'>
      <properties size='3'>
        <property name='org.eclipse.equinox.p2.name' value='Subclipse'/>
        <property name='org.eclipse.equinox.p2.description' value='Subversion 1.7 plug-in for Eclipse 3.2 and higher.'/>
        <property name='org.eclipse.equinox.p2.type.category' value='true'/>
      </properties>
      <provides size='1'>
        <provided namespace='org.eclipse.equinox.p2.iu' name='http://subclipse.tigris.org/update_1.8.x/site.xml.Subclipse' version='1.0.0.7J7k8ccLKjfh'/>
      </provides>
      <requires size='6'>
        <required namespace='org.eclipse.equinox.p2.iu' name='org.tigris.subversion.clientadapter.feature.feature.group' range='[1.8.6,1.8.6]'/>
        <required namespace='org.eclipse.equinox.p2.iu' name='org.tigris.subversion.subclipse.graph.feature.feature.group' range='[1.1.1,1.1.1]'/>
        <required namespace='org.eclipse.equinox.p2.iu' name='org.tigris.subversion.clientadapter.javahl.feature.feature.group' range='[1.7.10,1.7.10]'/>
        <required namespace='org.eclipse.equinox.p2.iu' name='org.tigris.subversion.subclipse.mylyn.feature.group' range='[3.0.0,3.0.0]'/>
        <required namespace='org.eclipse.equinox.p2.iu' name='com.collabnet.subversion.merge.feature.feature.group' range='[3.0.13,3.0.13]'/>
        <required namespace='org.eclipse.equinox.p2.iu' name='org.tigris.subversion.subclipse.feature.group' range='[1.8.22,1.8.22]'/>
      </requires>
      <touchpoint id='null' version='0.0.0'/>
    </unit>
    <unit id='org.tigris.subversion.subclipse.feature.jar' version='1.8.22'>
      <properties size='7'>
        <property name='org.eclipse.equinox.p2.name' value='Subclipse (Required)'/>
        <property name='org.eclipse.equinox.p2.description' value='%description'/>
        <property name='org.eclipse.equinox.p2.description.url' value='%changesURL'/>
        <property name='org.eclipse.equinox.p2.provider' value='tigris.org'/>
        <property name='org.eclipse.update.feature.plugin' value='org.tigris.subversion.subclipse.core'/>
        <property name='df_LT.license' value='Subclipse Software User Agreement&#xA;11th April, 2006&#xA;&#xA;Subclipse is licensed under the terms of the Eclipse Public&#xA;License v1.0.  http://www.eclipse.org/legal/epl-v10.html&#xA;&#xA;Applicable Licenses&#xA;&#xA;Subclipse is built upon a number of other open source&#xA;technologies and products.  Here is a list of those products&#xA;with links to their licenses.&#xA;&#xA;svnClientAdapter:  Part of the overall Subclipse project,&#xA;svnClientAdapter presents a pluggable high-level interface&#xA;to the Subversion repository.  svnClientAdapter is licensed&#xA;under the Apache2 License.&#xA;http://www.apache.org/licenses/LICENSE-2.0&#xA;&#xA;Depending on the adapter you choose in your preferences, the&#xA;following products and licenses are involved.&#xA;&#xA;Subversion/JavaHL:  JavaHL is a high-level Java language binding&#xA;to the Subversion &quot;C&quot; libraries and is part of the official&#xA;Subversion source distribution.  Subversion is licensed under&#xA;the CollabNet license.&#xA;http://subversion.tigris.org/project_license.html&#xA;&#xA;SVNKit:  SVNKit is a &quot;pure Java&quot; implementation of the&#xA;Subversion network protocols and working copy formats.&#xA;SVNKit is licensed under the TMate license.&#xA;http://svnkit.com/licensing/index.html&#xA;&#xA;Ganymed SSH-2:  SVNKit uses the Ganymed SSH-2 library to support&#xA;the svn+ssh:// protocol.  Ganymed SSH-2 is licensed under the&#xA;Ganymed license.&#xA;http://www.ganymed.ethz.ch/ssh2/LICENSE.txt&#xA;&#xA;IT IS YOUR OBLIGATION TO READ AND ACCEPT ALL SUCH TERMS&#xA;AND CONDITIONS PRIOR TO USE OF THIS CONTENT.&#xA;'/>
        <property name='df_LT.description' value='Subclipse is an Eclipse Team Provider for the Subversion version control system.'/>
      </properties>
      <provides size='3'>
        <provided namespace='org.eclipse.equinox.p2.iu' name='org.tigris.subversion.subclipse.feature.jar' version='1.8.22'/>
        <provided namespace='org.eclipse.equinox.p2.eclipse.type' name='feature' version='1.0.0'/>
        <provided namespace='org.eclipse.update.feature' name='org.tigris.subversion.subclipse' version='1.8.22'/>
      </provides>
      <filter>
        (org.eclipse.update.install.features=true)
      </filter>
      <artifacts size='1'>
        <artifact classifier='org.eclipse.update.feature' id='org.tigris.subversion.subclipse' version='1.8.22'/>
      </artifacts>
      <touchpoint id='org.eclipse.equinox.p2.osgi' version='1.0.0'/>
      <touchpointData size='1'>
        <instructions size='1'>
          <instruction key='zipped'>
            true
          </instruction>
        </instructions>
      </touchpointData>
      <licenses size='1'>
        <license uri='%25licenseURL' url='%25licenseURL'>
          %license
        </license>
      </licenses>
      <copyright uri='http://subclipse.tigris.org/' url='http://subclipse.tigris.org/'>
        http://subclipse.tigris.org/
      </copyright>
    </unit>
    <unit id='org.tigris.subversion.subclipse.tools.usage' version='1.1.0' singleton='false'>
      <update id='org.tigris.subversion.subclipse.tools.usage' range='[0.0.0,1.1.0)' severity='0'/>
      <properties size='1'>
        <property name='org.eclipse.equinox.p2.partial.iu' value='true'/>
      </properties>
      <provides size='3'>
        <provided namespace='org.eclipse.equinox.p2.iu' name='org.tigris.subversion.subclipse.tools.usage' version='1.1.0'/>
        <provided namespace='osgi.bundle' name='org.tigris.subversion.subclipse.tools.usage' version='1.1.0'/>
        <provided namespace='org.eclipse.equinox.p2.eclipse.type' name='bundle' version='1.0.0'/>
      </provides>
      <artifacts size='1'>
        <artifact classifier='osgi.bundle' id='org.tigris.subversion.subclipse.tools.usage' version='1.1.0'/>
      </artifacts>
      <touchpoint id='org.eclipse.equinox.p2.osgi' version='1.0.0'/>
      <touchpointData size='1'>
        <instructions size='1'>
          <instruction key='manifest'>
            Bundle-SymbolicName: org.tigris.subversion.subclipse.tools.usage&#xA;Bundle-Version: 1.1.0&#xA;
          </instruction>
        </instructions>
      </touchpointData>
    </unit>
    <unit id='org.tigris.subversion.clientadapter.javahl.feature.feature.jar' version='1.7.10'>
      <properties size='6'>
        <property name='org.eclipse.equinox.p2.name' value='Subversion JavaHL Native Library Adapter'/>
        <property name='org.eclipse.equinox.p2.description' value='%description'/>
        <property name='org.eclipse.equinox.p2.provider' value='tigris.org'/>
        <property name='org.eclipse.update.feature.plugin' value='org.tigris.subversion.clientadapter.javahl'/>
        <property name='df_LT.license' value='Subclipse Software User Agreement&#xA;11th April, 2006&#xA;&#xA;Subclipse is licensed under the terms of the Eclipse Public&#xA;License v1.0.  http://www.eclipse.org/legal/epl-v10.html&#xA;&#xA;Applicable Licenses&#xA;&#xA;Subclipse is built upon a number of other open source&#xA;technologies and products.  Here is a list of those products&#xA;with links to their licenses.&#xA;&#xA;svnClientAdapter:  Part of the overall Subclipse project,&#xA;svnClientAdapter presents a pluggable high-level interface&#xA;to the Subversion repository.  svnClientAdapter is licensed&#xA;under the Apache2 License.&#xA;http://www.apache.org/licenses/LICENSE-2.0&#xA;&#xA;Depending on the adapter you choose in your preferences, the&#xA;following products and licenses are involved.&#xA;&#xA;Subversion/JavaHL:  JavaHL is a high-level Java language binding&#xA;to the Subversion &quot;C&quot; libraries and is part of the official&#xA;Subversion source distribution.  Subversion is licensed under&#xA;the CollabNet license.&#xA;http://subversion.tigris.org/project_license.html&#xA;&#xA;SVNKit:  SVNKit is a &quot;pure Java&quot; implementation of the&#xA;Subversion network protocols and working copy formats.&#xA;SVNKit is licensed under the TMate license.&#xA;http://svnkit.com/licensing/index.html&#xA;&#xA;Ganymed SSH-2:  SVNKit uses the Ganymed SSH-2 library to support&#xA;the svn+ssh:// protocol.  Ganymed SSH-2 is licensed under the&#xA;Ganymed license.&#xA;http://www.ganymed.ethz.ch/ssh2/LICENSE.txt&#xA;&#xA;IT IS YOUR OBLIGATION TO READ AND ACCEPT ALL SUCH TERMS&#xA;AND CONDITIONS PRIOR TO USE OF THIS CONTENT.&#xA;'/>
        <property name='df_LT.description' value='Subversion Client Adapter implementation using the Subversion native JavaHL library.'/>
      </properties>
      <provides size='3'>
        <provided namespace='org.eclipse.equinox.p2.iu' name='org.tigris.subversion.clientadapter.javahl.feature.feature.jar' version='1.7.10'/>
        <provided namespace='org.eclipse.equinox.p2.eclipse.type' name='feature' version='1.0.0'/>
        <provided namespace='org.eclipse.update.feature' name='org.tigris.subversion.clientadapter.javahl.feature' version='1.7.10'/>
      </provides>
      <filter>
        (org.eclipse.update.install.features=true)
      </filter>
      <artifacts size='1'>
        <artifact classifier='org.eclipse.update.feature' id='org.tigris.subversion.clientadapter.javahl.feature' version='1.7.10'/>
      </artifacts>
      <touchpoint id='org.eclipse.equinox.p2.osgi' version='1.0.0'/>
      <touchpointData size='1'>
        <instructions size='1'>
          <instruction key='zipped'>
            true
          </instruction>
        </instructions>
      </touchpointData>
      <licenses size='1'>
        <license uri='%25licenseURL' url='%25licenseURL'>
          %license
        </license>
      </licenses>
      <copyright uri='http://subclipse.tigris.org/' url='http://subclipse.tigris.org/'>
        http://subclipse.tigris.org/
      </copyright>
    </unit>
    <unit id='com.collabnet.subversion.merge' version='3.0.13' singleton='false'>
      <update id='com.collabnet.subversion.merge' range='[0.0.0,3.0.13)' severity='0'/>
      <properties size='1'>
        <property name='org.eclipse.equinox.p2.partial.iu' value='true'/>
      </properties>
      <provides size='3'>
        <provided namespace='org.eclipse.equinox.p2.iu' name='com.collabnet.subversion.merge' version='3.0.13'/>
        <provided namespace='osgi.bundle' name='com.collabnet.subversion.merge' version='3.0.13'/>
        <provided namespace='org.eclipse.equinox.p2.eclipse.type' name='bundle' version='1.0.0'/>
      </provides>
      <artifacts size='1'>
        <artifact classifier='osgi.bundle' id='com.collabnet.subversion.merge' version='3.0.13'/>
      </artifacts>
      <touchpoint id='org.eclipse.equinox.p2.osgi' version='1.0.0'/>
      <touchpointData size='1'>
        <instructions size='1'>
          <instruction key='manifest'>
            Bundle-SymbolicName: com.collabnet.subversion.merge&#xA;Bundle-Version: 3.0.13&#xA;
          </instruction>
        </instructions>
      </touchpointData>
    </unit>
    <unit id='org.tigris.subversion.clientadapter.feature.feature.group' version='1.8.6' singleton='false'>
      <update id='org.tigris.subversion.clientadapter.feature.feature.group' range='[0.0.0,1.8.6)' severity='0'/>
      <properties size='6'>
        <property name='org.eclipse.equinox.p2.name' value='Subversion Client Adapter (Required)'/>
        <property name='org.eclipse.equinox.p2.description' value='%description'/>
        <property name='org.eclipse.equinox.p2.provider' value='tigris.org'/>
        <property name='org.eclipse.equinox.p2.type.group' value='true'/>
        <property name='df_LT.license' value='Subclipse Software User Agreement&#xA;11th April, 2006&#xA;&#xA;Subclipse is licensed under the terms of the Eclipse Public&#xA;License v1.0.  http://www.eclipse.org/legal/epl-v10.html&#xA;&#xA;Applicable Licenses&#xA;&#xA;Subclipse is built upon a number of other open source&#xA;technologies and products.  Here is a list of those products&#xA;with links to their licenses.&#xA;&#xA;svnClientAdapter:  Part of the overall Subclipse project,&#xA;svnClientAdapter presents a pluggable high-level interface&#xA;to the Subversion repository.  svnClientAdapter is licensed&#xA;under the Apache2 License.&#xA;http://www.apache.org/licenses/LICENSE-2.0&#xA;&#xA;Depending on the adapter you choose in your preferences, the&#xA;following products and licenses are involved.&#xA;&#xA;Subversion/JavaHL:  JavaHL is a high-level Java language binding&#xA;to the Subversion &quot;C&quot; libraries and is part of the official&#xA;Subversion source distribution.  Subversion is licensed under&#xA;the CollabNet license.&#xA;http://subversion.tigris.org/project_license.html&#xA;&#xA;SVNKit:  SVNKit is a &quot;pure Java&quot; implementation of the&#xA;Subversion network protocols and working copy formats.&#xA;SVNKit is licensed under the TMate license.&#xA;http://svnkit.com/licensing/index.html&#xA;&#xA;Ganymed SSH-2:  SVNKit uses the Ganymed SSH-2 library to support&#xA;the svn+ssh:// protocol.  Ganymed SSH-2 is licensed under the&#xA;Ganymed license.&#xA;http://www.ganymed.ethz.ch/ssh2/LICENSE.txt&#xA;&#xA;IT IS YOUR OBLIGATION TO READ AND ACCEPT ALL SUCH TERMS&#xA;AND CONDITIONS PRIOR TO USE OF THIS CONTENT.&#xA;'/>
        <property name='df_LT.description' value='Subversion Client Adapter provides a common API for Subversion client functionality.'/>
      </properties>
      <provides size='2'>
        <provided namespace='org.eclipse.equinox.p2.iu' name='org.tigris.subversion.clientadapter.feature.feature.group' version='1.8.6'/>
        <provided namespace='org.eclipse.equinox.p2.localization' name='df_LT' version='1.0.0'/>
      </provides>
      <requires size='3'>
        <required namespace='org.eclipse.equinox.p2.iu' name='org.eclipse.core.runtime' range='0.0.0'/>
        <required namespace='org.eclipse.equinox.p2.iu' name='org.tigris.subversion.clientadapter' range='[1.8.6,1.8.6]'/>
        <required namespace='org.eclipse.equinox.p2.iu' name='org.tigris.subversion.clientadapter.feature.feature.jar' range='[1.8.6,1.8.6]'>
          <filter>
            (org.eclipse.update.install.features=true)
          </filter>
        </required>
      </requires>
      <touchpoint id='null' version='0.0.0'/>
      <licenses size='1'>
        <license uri='%25licenseURL' url='%25licenseURL'>
          %license
        </license>
      </licenses>
      <copyright uri='http://subclipse.tigris.org/' url='http://subclipse.tigris.org/'>
        http://subclipse.tigris.org/
      </copyright>
    </unit>
    <unit id='org.tigris.subversion.clientadapter' version='1.8.6' singleton='false'>
      <update id='org.tigris.subversion.clientadapter' range='[0.0.0,1.8.6)' severity='0'/>
      <properties size='1'>
        <property name='org.eclipse.equinox.p2.partial.iu' value='true'/>
      </properties>
      <provides size='3'>
        <provided namespace='org.eclipse.equinox.p2.iu' name='org.tigris.subversion.clientadapter' version='1.8.6'/>
        <provided namespace='osgi.bundle' name='org.tigris.subversion.clientadapter' version='1.8.6'/>
        <provided namespace='org.eclipse.equinox.p2.eclipse.type' name='bundle' version='1.0.0'/>
      </provides>
      <artifacts size='1'>
        <artifact classifier='osgi.bundle' id='org.tigris.subversion.clientadapter' version='1.8.6'/>
      </artifacts>
      <touchpoint id='org.eclipse.equinox.p2.osgi' version='1.0.0'/>
      <touchpointData size='1'>
        <instructions size='1'>
          <instruction key='manifest'>
            Bundle-SymbolicName: org.tigris.subversion.clientadapter&#xA;Bundle-Version: 1.8.6&#xA;
          </instruction>
        </instructions>
      </touchpointData>
    </unit>
    <unit id='org.tmatesoft.svnkit.feature.group' version='1.7.9.r9659_v20130411_2103' singleton='false'>
      <update id='org.tmatesoft.svnkit.feature.group' range='[0.0.0,1.7.9.r9659_v20130411_2103)' severity='0'/>
      <properties size='7'>
        <property name='org.eclipse.equinox.p2.name' value='SVNKit Library'/>
        <property name='org.eclipse.equinox.p2.description' value='%description'/>
        <property name='org.eclipse.equinox.p2.description.url' value='http://svnkit.com/'/>
        <property name='org.eclipse.equinox.p2.provider' value='TMate Software'/>
        <property name='org.eclipse.equinox.p2.type.group' value='true'/>
        <property name='df_LT.license' value='The TMate Open Source License&#xA;&#xA;This license applies to all portions of TMate SVNKit library, which&#xA;are not externally-maintained libraries (e.g. Trilead SSH library).&#xA;&#xA;All the source code and compiled classes in package org.tigris.subversion.javahl&#xA;except SvnClient class are covered by the license in JAVAHL-LICENSE file&#xA;&#xA;Copyright (c) 2004-2011 TMate Software. All rights reserved.&#xA;&#xA;Redistribution and use in source and binary forms, with or without modification,&#xA;are permitted provided that the following conditions are met:&#xA;&#xA;* Redistributions of source code must retain the above copyright notice,&#xA;this list of conditions and the following disclaimer.&#xA;&#xA;* Redistributions in binary form must reproduce the above copyright notice,&#xA;this list of conditions and the following disclaimer in the documentation &#xA;and/or other materials provided with the distribution.&#xA;&#xA;* Redistributions in any form must be accompanied by information on how to&#xA;obtain complete source code for the software that uses SVNKit and any &#xA;accompanying software that uses the software that uses SVNKit. The source&#xA;code must either be included in the distribution or be available for no &#xA;more than the cost of distribution plus a nominal fee, and must be freely &#xA;redistributable under reasonable conditions. For an executable file, complete&#xA;source code means the source code for all modules it contains. It does not &#xA;include source code for modules or files that typically accompany the major &#xA;components of the operating system on which the executable file runs.&#xA;&#xA;* Redistribution in any form without redistributing source code for software&#xA;that uses SVNKit is possible only when such redistribution is explictly permitted&#xA;by TMate Software. Please, contact TMate <NAME_EMAIL> to &#xA;get such permission.&#xA;&#xA;THIS SOFTWARE IS PROVIDED BY TMATE SOFTWARE ``AS IS&apos;&apos; AND ANY EXPRESS OR IMPLIED&#xA;WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF &#xA;MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE, OR NON-INFRINGEMENT, ARE&#xA;DISCLAIMED. &#xA;&#xA;IN NO EVENT SHALL TMATE SOFTWARE BE LIABLE FOR ANY DIRECT, INDIRECT,&#xA;INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT&#xA;LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR &#xA;PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF &#xA;LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE&#xA;OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF &#xA;ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.&#xA;&#xA;'/>
        <property name='df_LT.description' value='SVNKit is a pure java Subversion client library that may be used through its own API or serve as a transparent replacement of native javahl bindings.'/>
      </properties>
      <provides size='2'>
        <provided namespace='org.eclipse.equinox.p2.iu' name='org.tmatesoft.svnkit.feature.group' version='1.7.9.r9659_v20130411_2103'/>
        <provided namespace='org.eclipse.equinox.p2.localization' name='df_LT' version='1.0.0'/>
      </provides>
      <requires size='4'>
        <required namespace='org.eclipse.equinox.p2.iu' name='org.tmatesoft.svnkit' range='[1.7.9.r9659_v20130411_2103,1.7.9.r9659_v20130411_2103]'/>
        <required namespace='org.eclipse.equinox.p2.iu' name='org.tmatesoft.sqljet' range='[1.1.7.r1256_v20130327_2103,1.1.7.r1256_v20130327_2103]'/>
        <required namespace='org.eclipse.equinox.p2.iu' name='com.trilead.ssh2' range='[1.0.0.build216_r152_v20130304_1651,1.0.0.build216_r152_v20130304_1651]'/>
        <required namespace='org.eclipse.equinox.p2.iu' name='org.tmatesoft.svnkit.feature.jar' range='[1.7.9.r9659_v20130411_2103,1.7.9.r9659_v20130411_2103]'>
          <filter>
            (org.eclipse.update.install.features=true)
          </filter>
        </required>
      </requires>
      <touchpoint id='null' version='0.0.0'/>
      <licenses size='1'>
        <license uri='' url=''>
          %license
        </license>
      </licenses>
      <copyright uri='http://svnkit.com/' url='http://svnkit.com/'>
        (c) 2004-2011, TMate Software Ltd.
      </copyright>
    </unit>
    <unit id='org.tigris.subversion.subclipse.feature.group' version='1.8.22' singleton='false'>
      <update id='org.tigris.subversion.subclipse.feature.group' range='[0.0.0,1.8.22)' severity='0'/>
      <properties size='7'>
        <property name='org.eclipse.equinox.p2.name' value='Subclipse (Required)'/>
        <property name='org.eclipse.equinox.p2.description' value='%description'/>
        <property name='org.eclipse.equinox.p2.description.url' value='%changesURL'/>
        <property name='org.eclipse.equinox.p2.provider' value='tigris.org'/>
        <property name='org.eclipse.equinox.p2.type.group' value='true'/>
        <property name='df_LT.license' value='Subclipse Software User Agreement&#xA;11th April, 2006&#xA;&#xA;Subclipse is licensed under the terms of the Eclipse Public&#xA;License v1.0.  http://www.eclipse.org/legal/epl-v10.html&#xA;&#xA;Applicable Licenses&#xA;&#xA;Subclipse is built upon a number of other open source&#xA;technologies and products.  Here is a list of those products&#xA;with links to their licenses.&#xA;&#xA;svnClientAdapter:  Part of the overall Subclipse project,&#xA;svnClientAdapter presents a pluggable high-level interface&#xA;to the Subversion repository.  svnClientAdapter is licensed&#xA;under the Apache2 License.&#xA;http://www.apache.org/licenses/LICENSE-2.0&#xA;&#xA;Depending on the adapter you choose in your preferences, the&#xA;following products and licenses are involved.&#xA;&#xA;Subversion/JavaHL:  JavaHL is a high-level Java language binding&#xA;to the Subversion &quot;C&quot; libraries and is part of the official&#xA;Subversion source distribution.  Subversion is licensed under&#xA;the CollabNet license.&#xA;http://subversion.tigris.org/project_license.html&#xA;&#xA;SVNKit:  SVNKit is a &quot;pure Java&quot; implementation of the&#xA;Subversion network protocols and working copy formats.&#xA;SVNKit is licensed under the TMate license.&#xA;http://svnkit.com/licensing/index.html&#xA;&#xA;Ganymed SSH-2:  SVNKit uses the Ganymed SSH-2 library to support&#xA;the svn+ssh:// protocol.  Ganymed SSH-2 is licensed under the&#xA;Ganymed license.&#xA;http://www.ganymed.ethz.ch/ssh2/LICENSE.txt&#xA;&#xA;IT IS YOUR OBLIGATION TO READ AND ACCEPT ALL SUCH TERMS&#xA;AND CONDITIONS PRIOR TO USE OF THIS CONTENT.&#xA;'/>
        <property name='df_LT.description' value='Subclipse is an Eclipse Team Provider for the Subversion version control system.'/>
      </properties>
      <provides size='2'>
        <provided namespace='org.eclipse.equinox.p2.iu' name='org.tigris.subversion.subclipse.feature.group' version='1.8.22'/>
        <provided namespace='org.eclipse.equinox.p2.localization' name='df_LT' version='1.0.0'/>
      </provides>
      <requires size='21'>
        <required namespace='org.eclipse.equinox.p2.iu' name='org.eclipse.core.resources' range='3.2.0'/>
        <required namespace='org.eclipse.equinox.p2.iu' name='org.eclipse.team.core' range='3.2.0'/>
        <required namespace='org.eclipse.equinox.p2.iu' name='org.eclipse.core.runtime' range='3.2.0'/>
        <required namespace='org.eclipse.equinox.p2.iu' name='org.tigris.subversion.clientadapter' range='[1.8.0,1.9.0)'/>
        <required namespace='org.eclipse.equinox.p2.iu' name='org.eclipse.ui.ide' range='0.0.0'/>
        <required namespace='org.eclipse.equinox.p2.iu' name='org.eclipse.ui.views' range='0.0.0'/>
        <required namespace='org.eclipse.equinox.p2.iu' name='org.eclipse.jface.text' range='0.0.0'/>
        <required namespace='org.eclipse.equinox.p2.iu' name='org.eclipse.ui.workbench.texteditor' range='0.0.0'/>
        <required namespace='org.eclipse.equinox.p2.iu' name='org.eclipse.ui.editors' range='0.0.0'/>
        <required namespace='org.eclipse.equinox.p2.iu' name='org.eclipse.team.ui' range='0.0.0'/>
        <required namespace='org.eclipse.equinox.p2.iu' name='org.eclipse.compare' range='0.0.0'/>
        <required namespace='org.eclipse.equinox.p2.iu' name='org.eclipse.ui.console' range='0.0.0'/>
        <required namespace='org.eclipse.equinox.p2.iu' name='org.eclipse.help' range='0.0.0'/>
        <required namespace='org.eclipse.equinox.p2.iu' name='org.eclipse.ui.forms' range='0.0.0'/>
        <required namespace='org.eclipse.equinox.p2.iu' name='org.eclipse.osgi' range='3.2.0'/>
        <required namespace='org.eclipse.equinox.p2.iu' name='org.eclipse.ui' range='3.2.0'/>
        <required namespace='org.eclipse.equinox.p2.iu' name='org.tigris.subversion.subclipse.core' range='[1.8.22,1.8.22]'/>
        <required namespace='org.eclipse.equinox.p2.iu' name='org.tigris.subversion.subclipse.ui' range='[1.8.21,1.8.21]'/>
        <required namespace='org.eclipse.equinox.p2.iu' name='org.tigris.subversion.subclipse.doc' range='[1.3.0,1.3.0]'/>
        <required namespace='org.eclipse.equinox.p2.iu' name='org.tigris.subversion.subclipse.tools.usage' range='[1.1.0,1.1.0]'/>
        <required namespace='org.eclipse.equinox.p2.iu' name='org.tigris.subversion.subclipse.feature.jar' range='[1.8.22,1.8.22]'>
          <filter>
            (org.eclipse.update.install.features=true)
          </filter>
        </required>
      </requires>
      <touchpoint id='null' version='0.0.0'/>
      <licenses size='1'>
        <license uri='%25licenseURL' url='%25licenseURL'>
          %license
        </license>
      </licenses>
      <copyright uri='http://subclipse.tigris.org/' url='http://subclipse.tigris.org/'>
        http://subclipse.tigris.org/
      </copyright>
    </unit>
    <unit id='org.tigris.subversion.clientadapter.javahl.feature.feature.group' version='1.7.10' singleton='false'>
      <update id='org.tigris.subversion.clientadapter.javahl.feature.feature.group' range='[0.0.0,1.7.10)' severity='0'/>
      <properties size='6'>
        <property name='org.eclipse.equinox.p2.name' value='Subversion JavaHL Native Library Adapter'/>
        <property name='org.eclipse.equinox.p2.description' value='%description'/>
        <property name='org.eclipse.equinox.p2.provider' value='tigris.org'/>
        <property name='org.eclipse.equinox.p2.type.group' value='true'/>
        <property name='df_LT.license' value='Subclipse Software User Agreement&#xA;11th April, 2006&#xA;&#xA;Subclipse is licensed under the terms of the Eclipse Public&#xA;License v1.0.  http://www.eclipse.org/legal/epl-v10.html&#xA;&#xA;Applicable Licenses&#xA;&#xA;Subclipse is built upon a number of other open source&#xA;technologies and products.  Here is a list of those products&#xA;with links to their licenses.&#xA;&#xA;svnClientAdapter:  Part of the overall Subclipse project,&#xA;svnClientAdapter presents a pluggable high-level interface&#xA;to the Subversion repository.  svnClientAdapter is licensed&#xA;under the Apache2 License.&#xA;http://www.apache.org/licenses/LICENSE-2.0&#xA;&#xA;Depending on the adapter you choose in your preferences, the&#xA;following products and licenses are involved.&#xA;&#xA;Subversion/JavaHL:  JavaHL is a high-level Java language binding&#xA;to the Subversion &quot;C&quot; libraries and is part of the official&#xA;Subversion source distribution.  Subversion is licensed under&#xA;the CollabNet license.&#xA;http://subversion.tigris.org/project_license.html&#xA;&#xA;SVNKit:  SVNKit is a &quot;pure Java&quot; implementation of the&#xA;Subversion network protocols and working copy formats.&#xA;SVNKit is licensed under the TMate license.&#xA;http://svnkit.com/licensing/index.html&#xA;&#xA;Ganymed SSH-2:  SVNKit uses the Ganymed SSH-2 library to support&#xA;the svn+ssh:// protocol.  Ganymed SSH-2 is licensed under the&#xA;Ganymed license.&#xA;http://www.ganymed.ethz.ch/ssh2/LICENSE.txt&#xA;&#xA;IT IS YOUR OBLIGATION TO READ AND ACCEPT ALL SUCH TERMS&#xA;AND CONDITIONS PRIOR TO USE OF THIS CONTENT.&#xA;'/>
        <property name='df_LT.description' value='Subversion Client Adapter implementation using the Subversion native JavaHL library.'/>
      </properties>
      <provides size='2'>
        <provided namespace='org.eclipse.equinox.p2.iu' name='org.tigris.subversion.clientadapter.javahl.feature.feature.group' version='1.7.10'/>
        <provided namespace='org.eclipse.equinox.p2.localization' name='df_LT' version='1.0.0'/>
      </provides>
      <requires size='6'>
        <required namespace='org.eclipse.equinox.p2.iu' name='org.eclipse.core.runtime' range='0.0.0'/>
        <required namespace='org.eclipse.equinox.p2.iu' name='org.tigris.subversion.clientadapter' range='[1.8.5,1.9.0)'/>
        <required namespace='org.eclipse.equinox.p2.iu' name='org.tigris.subversion.clientadapter.javahl' range='[1.7.10,1.7.10]'/>
        <required namespace='org.eclipse.equinox.p2.iu' name='org.tigris.subversion.clientadapter.javahl.win32' range='[1.7.10,1.7.10]'>
          <filter>
            (&amp;(osgi.arch=x86)(osgi.os=win32))
          </filter>
        </required>
        <required namespace='org.eclipse.equinox.p2.iu' name='org.tigris.subversion.clientadapter.javahl.win64' range='[1.7.10,1.7.10]'>
          <filter>
            (&amp;(osgi.arch=x86_64)(osgi.os=win32))
          </filter>
        </required>
        <required namespace='org.eclipse.equinox.p2.iu' name='org.tigris.subversion.clientadapter.javahl.feature.feature.jar' range='[1.7.10,1.7.10]'>
          <filter>
            (org.eclipse.update.install.features=true)
          </filter>
        </required>
      </requires>
      <touchpoint id='null' version='0.0.0'/>
      <licenses size='1'>
        <license uri='%25licenseURL' url='%25licenseURL'>
          %license
        </license>
      </licenses>
      <copyright uri='http://subclipse.tigris.org/' url='http://subclipse.tigris.org/'>
        http://subclipse.tigris.org/
      </copyright>
    </unit>
    <unit id='http://subclipse.tigris.org/update_1.8.x/site.xml.SVNKit' version='1.0.0.7_7Y7YcLL88F_gSIISRxCKSEIEI5M7539'>
      <properties size='3'>
        <property name='org.eclipse.equinox.p2.name' value='SVNKit'/>
        <property name='org.eclipse.equinox.p2.description' value='SVNKit Library support for Subversion 1.7.x API'/>
        <property name='org.eclipse.equinox.p2.type.category' value='true'/>
      </properties>
      <provides size='1'>
        <provided namespace='org.eclipse.equinox.p2.iu' name='http://subclipse.tigris.org/update_1.8.x/site.xml.SVNKit' version='1.0.0.7_7Y7YcLL88F_gSIISRxCKSEIEI5M7539'/>
      </provides>
      <requires size='3'>
        <required namespace='org.eclipse.equinox.p2.iu' name='net.java.dev.jna.feature.group' range='[3.4.0.t20120117_1605,3.4.0.t20120117_1605]'/>
        <required namespace='org.eclipse.equinox.p2.iu' name='org.tmatesoft.svnkit.feature.group' range='[1.7.9.r9659_v20130411_2103,1.7.9.r9659_v20130411_2103]'/>
        <required namespace='org.eclipse.equinox.p2.iu' name='org.tigris.subversion.clientadapter.svnkit.feature.feature.group' range='[*******,*******]'/>
      </requires>
      <touchpoint id='null' version='0.0.0'/>
    </unit>
    <unit id='org.tigris.subversion.subclipse.graph.feature.feature.jar' version='1.1.1'>
      <properties size='7'>
        <property name='org.eclipse.equinox.p2.name' value='Subversion Revision Graph'/>
        <property name='org.eclipse.equinox.p2.description' value='Subversion Revision Graph for Subclipse'/>
        <property name='org.eclipse.equinox.p2.description.url' value='%changesURL'/>
        <property name='org.eclipse.equinox.p2.provider' value='tigris.org'/>
        <property name='org.eclipse.update.feature.plugin' value='org.tigris.subversion.subclipse.graph'/>
        <property name='df_LT.license' value='Subclipse Software User Agreement&#xA;11th April, 2006&#xA;&#xA;Subclipse is licensed under the terms of the Eclipse Public&#xA;License v1.0.  http://www.eclipse.org/legal/epl-v10.html&#xA;&#xA;Applicable Licenses&#xA;&#xA;Subclipse is built upon a number of other open source&#xA;technologies and products.  Here is a list of those products&#xA;with links to their licenses.&#xA;&#xA;svnClientAdapter:  Part of the overall Subclipse project,&#xA;svnClientAdapter presents a pluggable high-level interface&#xA;to the Subversion repository.  svnClientAdapter is licensed&#xA;under the Apache2 License.&#xA;http://www.apache.org/licenses/LICENSE-2.0&#xA;&#xA;Depending on the adapter you choose in your preferences, the&#xA;following products and licenses are involved.&#xA;&#xA;Subversion/JavaHL:  JavaHL is a high-level Java language binding&#xA;to the Subversion &quot;C&quot; libraries and is part of the official&#xA;Subversion source distribution.  Subversion is licensed under&#xA;the CollabNet license.&#xA;http://subversion.tigris.org/project_license.html&#xA;&#xA;SVNKit:  SVNKit is a &quot;pure Java&quot; implementation of the&#xA;Subversion network protocols and working copy formats.&#xA;SVNKit is licensed under the TMate license.&#xA;http://svnkit.com/licensing/index.html&#xA;&#xA;Ganymed SSH-2:  SVNKit uses the Ganymed SSH-2 library to support&#xA;the svn+ssh:// protocol.  Ganymed SSH-2 is licensed under the&#xA;Ganymed license.&#xA;http://www.ganymed.ethz.ch/ssh2/LICENSE.txt&#xA;&#xA;IT IS YOUR OBLIGATION TO READ AND ACCEPT ALL SUCH TERMS&#xA;AND CONDITIONS PRIOR TO USE OF THIS CONTENT.&#xA;'/>
        <property name='df_LT.description' value='Revision graph feature for Subclipse.'/>
      </properties>
      <provides size='3'>
        <provided namespace='org.eclipse.equinox.p2.iu' name='org.tigris.subversion.subclipse.graph.feature.feature.jar' version='1.1.1'/>
        <provided namespace='org.eclipse.equinox.p2.eclipse.type' name='feature' version='1.0.0'/>
        <provided namespace='org.eclipse.update.feature' name='org.tigris.subversion.subclipse.graph.feature' version='1.1.1'/>
      </provides>
      <filter>
        (org.eclipse.update.install.features=true)
      </filter>
      <artifacts size='1'>
        <artifact classifier='org.eclipse.update.feature' id='org.tigris.subversion.subclipse.graph.feature' version='1.1.1'/>
      </artifacts>
      <touchpoint id='org.eclipse.equinox.p2.osgi' version='1.0.0'/>
      <touchpointData size='1'>
        <instructions size='1'>
          <instruction key='zipped'>
            true
          </instruction>
        </instructions>
      </touchpointData>
      <licenses size='1'>
        <license uri='%25licenseURL' url='%25licenseURL'>
          %license
        </license>
      </licenses>
      <copyright uri='http://subclipse.tigris.org/' url='http://subclipse.tigris.org/'>
        http://subclipse.tigris.org/
      </copyright>
    </unit>
    <unit id='org.tigris.subversion.clientadapter.javahl.win32' version='1.7.10' singleton='false'>
      <update id='org.tigris.subversion.clientadapter.javahl.win32' range='[0.0.0,1.7.10)' severity='0'/>
      <properties size='1'>
        <property name='org.eclipse.equinox.p2.partial.iu' value='true'/>
      </properties>
      <provides size='3'>
        <provided namespace='org.eclipse.equinox.p2.iu' name='org.tigris.subversion.clientadapter.javahl.win32' version='1.7.10'/>
        <provided namespace='osgi.bundle' name='org.tigris.subversion.clientadapter.javahl.win32' version='1.7.10'/>
        <provided namespace='org.eclipse.equinox.p2.eclipse.type' name='bundle' version='1.0.0'/>
      </provides>
      <artifacts size='1'>
        <artifact classifier='osgi.bundle' id='org.tigris.subversion.clientadapter.javahl.win32' version='1.7.10'/>
      </artifacts>
      <touchpoint id='org.eclipse.equinox.p2.osgi' version='1.0.0'/>
      <touchpointData size='1'>
        <instructions size='2'>
          <instruction key='zipped'>
            true
          </instruction>
          <instruction key='manifest'>
            Bundle-SymbolicName: org.tigris.subversion.clientadapter.javahl.win32&#xA;Bundle-Version: 1.7.10&#xA;
          </instruction>
        </instructions>
      </touchpointData>
    </unit>
    <unit id='org.tigris.subversion.clientadapter.javahl.win64' version='1.7.10' singleton='false'>
      <update id='org.tigris.subversion.clientadapter.javahl.win64' range='[0.0.0,1.7.10)' severity='0'/>
      <properties size='1'>
        <property name='org.eclipse.equinox.p2.partial.iu' value='true'/>
      </properties>
      <provides size='3'>
        <provided namespace='org.eclipse.equinox.p2.iu' name='org.tigris.subversion.clientadapter.javahl.win64' version='1.7.10'/>
        <provided namespace='osgi.bundle' name='org.tigris.subversion.clientadapter.javahl.win64' version='1.7.10'/>
        <provided namespace='org.eclipse.equinox.p2.eclipse.type' name='bundle' version='1.0.0'/>
      </provides>
      <artifacts size='1'>
        <artifact classifier='osgi.bundle' id='org.tigris.subversion.clientadapter.javahl.win64' version='1.7.10'/>
      </artifacts>
      <touchpoint id='org.eclipse.equinox.p2.osgi' version='1.0.0'/>
      <touchpointData size='1'>
        <instructions size='2'>
          <instruction key='zipped'>
            true
          </instruction>
          <instruction key='manifest'>
            Bundle-SymbolicName: org.tigris.subversion.clientadapter.javahl.win64&#xA;Bundle-Version: 1.7.10&#xA;
          </instruction>
        </instructions>
      </touchpointData>
    </unit>
    <unit id='org.tmatesoft.sqljet' version='1.1.7.r1256_v20130327_2103' singleton='false'>
      <update id='org.tmatesoft.sqljet' range='[0.0.0,1.1.7.r1256_v20130327_2103)' severity='0'/>
      <properties size='1'>
        <property name='org.eclipse.equinox.p2.partial.iu' value='true'/>
      </properties>
      <provides size='3'>
        <provided namespace='org.eclipse.equinox.p2.iu' name='org.tmatesoft.sqljet' version='1.1.7.r1256_v20130327_2103'/>
        <provided namespace='osgi.bundle' name='org.tmatesoft.sqljet' version='1.1.7.r1256_v20130327_2103'/>
        <provided namespace='org.eclipse.equinox.p2.eclipse.type' name='bundle' version='1.0.0'/>
      </provides>
      <artifacts size='1'>
        <artifact classifier='osgi.bundle' id='org.tmatesoft.sqljet' version='1.1.7.r1256_v20130327_2103'/>
      </artifacts>
      <touchpoint id='org.eclipse.equinox.p2.osgi' version='1.0.0'/>
      <touchpointData size='1'>
        <instructions size='1'>
          <instruction key='manifest'>
            Bundle-SymbolicName: org.tmatesoft.sqljet&#xA;Bundle-Version: 1.1.7.r1256_v20130327_2103&#xA;
          </instruction>
        </instructions>
      </touchpointData>
    </unit>
    <unit id='org.tigris.subversion.clientadapter.svnkit' version='*******' singleton='false'>
      <update id='org.tigris.subversion.clientadapter.svnkit' range='[0.0.0,*******)' severity='0'/>
      <properties size='1'>
        <property name='org.eclipse.equinox.p2.partial.iu' value='true'/>
      </properties>
      <provides size='3'>
        <provided namespace='org.eclipse.equinox.p2.iu' name='org.tigris.subversion.clientadapter.svnkit' version='*******'/>
        <provided namespace='osgi.bundle' name='org.tigris.subversion.clientadapter.svnkit' version='*******'/>
        <provided namespace='org.eclipse.equinox.p2.eclipse.type' name='bundle' version='1.0.0'/>
      </provides>
      <artifacts size='1'>
        <artifact classifier='osgi.bundle' id='org.tigris.subversion.clientadapter.svnkit' version='*******'/>
      </artifacts>
      <touchpoint id='org.eclipse.equinox.p2.osgi' version='1.0.0'/>
      <touchpointData size='1'>
        <instructions size='1'>
          <instruction key='manifest'>
            Bundle-SymbolicName: org.tigris.subversion.clientadapter.svnkit&#xA;Bundle-Version: *******&#xA;
          </instruction>
        </instructions>
      </touchpointData>
    </unit>
    <unit id='org.tigris.subversion.clientadapter.feature.feature.jar' version='1.8.6'>
      <properties size='6'>
        <property name='org.eclipse.equinox.p2.name' value='Subversion Client Adapter (Required)'/>
        <property name='org.eclipse.equinox.p2.description' value='%description'/>
        <property name='org.eclipse.equinox.p2.provider' value='tigris.org'/>
        <property name='org.eclipse.update.feature.plugin' value='org.tigris.subversion.clientadapter'/>
        <property name='df_LT.license' value='Subclipse Software User Agreement&#xA;11th April, 2006&#xA;&#xA;Subclipse is licensed under the terms of the Eclipse Public&#xA;License v1.0.  http://www.eclipse.org/legal/epl-v10.html&#xA;&#xA;Applicable Licenses&#xA;&#xA;Subclipse is built upon a number of other open source&#xA;technologies and products.  Here is a list of those products&#xA;with links to their licenses.&#xA;&#xA;svnClientAdapter:  Part of the overall Subclipse project,&#xA;svnClientAdapter presents a pluggable high-level interface&#xA;to the Subversion repository.  svnClientAdapter is licensed&#xA;under the Apache2 License.&#xA;http://www.apache.org/licenses/LICENSE-2.0&#xA;&#xA;Depending on the adapter you choose in your preferences, the&#xA;following products and licenses are involved.&#xA;&#xA;Subversion/JavaHL:  JavaHL is a high-level Java language binding&#xA;to the Subversion &quot;C&quot; libraries and is part of the official&#xA;Subversion source distribution.  Subversion is licensed under&#xA;the CollabNet license.&#xA;http://subversion.tigris.org/project_license.html&#xA;&#xA;SVNKit:  SVNKit is a &quot;pure Java&quot; implementation of the&#xA;Subversion network protocols and working copy formats.&#xA;SVNKit is licensed under the TMate license.&#xA;http://svnkit.com/licensing/index.html&#xA;&#xA;Ganymed SSH-2:  SVNKit uses the Ganymed SSH-2 library to support&#xA;the svn+ssh:// protocol.  Ganymed SSH-2 is licensed under the&#xA;Ganymed license.&#xA;http://www.ganymed.ethz.ch/ssh2/LICENSE.txt&#xA;&#xA;IT IS YOUR OBLIGATION TO READ AND ACCEPT ALL SUCH TERMS&#xA;AND CONDITIONS PRIOR TO USE OF THIS CONTENT.&#xA;'/>
        <property name='df_LT.description' value='Subversion Client Adapter provides a common API for Subversion client functionality.'/>
      </properties>
      <provides size='3'>
        <provided namespace='org.eclipse.equinox.p2.iu' name='org.tigris.subversion.clientadapter.feature.feature.jar' version='1.8.6'/>
        <provided namespace='org.eclipse.equinox.p2.eclipse.type' name='feature' version='1.0.0'/>
        <provided namespace='org.eclipse.update.feature' name='org.tigris.subversion.clientadapter.feature' version='1.8.6'/>
      </provides>
      <filter>
        (org.eclipse.update.install.features=true)
      </filter>
      <artifacts size='1'>
        <artifact classifier='org.eclipse.update.feature' id='org.tigris.subversion.clientadapter.feature' version='1.8.6'/>
      </artifacts>
      <touchpoint id='org.eclipse.equinox.p2.osgi' version='1.0.0'/>
      <touchpointData size='1'>
        <instructions size='1'>
          <instruction key='zipped'>
            true
          </instruction>
        </instructions>
      </touchpointData>
      <licenses size='1'>
        <license uri='%25licenseURL' url='%25licenseURL'>
          %license
        </license>
      </licenses>
      <copyright uri='http://subclipse.tigris.org/' url='http://subclipse.tigris.org/'>
        http://subclipse.tigris.org/
      </copyright>
    </unit>
    <unit id='net.java.dev.jna.feature.group' version='3.4.0.t20120117_1605' singleton='false'>
      <update id='net.java.dev.jna.feature.group' range='[0.0.0,3.4.0.t20120117_1605)' severity='0'/>
      <properties size='6'>
        <property name='org.eclipse.equinox.p2.name' value='JNA Library'/>
        <property name='org.eclipse.equinox.p2.description' value='JNA Library'/>
        <property name='org.eclipse.equinox.p2.description.url' value='http://jna.dev.java.net/'/>
        <property name='org.eclipse.equinox.p2.type.group' value='true'/>
        <property name='df_LT.license' value='Copyright (c) 2008 Timothy Wall, All Rights Reserved&#xA;&#xA;This library is free software; you can redistribute it and/or&#xA;modify it under the terms of the GNU Lesser General Public&#xA;License as published by the Free Software Foundation; either&#xA;version 2.1 of the License, or (at your option) any later version.&#xA;&#xA;This library is distributed in the hope that it will be useful,&#xA;but WITHOUT ANY WARRANTY; without even the implied warranty of&#xA;MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU&#xA;Lesser General Public License for more details.'/>
        <property name='df_LT.description' value='JNA provides Java programs easy access to native shared libraries (DLLs on Windows) without writing anything but Java code - no JNI or native code is required. This functionality is comparable to Windows&amp;apos; Platform/Invoke and Python&amp;apos;s ctypes. Access is dynamic at runtime without code generation.'/>
      </properties>
      <provides size='2'>
        <provided namespace='org.eclipse.equinox.p2.iu' name='net.java.dev.jna.feature.group' version='3.4.0.t20120117_1605'/>
        <provided namespace='org.eclipse.equinox.p2.localization' name='df_LT' version='1.0.0'/>
      </provides>
      <requires size='2'>
        <required namespace='org.eclipse.equinox.p2.iu' name='net.java.dev.jna' range='[3.4.0.t20120117_1605,3.4.0.t20120117_1605]'/>
        <required namespace='org.eclipse.equinox.p2.iu' name='net.java.dev.jna.feature.jar' range='[3.4.0.t20120117_1605,3.4.0.t20120117_1605]'>
          <filter>
            (org.eclipse.update.install.features=true)
          </filter>
        </required>
      </requires>
      <touchpoint id='null' version='0.0.0'/>
      <licenses size='1'>
        <license uri='http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html' url='http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html'>
          %license
        </license>
      </licenses>
      <copyright uri='http://jna.dev.java.net/' url='http://jna.dev.java.net/'>
        Copyright (c) 2008 Timothy Wall, All Rights Reserved
      </copyright>
    </unit>
    <unit id='com.collabnet.subversion.merge.feature.feature.group' version='3.0.13' singleton='false'>
      <update id='com.collabnet.subversion.merge.feature.feature.group' range='[0.0.0,3.0.13)' severity='0'/>
      <properties size='5'>
        <property name='org.eclipse.equinox.p2.name' value='CollabNet Merge Client'/>
        <property name='org.eclipse.equinox.p2.description' value='The CollabNet Merge client provides powerful Subversion merge capabilities within the Eclipse environment.'/>
        <property name='org.eclipse.equinox.p2.description.url' value='http://desktop-eclipse.open.collab.net'/>
        <property name='org.eclipse.equinox.p2.provider' value='CollabNet'/>
        <property name='org.eclipse.equinox.p2.type.group' value='true'/>
      </properties>
      <provides size='1'>
        <provided namespace='org.eclipse.equinox.p2.iu' name='com.collabnet.subversion.merge.feature.feature.group' version='3.0.13'/>
      </provides>
      <requires size='16'>
        <required namespace='org.eclipse.equinox.p2.iu' name='org.eclipse.ui' range='3.3.0'/>
        <required namespace='org.eclipse.equinox.p2.iu' name='org.eclipse.core.runtime' range='3.3.0'/>
        <required namespace='org.eclipse.equinox.p2.iu' name='org.tigris.subversion.subclipse.ui' range='1.7.0'/>
        <required namespace='org.eclipse.equinox.p2.iu' name='org.tigris.subversion.subclipse.core' range='1.7.0'/>
        <required namespace='org.eclipse.equinox.p2.iu' name='org.eclipse.core.resources' range='3.3.0'/>
        <required namespace='org.eclipse.equinox.p2.iu' name='org.eclipse.team.core' range='3.3.0'/>
        <required namespace='org.eclipse.equinox.p2.iu' name='org.eclipse.team.ui' range='3.3.0'/>
        <required namespace='org.eclipse.equinox.p2.iu' name='org.eclipse.ui.ide' range='3.3.0'/>
        <required namespace='org.eclipse.equinox.p2.iu' name='org.eclipse.ui.views' range='3.2.0'/>
        <required namespace='org.eclipse.equinox.p2.iu' name='org.eclipse.compare' range='3.3.0'/>
        <required namespace='org.eclipse.equinox.p2.iu' name='org.eclipse.ui.editors' range='3.3.0'/>
        <required namespace='org.eclipse.equinox.p2.iu' name='org.eclipse.jface.text' range='3.3.0'/>
        <required namespace='org.eclipse.equinox.p2.iu' name='org.eclipse.ui.forms' range='3.3.0'/>
        <required namespace='org.eclipse.equinox.p2.iu' name='org.eclipse.core.filesystem' range='1.1.0'/>
        <required namespace='org.eclipse.equinox.p2.iu' name='com.collabnet.subversion.merge' range='[3.0.13,3.0.13]'/>
        <required namespace='org.eclipse.equinox.p2.iu' name='com.collabnet.subversion.merge.feature.feature.jar' range='[3.0.13,3.0.13]'>
          <filter>
            (org.eclipse.update.install.features=true)
          </filter>
        </required>
      </requires>
      <touchpoint id='null' version='0.0.0'/>
      <licenses size='1'>
        <license uri='http://www.eclipse.org/legal/epl-v10.html' url='http://www.eclipse.org/legal/epl-v10.html'>
          Eclipse Public License - v 1.0&#xA;THE ACCOMPANYING PROGRAM IS PROVIDED UNDER THE TERMS OF THIS&#xA;ECLIPSE PUBLIC LICENSE (&quot;AGREEMENT&quot;). ANY USE, REPRODUCTION OR&#xA;DISTRIBUTION OF THE PROGRAM CONSTITUTES RECIPIENT&apos;S ACCEPTANCE&#xA;OF THIS AGREEMENT.&#xA;1. DEFINITIONS&#xA;&quot;Contribution&quot; means:&#xA;a) in the case of the initial Contributor, the initial code and&#xA;documentation distributed under this Agreement, and&#xA;b) in the case of each subsequent Contributor:&#xA;i) changes to the Program, and&#xA;ii) additions to the Program;&#xA;where such changes and/or additions to the Program originate&#xA;from and are distributed by that particular Contributor. A Contribution&#xA;&apos;originates&apos; from a Contributor if it was added to the Program&#xA;by such Contributor itself or anyone acting on such Contributor&apos;s&#xA;behalf. Contributions do not include additions to the Program&#xA;which: (i) are separate modules of software distributed in conjunction&#xA;with the Program under their own license agreement, and (ii)&#xA;are not derivative works of the Program.&#xA;&quot;Contributor&quot; means any person or entity that distributes the&#xA;Program.&#xA;&quot;Licensed Patents &quot; mean patent claims licensable by a Contributor&#xA;which are necessarily infringed by the use or sale of its Contribution&#xA;alone or when combined with the Program.&#xA;&quot;Program&quot; means the Contributions distributed in accordance with&#xA;this Agreement.&#xA;&quot;Recipient&quot; means anyone who receives the Program under this&#xA;Agreement, including all Contributors.&#xA;2. GRANT OF RIGHTS&#xA;a) Subject to the terms of this Agreement, each Contributor hereby&#xA;grants Recipient a non-exclusive, worldwide, royalty-free copyright&#xA;license to reproduce, prepare derivative works of, publicly display,&#xA;publicly perform, distribute and sublicense the Contribution&#xA;of such Contributor, if any, and such derivative works, in source&#xA;code and object code form.&#xA;b) Subject to the terms of this Agreement, each Contributor hereby&#xA;grants Recipient a non-exclusive, worldwide, royalty-free patent&#xA;license under Licensed Patents to make, use, sell, offer to sell,&#xA;import and otherwise transfer the Contribution of such Contributor,&#xA;if any, in source code and object code form. This patent license&#xA;shall apply to the combination of the Contribution and the Program&#xA;if, at the time the Contribution is added by the Contributor,&#xA;such addition of the Contribution causes such combination to&#xA;be covered by the Licensed Patents. The patent license shall&#xA;not apply to any other combinations which include the Contribution.&#xA;No hardware per se is licensed hereunder.&#xA;c) Recipient understands that although each Contributor grants&#xA;the licenses to its Contributions set forth herein, no assurances&#xA;are provided by any Contributor that the Program does not infringe&#xA;the patent or other intellectual property rights of any other&#xA;entity. Each Contributor disclaims any liability to Recipient&#xA;for claims brought by any other entity based on infringement&#xA;of intellectual property rights or otherwise. As a condition&#xA;to exercising the rights and licenses granted hereunder, each&#xA;Recipient hereby assumes sole responsibility to secure any other&#xA;intellectual property rights needed, if any. For example, if&#xA;a third party patent license is required to allow Recipient to&#xA;distribute the Program, it is Recipient&apos;s responsibility to acquire&#xA;that license before distributing the Program.&#xA;d) Each Contributor represents that to its knowledge it has sufficient&#xA;copyright rights in its Contribution, if any, to grant the copyright&#xA;license set forth in this Agreement.&#xA;3. REQUIREMENTS&#xA;A Contributor may choose to distribute the Program in object&#xA;code form under its own license agreement, provided that:&#xA;a) it complies with the terms and conditions of this Agreement;&#xA;and&#xA;b) its license agreement:&#xA;i) effectively disclaims on behalf of all Contributors all warranties&#xA;and conditions, express and implied, including warranties or&#xA;conditions of title and non-infringement, and implied warranties&#xA;or conditions of merchantability and fitness for a particular&#xA;purpose;&#xA;ii) effectively excludes on behalf of all Contributors all liability&#xA;for damages, including direct, indirect, special, incidental&#xA;and consequential damages, such as lost profits;&#xA;iii) states that any provisions which differ from this Agreement&#xA;are offered by that Contributor alone and not by any other party;&#xA;and&#xA;iv) states that source code for the Program is available from&#xA;such Contributor, and informs licensees how to obtain it in a&#xA;reasonable manner on or through a medium customarily used for&#xA;software exchange.&#xA;When the Program is made available in source code form:&#xA;a) it must be made available under this Agreement; and&#xA;b) a copy of this Agreement must be included with each copy of&#xA;the Program.&#xA;Contributors may not remove or alter any copyright notices contained&#xA;within the Program.&#xA;Each Contributor must identify itself as the originator of its&#xA;Contribution, if any, in a manner that reasonably allows subsequent&#xA;Recipients to identify the originator of the Contribution.&#xA;4. COMMERCIAL DISTRIBUTION&#xA;Commercial distributors of software may accept certain responsibilities&#xA;with respect to end users, business partners and the like. While&#xA;this license is intended to facilitate the commercial use of&#xA;the Program, the Contributor who includes the Program in a commercial&#xA;product offering should do so in a manner which does not create&#xA;potential liability for other Contributors. Therefore, if a Contributor&#xA;includes the Program in a commercial product offering, such Contributor&#xA;(&quot;Commercial Contributor&quot;) hereby agrees to defend and indemnify&#xA;every other Contributor (&quot;Indemnified Contributor&quot;) against any&#xA;losses, damages and costs (collectively &quot;Losses&quot;) arising from&#xA;claims, lawsuits and other legal actions brought by a third party&#xA;against the Indemnified Contributor to the extent caused by the&#xA;acts or omissions of such Commercial Contributor in connection&#xA;with its distribution of the Program in a commercial product&#xA;offering. The obligations in this section do not apply to any&#xA;claims or Losses relating to any actual or alleged intellectual&#xA;property infringement. In order to qualify, an Indemnified Contributor&#xA;must: a) promptly notify the Commercial Contributor in writing&#xA;of such claim, and b) allow the Commercial Contributor to control,&#xA;and cooperate with the Commercial Contributor in, the defense&#xA;and any related settlement negotiations. The Indemnified Contributor&#xA;may participate in any such claim at its own expense.&#xA;For example, a Contributor might include the Program in a commercial&#xA;product offering, Product X. That Contributor is then a Commercial&#xA;Contributor. If that Commercial Contributor then makes performance&#xA;claims, or offers warranties related to Product X, those performance&#xA;claims and warranties are such Commercial Contributor&apos;s responsibility&#xA;alone. Under this section, the Commercial Contributor would have&#xA;to defend claims against the other Contributors related to those&#xA;performance claims and warranties, and if a court requires any&#xA;other Contributor to pay any damages as a result, the Commercial&#xA;Contributor must pay those damages.&#xA;5. NO WARRANTY&#xA;EXCEPT AS EXPRESSLY SET FORTH IN THIS AGREEMENT, THE PROGRAM&#xA;IS PROVIDED ON AN &quot;AS IS&quot; BASIS, WITHOUT WARRANTIES OR CONDITIONS&#xA;OF ANY KIND, EITHER EXPRESS OR IMPLIED INCLUDING, WITHOUT LIMITATION,&#xA;ANY WARRANTIES OR CONDITIONS OF TITLE, NON-INFRINGEMENT, MERCHANTABILITY&#xA;OR FITNESS FOR A PARTICULAR PURPOSE. Each Recipient is solely&#xA;responsible for determining the appropriateness of using and&#xA;distributing the Program and assumes all risks associated with&#xA;its exercise of rights under this Agreement , including but not&#xA;limited to the risks and costs of program errors, compliance&#xA;with applicable laws, damage to or loss of data, programs or&#xA;equipment, and unavailability or interruption of operations.&#xA;6. DISCLAIMER OF LIABILITY&#xA;EXCEPT AS EXPRESSLY SET FORTH IN THIS AGREEMENT, NEITHER RECIPIENT&#xA;NOR ANY CONTRIBUTORS SHALL HAVE ANY LIABILITY FOR ANY DIRECT,&#xA;INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES&#xA;(INCLUDING WITHOUT LIMITATION LOST PROFITS), HOWEVER CAUSED AND&#xA;ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,&#xA;OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY&#xA;OUT OF THE USE OR DISTRIBUTION OF THE PROGRAM OR THE EXERCISE&#xA;OF ANY RIGHTS GRANTED HEREUNDER, EVEN IF ADVISED OF THE POSSIBILITY&#xA;OF SUCH DAMAGES.&#xA;7. GENERAL&#xA;If any provision of this Agreement is invalid or unenforceable&#xA;under applicable law, it shall not affect the validity or enforceability&#xA;of the remainder of the terms of this Agreement, and without&#xA;further action by the parties hereto, such provision shall be&#xA;reformed to the minimum extent necessary to make such provision&#xA;valid and enforceable.&#xA;If Recipient institutes patent litigation against any entity&#xA;(including a cross-claim or counterclaim in a lawsuit) alleging&#xA;that the Program itself (excluding combinations of the Program&#xA;with other software or hardware) infringes such Recipient&apos;s patent(s),&#xA;then such Recipient&apos;s rights granted under Section 2(b) shall&#xA;terminate as of the date such litigation is filed.&#xA;All Recipient&apos;s rights under this Agreement shall terminate if&#xA;it fails to comply with any of the material terms or conditions&#xA;of this Agreement and does not cure such failure in a reasonable&#xA;period of time after becoming aware of such noncompliance. If&#xA;all Recipient&apos;s rights under this Agreement terminate, Recipient&#xA;agrees to cease use and distribution of the Program as soon as&#xA;reasonably practicable. However, Recipient&apos;s obligations under&#xA;this Agreement and any licenses granted by Recipient relating&#xA;to the Program shall continue and survive.&#xA;Everyone is permitted to copy and distribute copies of this Agreement,&#xA;but in order to avoid inconsistency the Agreement is copyrighted&#xA;and may only be modified in the following manner. The Agreement&#xA;Steward reserves the right to publish new versions (including&#xA;revisions) of this Agreement from time to time. No one other&#xA;than the Agreement Steward has the right to modify this Agreement.&#xA;The Eclipse Foundation is the initial Agreement Steward. The&#xA;Eclipse Foundation may assign the responsibility to serve as&#xA;the Agreement Steward to a suitable separate entity. Each new&#xA;version of the Agreement will be given a distinguishing version&#xA;number. The Program (including Contributions) may always be distributed&#xA;subject to the version of the Agreement under which it was received.&#xA;In addition, after a new version of the Agreement is published,&#xA;Contributor may elect to distribute the Program (including its&#xA;Contributions) under the new version. Except as expressly stated&#xA;in Sections 2(a) and 2(b) above, Recipient receives no rights&#xA;or licenses to the intellectual property of any Contributor under&#xA;this Agreement, whether expressly, by implication, estoppel or&#xA;otherwise. All rights in the Program not expressly granted under&#xA;this Agreement are reserved.&#xA;This Agreement is governed by the laws of the State of New York&#xA;and the intellectual property laws of the United States of America.&#xA;No party to this Agreement will bring a legal action under this&#xA;Agreement more than one year after the cause of action arose.&#xA;Each party waives its rights to a jury trial in any resulting&#xA;litigation.
        </license>
      </licenses>
      <copyright>
        (c) 2011 CollabNet, Inc.
      </copyright>
    </unit>
    <unit id='org.tigris.subversion.subclipse.mylyn' version='3.0.0' singleton='false'>
      <update id='org.tigris.subversion.subclipse.mylyn' range='[0.0.0,3.0.0)' severity='0'/>
      <properties size='1'>
        <property name='org.eclipse.equinox.p2.partial.iu' value='true'/>
      </properties>
      <provides size='3'>
        <provided namespace='org.eclipse.equinox.p2.iu' name='org.tigris.subversion.subclipse.mylyn' version='3.0.0'/>
        <provided namespace='osgi.bundle' name='org.tigris.subversion.subclipse.mylyn' version='3.0.0'/>
        <provided namespace='org.eclipse.equinox.p2.eclipse.type' name='bundle' version='1.0.0'/>
      </provides>
      <artifacts size='1'>
        <artifact classifier='osgi.bundle' id='org.tigris.subversion.subclipse.mylyn' version='3.0.0'/>
      </artifacts>
      <touchpoint id='org.eclipse.equinox.p2.osgi' version='1.0.0'/>
      <touchpointData size='1'>
        <instructions size='1'>
          <instruction key='manifest'>
            Bundle-SymbolicName: org.tigris.subversion.subclipse.mylyn&#xA;Bundle-Version: 3.0.0&#xA;
          </instruction>
        </instructions>
      </touchpointData>
    </unit>
    <unit id='org.tigris.subversion.subclipse.mylyn.feature.group' version='3.0.0' singleton='false'>
      <update id='org.tigris.subversion.subclipse.mylyn.feature.group' range='[0.0.0,3.0.0)' severity='0'/>
      <properties size='7'>
        <property name='org.eclipse.equinox.p2.name' value='Subclipse Integration for Mylyn 3.x (Optional)'/>
        <property name='org.eclipse.equinox.p2.description' value='%description'/>
        <property name='org.eclipse.equinox.p2.description.url' value='%changesURL'/>
        <property name='org.eclipse.equinox.p2.provider' value='tigris.org'/>
        <property name='org.eclipse.equinox.p2.type.group' value='true'/>
        <property name='df_LT.license' value='Subclipse Software User Agreement&#xA;11th April, 2006&#xA;&#xA;Subclipse is licensed under the terms of the Eclipse Public&#xA;License v1.0.  http://www.eclipse.org/legal/epl-v10.html&#xA;&#xA;Applicable Licenses&#xA;&#xA;Subclipse is built upon a number of other open source&#xA;technologies and products.  Here is a list of those products&#xA;with links to their licenses.&#xA;&#xA;svnClientAdapter:  Part of the overall Subclipse project,&#xA;svnClientAdapter presents a plugable high-level interface&#xA;to the Subversion repository.  svnClientAdapter is licensed&#xA;under the Eclipse Public License v1.0.&#xA;http://www.eclipse.org/legal/epl-v10.html&#xA;&#xA;Depending on the adapter you choose in your preferences, the&#xA;following products and licenses are involved.&#xA;&#xA;Subversion/JavaHL:  JavaHL is a high-level Java language binding&#xA;to the Subversion &quot;C&quot; libraries and is part of the official&#xA;Subversion source distribution.  Subversion is licensed under&#xA;the CollabNet license.&#xA;http://subversion.tigris.org/project_license.html&#xA;&#xA;SVNKit:  SVNKit is a &quot;pure Java&quot; implementation of the&#xA;Subversion network protocols and working copy formats.&#xA;SVNKit is licensed under the TMate license.&#xA;http://svnkit.com/licensing/index.html&#xA;&#xA;Ganymed SSH-2:  SVNKit uses the Ganymed SSH-2 library to support&#xA;the svn+ssh:// protocol.  Ganymed SSH-2 is licensed under the&#xA;Ganymed license.&#xA;http://www.ganymed.ethz.ch/ssh2/LICENSE.txt&#xA;&#xA;IT IS YOUR OBLIGATION TO READ AND ACCEPT ALL SUCH TERMS&#xA;AND CONDITIONS PRIOR TO USE OF THIS CONTENT.&#xA;'/>
        <property name='df_LT.description' value='Provides integration features between Subclipse and Mylyn,such as automatic change set management and linking projects to issue tracking systemsbased on svn properties.'/>
      </properties>
      <provides size='2'>
        <provided namespace='org.eclipse.equinox.p2.iu' name='org.tigris.subversion.subclipse.mylyn.feature.group' version='3.0.0'/>
        <provided namespace='org.eclipse.equinox.p2.localization' name='df_LT' version='1.0.0'/>
      </provides>
      <requires size='11'>
        <required namespace='org.eclipse.equinox.p2.iu' name='org.eclipse.core.resources' range='3.2.0'/>
        <required namespace='org.eclipse.equinox.p2.iu' name='org.eclipse.core.runtime' range='3.2.0'/>
        <required namespace='org.eclipse.equinox.p2.iu' name='org.eclipse.team.core' range='3.2.0'/>
        <required namespace='org.eclipse.equinox.p2.iu' name='org.tigris.subversion.subclipse.core' range='1.1.9'/>
        <required namespace='org.eclipse.equinox.p2.iu' name='org.tigris.subversion.subclipse.ui' range='1.1.9'/>
        <required namespace='org.eclipse.equinox.p2.iu' name='org.eclipse.ui' range='3.2.0'/>
        <required namespace='org.eclipse.equinox.p2.iu' name='org.eclipse.mylyn.tasks.core' range='[3.0.0,4.0.0)'/>
        <required namespace='org.eclipse.equinox.p2.iu' name='org.eclipse.mylyn.tasks.ui' range='[3.0.0,4.0.0)'/>
        <required namespace='org.eclipse.equinox.p2.iu' name='org.eclipse.mylyn.team.ui' range='[3.0.0,4.0.0)'/>
        <required namespace='org.eclipse.equinox.p2.iu' name='org.tigris.subversion.subclipse.mylyn' range='[3.0.0,3.0.0]'/>
        <required namespace='org.eclipse.equinox.p2.iu' name='org.tigris.subversion.subclipse.mylyn.feature.jar' range='[3.0.0,3.0.0]'>
          <filter>
            (org.eclipse.update.install.features=true)
          </filter>
        </required>
      </requires>
      <touchpoint id='null' version='0.0.0'/>
      <licenses size='1'>
        <license uri='%25licenseURL' url='%25licenseURL'>
          %license
        </license>
      </licenses>
      <copyright uri='http://subclipse.tigris.org/' url='http://subclipse.tigris.org/'>
        http://subclipse.tigris.org/
      </copyright>
    </unit>
    <unit id='org.tigris.subversion.subclipse.graph' version='1.1.1' singleton='false'>
      <update id='org.tigris.subversion.subclipse.graph' range='[0.0.0,1.1.1)' severity='0'/>
      <properties size='1'>
        <property name='org.eclipse.equinox.p2.partial.iu' value='true'/>
      </properties>
      <provides size='3'>
        <provided namespace='org.eclipse.equinox.p2.iu' name='org.tigris.subversion.subclipse.graph' version='1.1.1'/>
        <provided namespace='osgi.bundle' name='org.tigris.subversion.subclipse.graph' version='1.1.1'/>
        <provided namespace='org.eclipse.equinox.p2.eclipse.type' name='bundle' version='1.0.0'/>
      </provides>
      <artifacts size='1'>
        <artifact classifier='osgi.bundle' id='org.tigris.subversion.subclipse.graph' version='1.1.1'/>
      </artifacts>
      <touchpoint id='org.eclipse.equinox.p2.osgi' version='1.0.0'/>
      <touchpointData size='1'>
        <instructions size='1'>
          <instruction key='manifest'>
            Bundle-SymbolicName: org.tigris.subversion.subclipse.graph&#xA;Bundle-Version: 1.1.1&#xA;
          </instruction>
        </instructions>
      </touchpointData>
    </unit>
    <unit id='org.tigris.subversion.clientadapter.svnkit.feature.feature.group' version='*******' singleton='false'>
      <update id='org.tigris.subversion.clientadapter.svnkit.feature.feature.group' range='[0.0.0,*******)' severity='0'/>
      <properties size='6'>
        <property name='org.eclipse.equinox.p2.name' value='SVNKit Client Adapter (Not required)'/>
        <property name='org.eclipse.equinox.p2.description' value='%description'/>
        <property name='org.eclipse.equinox.p2.provider' value='tigris.org'/>
        <property name='org.eclipse.equinox.p2.type.group' value='true'/>
        <property name='df_LT.license' value='Subclipse Software User Agreement&#xA;11th April, 2006&#xA;&#xA;Subclipse is licensed under the terms of the Eclipse Public&#xA;License v1.0.  http://www.eclipse.org/legal/epl-v10.html&#xA;&#xA;Applicable Licenses&#xA;&#xA;Subclipse is built upon a number of other open source&#xA;technologies and products.  Here is a list of those products&#xA;with links to their licenses.&#xA;&#xA;svnClientAdapter:  Part of the overall Subclipse project,&#xA;svnClientAdapter presents a pluggable high-level interface&#xA;to the Subversion repository.  svnClientAdapter is licensed&#xA;under the Apache2 License.&#xA;http://www.apache.org/licenses/LICENSE-2.0&#xA;&#xA;Depending on the adapter you choose in your preferences, the&#xA;following products and licenses are involved.&#xA;&#xA;Subversion/JavaHL:  JavaHL is a high-level Java language binding&#xA;to the Subversion &quot;C&quot; libraries and is part of the official&#xA;Subversion source distribution.  Subversion is licensed under&#xA;the CollabNet license.&#xA;http://subversion.tigris.org/project_license.html&#xA;&#xA;SVNKit:  SVNKit is a &quot;pure Java&quot; implementation of the&#xA;Subversion network protocols and working copy formats.&#xA;SVNKit is licensed under the TMate license.&#xA;http://svnkit.com/licensing/index.html&#xA;&#xA;Ganymed SSH-2:  SVNKit uses the Ganymed SSH-2 library to support&#xA;the svn+ssh:// protocol.  Ganymed SSH-2 is licensed under the&#xA;Ganymed license.&#xA;http://www.ganymed.ethz.ch/ssh2/LICENSE.txt&#xA;&#xA;IT IS YOUR OBLIGATION TO READ AND ACCEPT ALL SUCH TERMS&#xA;AND CONDITIONS PRIOR TO USE OF THIS CONTENT.&#xA;'/>
        <property name='df_LT.description' value='Subversion Client Adapter implementation using the pure Java SVNKit library.'/>
      </properties>
      <provides size='2'>
        <provided namespace='org.eclipse.equinox.p2.iu' name='org.tigris.subversion.clientadapter.svnkit.feature.feature.group' version='*******'/>
        <provided namespace='org.eclipse.equinox.p2.localization' name='df_LT' version='1.0.0'/>
      </provides>
      <requires size='5'>
        <required namespace='org.eclipse.equinox.p2.iu' name='org.eclipse.core.runtime' range='0.0.0'/>
        <required namespace='org.eclipse.equinox.p2.iu' name='org.tigris.subversion.clientadapter' range='[1.8.5,1.9.0)'/>
        <required namespace='org.eclipse.equinox.p2.iu' name='org.tmatesoft.svnkit' range='[1.7.0,1.8.0)'/>
        <required namespace='org.eclipse.equinox.p2.iu' name='org.tigris.subversion.clientadapter.svnkit' range='[*******,*******]'/>
        <required namespace='org.eclipse.equinox.p2.iu' name='org.tigris.subversion.clientadapter.svnkit.feature.feature.jar' range='[*******,*******]'>
          <filter>
            (org.eclipse.update.install.features=true)
          </filter>
        </required>
      </requires>
      <touchpoint id='null' version='0.0.0'/>
      <licenses size='1'>
        <license uri='%25licenseURL' url='%25licenseURL'>
          %license
        </license>
      </licenses>
      <copyright uri='http://subclipse.tigris.org/' url='http://subclipse.tigris.org/'>
        http://subclipse.tigris.org/
      </copyright>
    </unit>
    <unit id='com.trilead.ssh2' version='1.0.0.build216_r152_v20130304_1651' singleton='false'>
      <update id='com.trilead.ssh2' range='[0.0.0,1.0.0.build216_r152_v20130304_1651)' severity='0'/>
      <properties size='1'>
        <property name='org.eclipse.equinox.p2.partial.iu' value='true'/>
      </properties>
      <provides size='3'>
        <provided namespace='org.eclipse.equinox.p2.iu' name='com.trilead.ssh2' version='1.0.0.build216_r152_v20130304_1651'/>
        <provided namespace='osgi.bundle' name='com.trilead.ssh2' version='1.0.0.build216_r152_v20130304_1651'/>
        <provided namespace='org.eclipse.equinox.p2.eclipse.type' name='bundle' version='1.0.0'/>
      </provides>
      <artifacts size='1'>
        <artifact classifier='osgi.bundle' id='com.trilead.ssh2' version='1.0.0.build216_r152_v20130304_1651'/>
      </artifacts>
      <touchpoint id='org.eclipse.equinox.p2.osgi' version='1.0.0'/>
      <touchpointData size='1'>
        <instructions size='1'>
          <instruction key='manifest'>
            Bundle-SymbolicName: com.trilead.ssh2&#xA;Bundle-Version: 1.0.0.build216_r152_v20130304_1651&#xA;
          </instruction>
        </instructions>
      </touchpointData>
    </unit>
    <unit id='org.tigris.subversion.clientadapter.javahl' version='1.7.10' singleton='false'>
      <update id='org.tigris.subversion.clientadapter.javahl' range='[0.0.0,1.7.10)' severity='0'/>
      <properties size='1'>
        <property name='org.eclipse.equinox.p2.partial.iu' value='true'/>
      </properties>
      <provides size='3'>
        <provided namespace='org.eclipse.equinox.p2.iu' name='org.tigris.subversion.clientadapter.javahl' version='1.7.10'/>
        <provided namespace='osgi.bundle' name='org.tigris.subversion.clientadapter.javahl' version='1.7.10'/>
        <provided namespace='org.eclipse.equinox.p2.eclipse.type' name='bundle' version='1.0.0'/>
      </provides>
      <artifacts size='1'>
        <artifact classifier='osgi.bundle' id='org.tigris.subversion.clientadapter.javahl' version='1.7.10'/>
      </artifacts>
      <touchpoint id='org.eclipse.equinox.p2.osgi' version='1.0.0'/>
      <touchpointData size='1'>
        <instructions size='1'>
          <instruction key='manifest'>
            Bundle-SymbolicName: org.tigris.subversion.clientadapter.javahl&#xA;Bundle-Version: 1.7.10&#xA;
          </instruction>
        </instructions>
      </touchpointData>
    </unit>
    <unit id='org.tmatesoft.svnkit' version='1.7.9.r9659_v20130411_2103' singleton='false'>
      <update id='org.tmatesoft.svnkit' range='[0.0.0,1.7.9.r9659_v20130411_2103)' severity='0'/>
      <properties size='1'>
        <property name='org.eclipse.equinox.p2.partial.iu' value='true'/>
      </properties>
      <provides size='3'>
        <provided namespace='org.eclipse.equinox.p2.iu' name='org.tmatesoft.svnkit' version='1.7.9.r9659_v20130411_2103'/>
        <provided namespace='osgi.bundle' name='org.tmatesoft.svnkit' version='1.7.9.r9659_v20130411_2103'/>
        <provided namespace='org.eclipse.equinox.p2.eclipse.type' name='bundle' version='1.0.0'/>
      </provides>
      <artifacts size='1'>
        <artifact classifier='osgi.bundle' id='org.tmatesoft.svnkit' version='1.7.9.r9659_v20130411_2103'/>
      </artifacts>
      <touchpoint id='org.eclipse.equinox.p2.osgi' version='1.0.0'/>
      <touchpointData size='1'>
        <instructions size='1'>
          <instruction key='manifest'>
            Bundle-SymbolicName: org.tmatesoft.svnkit&#xA;Bundle-Version: 1.7.9.r9659_v20130411_2103&#xA;
          </instruction>
        </instructions>
      </touchpointData>
    </unit>
    <unit id='org.tigris.subversion.subclipse.mylyn.feature.jar' version='3.0.0'>
      <properties size='6'>
        <property name='org.eclipse.equinox.p2.name' value='Subclipse Integration for Mylyn 3.x (Optional)'/>
        <property name='org.eclipse.equinox.p2.description' value='%description'/>
        <property name='org.eclipse.equinox.p2.description.url' value='%changesURL'/>
        <property name='org.eclipse.equinox.p2.provider' value='tigris.org'/>
        <property name='df_LT.license' value='Subclipse Software User Agreement&#xA;11th April, 2006&#xA;&#xA;Subclipse is licensed under the terms of the Eclipse Public&#xA;License v1.0.  http://www.eclipse.org/legal/epl-v10.html&#xA;&#xA;Applicable Licenses&#xA;&#xA;Subclipse is built upon a number of other open source&#xA;technologies and products.  Here is a list of those products&#xA;with links to their licenses.&#xA;&#xA;svnClientAdapter:  Part of the overall Subclipse project,&#xA;svnClientAdapter presents a plugable high-level interface&#xA;to the Subversion repository.  svnClientAdapter is licensed&#xA;under the Eclipse Public License v1.0.&#xA;http://www.eclipse.org/legal/epl-v10.html&#xA;&#xA;Depending on the adapter you choose in your preferences, the&#xA;following products and licenses are involved.&#xA;&#xA;Subversion/JavaHL:  JavaHL is a high-level Java language binding&#xA;to the Subversion &quot;C&quot; libraries and is part of the official&#xA;Subversion source distribution.  Subversion is licensed under&#xA;the CollabNet license.&#xA;http://subversion.tigris.org/project_license.html&#xA;&#xA;SVNKit:  SVNKit is a &quot;pure Java&quot; implementation of the&#xA;Subversion network protocols and working copy formats.&#xA;SVNKit is licensed under the TMate license.&#xA;http://svnkit.com/licensing/index.html&#xA;&#xA;Ganymed SSH-2:  SVNKit uses the Ganymed SSH-2 library to support&#xA;the svn+ssh:// protocol.  Ganymed SSH-2 is licensed under the&#xA;Ganymed license.&#xA;http://www.ganymed.ethz.ch/ssh2/LICENSE.txt&#xA;&#xA;IT IS YOUR OBLIGATION TO READ AND ACCEPT ALL SUCH TERMS&#xA;AND CONDITIONS PRIOR TO USE OF THIS CONTENT.&#xA;'/>
        <property name='df_LT.description' value='Provides integration features between Subclipse and Mylyn,such as automatic change set management and linking projects to issue tracking systemsbased on svn properties.'/>
      </properties>
      <provides size='3'>
        <provided namespace='org.eclipse.equinox.p2.iu' name='org.tigris.subversion.subclipse.mylyn.feature.jar' version='3.0.0'/>
        <provided namespace='org.eclipse.equinox.p2.eclipse.type' name='feature' version='1.0.0'/>
        <provided namespace='org.eclipse.update.feature' name='org.tigris.subversion.subclipse.mylyn' version='3.0.0'/>
      </provides>
      <filter>
        (org.eclipse.update.install.features=true)
      </filter>
      <artifacts size='1'>
        <artifact classifier='org.eclipse.update.feature' id='org.tigris.subversion.subclipse.mylyn' version='3.0.0'/>
      </artifacts>
      <touchpoint id='org.eclipse.equinox.p2.osgi' version='1.0.0'/>
      <touchpointData size='1'>
        <instructions size='1'>
          <instruction key='zipped'>
            true
          </instruction>
        </instructions>
      </touchpointData>
      <licenses size='1'>
        <license uri='%25licenseURL' url='%25licenseURL'>
          %license
        </license>
      </licenses>
      <copyright uri='http://subclipse.tigris.org/' url='http://subclipse.tigris.org/'>
        http://subclipse.tigris.org/
      </copyright>
    </unit>
    <unit id='org.tigris.subversion.clientadapter.svnkit.feature.feature.jar' version='*******'>
      <properties size='6'>
        <property name='org.eclipse.equinox.p2.name' value='SVNKit Client Adapter (Not required)'/>
        <property name='org.eclipse.equinox.p2.description' value='%description'/>
        <property name='org.eclipse.equinox.p2.provider' value='tigris.org'/>
        <property name='org.eclipse.update.feature.plugin' value='org.tigris.subversion.clientadapter.svnkit'/>
        <property name='df_LT.license' value='Subclipse Software User Agreement&#xA;11th April, 2006&#xA;&#xA;Subclipse is licensed under the terms of the Eclipse Public&#xA;License v1.0.  http://www.eclipse.org/legal/epl-v10.html&#xA;&#xA;Applicable Licenses&#xA;&#xA;Subclipse is built upon a number of other open source&#xA;technologies and products.  Here is a list of those products&#xA;with links to their licenses.&#xA;&#xA;svnClientAdapter:  Part of the overall Subclipse project,&#xA;svnClientAdapter presents a pluggable high-level interface&#xA;to the Subversion repository.  svnClientAdapter is licensed&#xA;under the Apache2 License.&#xA;http://www.apache.org/licenses/LICENSE-2.0&#xA;&#xA;Depending on the adapter you choose in your preferences, the&#xA;following products and licenses are involved.&#xA;&#xA;Subversion/JavaHL:  JavaHL is a high-level Java language binding&#xA;to the Subversion &quot;C&quot; libraries and is part of the official&#xA;Subversion source distribution.  Subversion is licensed under&#xA;the CollabNet license.&#xA;http://subversion.tigris.org/project_license.html&#xA;&#xA;SVNKit:  SVNKit is a &quot;pure Java&quot; implementation of the&#xA;Subversion network protocols and working copy formats.&#xA;SVNKit is licensed under the TMate license.&#xA;http://svnkit.com/licensing/index.html&#xA;&#xA;Ganymed SSH-2:  SVNKit uses the Ganymed SSH-2 library to support&#xA;the svn+ssh:// protocol.  Ganymed SSH-2 is licensed under the&#xA;Ganymed license.&#xA;http://www.ganymed.ethz.ch/ssh2/LICENSE.txt&#xA;&#xA;IT IS YOUR OBLIGATION TO READ AND ACCEPT ALL SUCH TERMS&#xA;AND CONDITIONS PRIOR TO USE OF THIS CONTENT.&#xA;'/>
        <property name='df_LT.description' value='Subversion Client Adapter implementation using the pure Java SVNKit library.'/>
      </properties>
      <provides size='3'>
        <provided namespace='org.eclipse.equinox.p2.iu' name='org.tigris.subversion.clientadapter.svnkit.feature.feature.jar' version='*******'/>
        <provided namespace='org.eclipse.equinox.p2.eclipse.type' name='feature' version='1.0.0'/>
        <provided namespace='org.eclipse.update.feature' name='org.tigris.subversion.clientadapter.svnkit.feature' version='*******'/>
      </provides>
      <filter>
        (org.eclipse.update.install.features=true)
      </filter>
      <artifacts size='1'>
        <artifact classifier='org.eclipse.update.feature' id='org.tigris.subversion.clientadapter.svnkit.feature' version='*******'/>
      </artifacts>
      <touchpoint id='org.eclipse.equinox.p2.osgi' version='1.0.0'/>
      <touchpointData size='1'>
        <instructions size='1'>
          <instruction key='zipped'>
            true
          </instruction>
        </instructions>
      </touchpointData>
      <licenses size='1'>
        <license uri='%25licenseURL' url='%25licenseURL'>
          %license
        </license>
      </licenses>
      <copyright uri='http://subclipse.tigris.org/' url='http://subclipse.tigris.org/'>
        http://subclipse.tigris.org/
      </copyright>
    </unit>
    <unit id='org.tmatesoft.svnkit.feature.jar' version='1.7.9.r9659_v20130411_2103'>
      <properties size='6'>
        <property name='org.eclipse.equinox.p2.name' value='SVNKit Library'/>
        <property name='org.eclipse.equinox.p2.description' value='%description'/>
        <property name='org.eclipse.equinox.p2.description.url' value='http://svnkit.com/'/>
        <property name='org.eclipse.equinox.p2.provider' value='TMate Software'/>
        <property name='df_LT.license' value='The TMate Open Source License&#xA;&#xA;This license applies to all portions of TMate SVNKit library, which&#xA;are not externally-maintained libraries (e.g. Trilead SSH library).&#xA;&#xA;All the source code and compiled classes in package org.tigris.subversion.javahl&#xA;except SvnClient class are covered by the license in JAVAHL-LICENSE file&#xA;&#xA;Copyright (c) 2004-2011 TMate Software. All rights reserved.&#xA;&#xA;Redistribution and use in source and binary forms, with or without modification,&#xA;are permitted provided that the following conditions are met:&#xA;&#xA;* Redistributions of source code must retain the above copyright notice,&#xA;this list of conditions and the following disclaimer.&#xA;&#xA;* Redistributions in binary form must reproduce the above copyright notice,&#xA;this list of conditions and the following disclaimer in the documentation &#xA;and/or other materials provided with the distribution.&#xA;&#xA;* Redistributions in any form must be accompanied by information on how to&#xA;obtain complete source code for the software that uses SVNKit and any &#xA;accompanying software that uses the software that uses SVNKit. The source&#xA;code must either be included in the distribution or be available for no &#xA;more than the cost of distribution plus a nominal fee, and must be freely &#xA;redistributable under reasonable conditions. For an executable file, complete&#xA;source code means the source code for all modules it contains. It does not &#xA;include source code for modules or files that typically accompany the major &#xA;components of the operating system on which the executable file runs.&#xA;&#xA;* Redistribution in any form without redistributing source code for software&#xA;that uses SVNKit is possible only when such redistribution is explictly permitted&#xA;by TMate Software. Please, contact TMate <NAME_EMAIL> to &#xA;get such permission.&#xA;&#xA;THIS SOFTWARE IS PROVIDED BY TMATE SOFTWARE ``AS IS&apos;&apos; AND ANY EXPRESS OR IMPLIED&#xA;WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF &#xA;MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE, OR NON-INFRINGEMENT, ARE&#xA;DISCLAIMED. &#xA;&#xA;IN NO EVENT SHALL TMATE SOFTWARE BE LIABLE FOR ANY DIRECT, INDIRECT,&#xA;INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT&#xA;LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR &#xA;PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF &#xA;LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE&#xA;OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF &#xA;ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.&#xA;&#xA;'/>
        <property name='df_LT.description' value='SVNKit is a pure java Subversion client library that may be used through its own API or serve as a transparent replacement of native javahl bindings.'/>
      </properties>
      <provides size='3'>
        <provided namespace='org.eclipse.equinox.p2.iu' name='org.tmatesoft.svnkit.feature.jar' version='1.7.9.r9659_v20130411_2103'/>
        <provided namespace='org.eclipse.equinox.p2.eclipse.type' name='feature' version='1.0.0'/>
        <provided namespace='org.eclipse.update.feature' name='org.tmatesoft.svnkit' version='1.7.9.r9659_v20130411_2103'/>
      </provides>
      <filter>
        (org.eclipse.update.install.features=true)
      </filter>
      <artifacts size='1'>
        <artifact classifier='org.eclipse.update.feature' id='org.tmatesoft.svnkit' version='1.7.9.r9659_v20130411_2103'/>
      </artifacts>
      <touchpoint id='org.eclipse.equinox.p2.osgi' version='1.0.0'/>
      <touchpointData size='1'>
        <instructions size='1'>
          <instruction key='zipped'>
            true
          </instruction>
        </instructions>
      </touchpointData>
      <licenses size='1'>
        <license uri='' url=''>
          %license
        </license>
      </licenses>
      <copyright uri='http://svnkit.com/' url='http://svnkit.com/'>
        (c) 2004-2011, TMate Software Ltd.
      </copyright>
    </unit>
    <unit id='net.java.dev.jna' version='3.4.0.t20120117_1605' singleton='false'>
      <update id='net.java.dev.jna' range='[0.0.0,3.4.0.t20120117_1605)' severity='0'/>
      <properties size='1'>
        <property name='org.eclipse.equinox.p2.partial.iu' value='true'/>
      </properties>
      <provides size='3'>
        <provided namespace='org.eclipse.equinox.p2.iu' name='net.java.dev.jna' version='3.4.0.t20120117_1605'/>
        <provided namespace='osgi.bundle' name='net.java.dev.jna' version='3.4.0.t20120117_1605'/>
        <provided namespace='org.eclipse.equinox.p2.eclipse.type' name='bundle' version='1.0.0'/>
      </provides>
      <artifacts size='1'>
        <artifact classifier='osgi.bundle' id='net.java.dev.jna' version='3.4.0.t20120117_1605'/>
      </artifacts>
      <touchpoint id='org.eclipse.equinox.p2.osgi' version='1.0.0'/>
      <touchpointData size='1'>
        <instructions size='1'>
          <instruction key='manifest'>
            Bundle-SymbolicName: net.java.dev.jna&#xA;Bundle-Version: 3.4.0.t20120117_1605&#xA;
          </instruction>
        </instructions>
      </touchpointData>
    </unit>
    <unit id='org.tigris.subversion.subclipse.doc' version='1.3.0' singleton='false'>
      <update id='org.tigris.subversion.subclipse.doc' range='[0.0.0,1.3.0)' severity='0'/>
      <properties size='1'>
        <property name='org.eclipse.equinox.p2.partial.iu' value='true'/>
      </properties>
      <provides size='3'>
        <provided namespace='org.eclipse.equinox.p2.iu' name='org.tigris.subversion.subclipse.doc' version='1.3.0'/>
        <provided namespace='osgi.bundle' name='org.tigris.subversion.subclipse.doc' version='1.3.0'/>
        <provided namespace='org.eclipse.equinox.p2.eclipse.type' name='bundle' version='1.0.0'/>
      </provides>
      <artifacts size='1'>
        <artifact classifier='osgi.bundle' id='org.tigris.subversion.subclipse.doc' version='1.3.0'/>
      </artifacts>
      <touchpoint id='org.eclipse.equinox.p2.osgi' version='1.0.0'/>
      <touchpointData size='1'>
        <instructions size='1'>
          <instruction key='manifest'>
            Bundle-SymbolicName: org.tigris.subversion.subclipse.doc&#xA;Bundle-Version: 1.3.0&#xA;
          </instruction>
        </instructions>
      </touchpointData>
    </unit>
  </units>
</repository>
