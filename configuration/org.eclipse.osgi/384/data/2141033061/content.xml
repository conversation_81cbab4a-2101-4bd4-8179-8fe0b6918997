<?xml version='1.0' encoding='UTF-8'?>
<?metadataRepository version='1.1.0'?>
<repository name='update site: http://download.eclipse.org/jetty/updates/jetty-wtp/*******01107011148' type='org.eclipse.equinox.internal.p2.metadata.repository.LocalMetadataRepository' version='1'>
  <properties size='2'>
    <property name='p2.timestamp' value='1524186110185'/>
    <property name='site.checksum' value='3100951603'/>
  </properties>
  <units size='9'>
    <unit id='org.eclipse.jst.server.jetty.feature.feature.group' version='*******01107011148' singleton='false'>
      <update id='org.eclipse.jst.server.jetty.feature.feature.group' range='[0.0.0,*******01107011148)' severity='0'/>
      <properties size='10'>
        <property name='org.eclipse.equinox.p2.name' value='%featureName'/>
        <property name='org.eclipse.equinox.p2.description' value='%description'/>
        <property name='org.eclipse.equinox.p2.description.url' value='%descriptionURL'/>
        <property name='org.eclipse.equinox.p2.provider' value='%providerName'/>
        <property name='org.eclipse.equinox.p2.type.group' value='true'/>
        <property name='df_LT.license' value='Eclipse Foundation Software User Agreement&#xA;April 14, 2010&#xA;&#xA;Usage Of Content&#xA;&#xA;THE ECLIPSE FOUNDATION MAKES AVAILABLE SOFTWARE, DOCUMENTATION, INFORMATION AND/OR&#xA;OTHER MATERIALS FOR OPEN SOURCE PROJECTS (COLLECTIVELY &quot;CONTENT&quot;).&#xA;USE OF THE CONTENT IS GOVERNED BY THE TERMS AND CONDITIONS OF THIS&#xA;AGREEMENT AND/OR THE TERMS AND CONDITIONS OF LICENSE AGREEMENTS OR&#xA;NOTICES INDICATED OR REFERENCED BELOW.  BY USING THE CONTENT, YOU&#xA;AGREE THAT YOUR USE OF THE CONTENT IS GOVERNED BY THIS AGREEMENT&#xA;AND/OR THE TERMS AND CONDITIONS OF ANY APPLICABLE LICENSE AGREEMENTS&#xA;OR NOTICES INDICATED OR REFERENCED BELOW.  IF YOU DO NOT AGREE TO THE&#xA;TERMS AND CONDITIONS OF THIS AGREEMENT AND THE TERMS AND CONDITIONS&#xA;OF ANY APPLICABLE LICENSE AGREEMENTS OR NOTICES INDICATED OR REFERENCED&#xA;BELOW, THEN YOU MAY NOT USE THE CONTENT.&#xA;&#xA;Applicable Licenses&#xA;&#xA;Unless otherwise indicated, all Content made available by the&#xA;Eclipse Foundation is provided to you under the terms and conditions of&#xA;the Eclipse Public License Version 1.0 (&quot;EPL&quot;). A copy of the EPL is&#xA;provided with this Content and is also available at http://www.eclipse.org/legal/epl-v10.html.&#xA;For purposes of the EPL, &quot;Program&quot; will mean the Content.&#xA;&#xA;Content includes, but is not limited to, source code, object code,&#xA;documentation and other files maintained in the Eclipse Foundation source code&#xA;repository (&quot;Repository&quot;) in software modules (&quot;Modules&quot;) and made available&#xA;as downloadable archives (&quot;Downloads&quot;).&#xA;&#xA;- Content may be structured and packaged into modules to facilitate delivering,&#xA;extending, and upgrading the Content. Typical modules may include plug-ins (&quot;Plug-ins&quot;),&#xA;plug-in fragments (&quot;Fragments&quot;), and features (&quot;Features&quot;).&#xA;- Each Plug-in or Fragment may be packaged as a sub-directory or JAR (Java(TM) ARchive)&#xA;in a directory named &quot;plugins&quot;.&#xA;- A Feature is a bundle of one or more Plug-ins and/or Fragments and associated material.&#xA;Each Feature may be packaged as a sub-directory in a directory named &quot;features&quot;.&#xA;Within a Feature, files named &quot;feature.xml&quot; may contain a list of the names and version&#xA;numbers of the Plug-ins and/or Fragments associated with that Feature.&#xA;- Features may also include other Features (&quot;Included Features&quot;). Within a Feature, files&#xA;named &quot;feature.xml&quot; may contain a list of the names and version numbers of Included Features.&#xA;&#xA;The terms and conditions governing Plug-ins and Fragments should be&#xA;contained in files named &quot;about.html&quot; (&quot;Abouts&quot;). The terms and&#xA;conditions governing Features and Included Features should be contained&#xA;in files named &quot;license.html&quot; (&quot;Feature Licenses&quot;). Abouts and Feature&#xA;Licenses may be located in any directory of a Download or Module&#xA;including, but not limited to the following locations:&#xA;&#xA;- The top-level (root) directory&#xA;- Plug-in and Fragment directories&#xA;- Inside Plug-ins and Fragments packaged as JARs&#xA;- Sub-directories of the directory named &quot;src&quot; of certain Plug-ins&#xA;- Feature directories&#xA;&#xA;Note: if a Feature made available by the Eclipse Foundation is installed using the&#xA;Provisioning Technology (as defined below), you must agree to a license (&quot;Feature &#xA;Update License&quot;) during the installation process. If the Feature contains&#xA;Included Features, the Feature Update License should either provide you&#xA;with the terms and conditions governing the Included Features or inform&#xA;you where you can locate them. Feature Update Licenses may be found in&#xA;the &quot;license&quot; property of files named &quot;feature.properties&quot; found within a Feature.&#xA;Such Abouts, Feature Licenses, and Feature Update Licenses contain the&#xA;terms and conditions (or references to such terms and conditions) that&#xA;govern your use of the associated Content in that directory.&#xA;&#xA;THE ABOUTS, FEATURE LICENSES, AND FEATURE UPDATE LICENSES MAY REFER&#xA;TO THE EPL OR OTHER LICENSE AGREEMENTS, NOTICES OR TERMS AND CONDITIONS.&#xA;SOME OF THESE OTHER LICENSE AGREEMENTS MAY INCLUDE (BUT ARE NOT LIMITED TO):&#xA;&#xA;- Common Public License Version 1.0 (available at http://www.eclipse.org/legal/cpl-v10.html)&#xA;- Apache Software License 1.1 (available at http://www.apache.org/licenses/LICENSE)&#xA;- Apache Software License 2.0 (available at http://www.apache.org/licenses/LICENSE-2.0)&#xA;- Metro Link Public License 1.00 (available at http://www.opengroup.org/openmotif/supporters/metrolink/license.html)&#xA;- Mozilla Public License Version 1.1 (available at http://www.mozilla.org/MPL/MPL-1.1.html)&#xA;&#xA;IT IS YOUR OBLIGATION TO READ AND ACCEPT ALL SUCH TERMS AND CONDITIONS PRIOR&#xA;TO USE OF THE CONTENT. If no About, Feature License, or Feature Update License&#xA;is provided, please contact the Eclipse Foundation to determine what terms and conditions&#xA;govern that particular Content.&#xA;&#xA;&#xA;Use of Provisioning Technology&#xA;&#xA;The Eclipse Foundation makes available provisioning software, examples of which include,&#xA;but are not limited to, p2 and the Eclipse Update Manager (&quot;Provisioning Technology&quot;) for&#xA;the purpose of allowing users to install software, documentation, information and/or&#xA;other materials (collectively &quot;Installable Software&quot;). This capability is provided with&#xA;the intent of allowing such users to install, extend and update Eclipse-based products.&#xA;Information about packaging Installable Software is available at&#xA;http://eclipse.org/equinox/p2/repository_packaging.html (&quot;Specification&quot;).&#xA;&#xA;You may use Provisioning Technology to allow other parties to install Installable Software.&#xA;You shall be responsible for enabling the applicable license agreements relating to the&#xA;Installable Software to be presented to, and accepted by, the users of the Provisioning Technology&#xA;in accordance with the Specification. By using Provisioning Technology in such a manner and&#xA;making it available in accordance with the Specification, you further acknowledge your&#xA;agreement to, and the acquisition of all necessary rights to permit the following:&#xA;&#xA;1. A series of actions may occur (&quot;Provisioning Process&quot;) in which a user may execute&#xA;the Provisioning Technology on a machine (&quot;Target Machine&quot;) with the intent of installing,&#xA;extending or updating the functionality of an Eclipse-based product.&#xA;2. During the Provisioning Process, the Provisioning Technology may cause third party&#xA;Installable Software or a portion thereof to be accessed and copied to the Target Machine.&#xA;3. Pursuant to the Specification, you will provide to the user the terms and conditions that&#xA;govern the use of the Installable Software (&quot;Installable Software Agreement&quot;) and such&#xA;Installable Software Agreement shall be accessed from the Target Machine in accordance&#xA;with the Specification. Such Installable Software Agreement must inform the user of the&#xA;terms and conditions that govern the Installable Software and must solicit acceptance by&#xA;the end user in the manner prescribed in such Installable Software Agreement. Upon such&#xA;indication of agreement by the user, the provisioning Technology will complete installation&#xA;of the Installable Software.&#xA;&#xA;Cryptography&#xA;&#xA;Content may contain encryption software. The country in which you are&#xA;currently may have restrictions on the import, possession, and use,&#xA;and/or re-export to another country, of encryption software. BEFORE&#xA;using any encryption software, please check the country&apos;s laws,&#xA;regulations and policies concerning the import, possession, or use, and&#xA;re-export of encryption software, to see if this is permitted.&#xA;&#xA;Java and all Java-based trademarks are trademarks of Oracle Corporation in the United States, other countries, or both.&#xA;'/>
        <property name='df_LT.copyright' value='Copyright (c) 2004-2010 Mort Bay Consulting Pty. Ltd. and others.&#xA;'/>
        <property name='df_LT.featureName' value='Jetty WTP Adaptor'/>
        <property name='df_LT.description' value='Jetty WTP Adaptor allows you to develop applications with jetty with in eclipse.'/>
        <property name='df_LT.providerName' value='Eclipse.org - Jetty'/>
      </properties>
      <provides size='2'>
        <provided namespace='org.eclipse.equinox.p2.iu' name='org.eclipse.jst.server.jetty.feature.feature.group' version='*******01107011148'/>
        <provided namespace='org.eclipse.equinox.p2.localization' name='df_LT' version='1.0.0'/>
      </provides>
      <requires size='7'>
        <required namespace='org.eclipse.equinox.p2.iu' name='org.eclipse.jst.server.jetty.core' range='[*******01107011148,*******01107011148]'/>
        <required namespace='org.eclipse.equinox.p2.iu' name='org.eclipse.jst.server.jetty.core.source' range='[*******01107011148,*******01107011148]'/>
        <required namespace='org.eclipse.equinox.p2.iu' name='org.eclipse.jst.server.jetty.ui' range='[*******01107011148,*******01107011148]'/>
        <required namespace='org.eclipse.equinox.p2.iu' name='org.eclipse.jst.server.jetty.ui.source' range='[*******01107011148,*******01107011148]'/>
        <required namespace='org.eclipse.equinox.p2.iu' name='org.eclipse.jst.server.jetty.ui.websocket' range='[*******01107011148,*******01107011148]'/>
        <required namespace='org.eclipse.equinox.p2.iu' name='org.eclipse.jst.server.jetty.ui.websocket.source' range='[*******01107011148,*******01107011148]'/>
        <required namespace='org.eclipse.equinox.p2.iu' name='org.eclipse.jst.server.jetty.feature.feature.jar' range='[*******01107011148,*******01107011148]'>
          <filter>
            (org.eclipse.update.install.features=true)
          </filter>
        </required>
      </requires>
      <touchpoint id='null' version='0.0.0'/>
      <licenses size='1'>
        <license uri='%25licenseURL' url='%25licenseURL'>
          %license
        </license>
      </licenses>
      <copyright uri='%25copyrightURL' url='%25copyrightURL'>
        %copyright
      </copyright>
    </unit>
    <unit id='org.eclipse.jst.server.jetty.core' version='*******01107011148' singleton='false'>
      <update id='org.eclipse.jst.server.jetty.core' range='[0.0.0,*******01107011148)' severity='0'/>
      <properties size='1'>
        <property name='org.eclipse.equinox.p2.partial.iu' value='true'/>
      </properties>
      <provides size='3'>
        <provided namespace='org.eclipse.equinox.p2.iu' name='org.eclipse.jst.server.jetty.core' version='*******01107011148'/>
        <provided namespace='osgi.bundle' name='org.eclipse.jst.server.jetty.core' version='*******01107011148'/>
        <provided namespace='org.eclipse.equinox.p2.eclipse.type' name='bundle' version='1.0.0'/>
      </provides>
      <artifacts size='1'>
        <artifact classifier='osgi.bundle' id='org.eclipse.jst.server.jetty.core' version='*******01107011148'/>
      </artifacts>
      <touchpoint id='org.eclipse.equinox.p2.osgi' version='1.0.0'/>
      <touchpointData size='1'>
        <instructions size='1'>
          <instruction key='manifest'>
            Bundle-SymbolicName: org.eclipse.jst.server.jetty.core&#xA;Bundle-Version: *******01107011148&#xA;
          </instruction>
        </instructions>
      </touchpointData>
    </unit>
    <unit id='org.eclipse.jst.server.jetty.core.source' version='*******01107011148' singleton='false'>
      <update id='org.eclipse.jst.server.jetty.core.source' range='[0.0.0,*******01107011148)' severity='0'/>
      <properties size='1'>
        <property name='org.eclipse.equinox.p2.partial.iu' value='true'/>
      </properties>
      <provides size='3'>
        <provided namespace='org.eclipse.equinox.p2.iu' name='org.eclipse.jst.server.jetty.core.source' version='*******01107011148'/>
        <provided namespace='osgi.bundle' name='org.eclipse.jst.server.jetty.core.source' version='*******01107011148'/>
        <provided namespace='org.eclipse.equinox.p2.eclipse.type' name='bundle' version='1.0.0'/>
      </provides>
      <artifacts size='1'>
        <artifact classifier='osgi.bundle' id='org.eclipse.jst.server.jetty.core.source' version='*******01107011148'/>
      </artifacts>
      <touchpoint id='org.eclipse.equinox.p2.osgi' version='1.0.0'/>
      <touchpointData size='1'>
        <instructions size='1'>
          <instruction key='manifest'>
            Bundle-SymbolicName: org.eclipse.jst.server.jetty.core.source&#xA;Bundle-Version: *******01107011148&#xA;
          </instruction>
        </instructions>
      </touchpointData>
    </unit>
    <unit id='http://download.eclipse.org/jetty/updates/jetty-wtp/*******01107011148/site.xml.org.eclipse.jst.server.jetty.site' version='*******--cLTB8V773553G3555AI'>
      <properties size='2'>
        <property name='org.eclipse.equinox.p2.name' value='Jetty WTP Adaptor Site'/>
        <property name='org.eclipse.equinox.p2.type.category' value='true'/>
      </properties>
      <provides size='1'>
        <provided namespace='org.eclipse.equinox.p2.iu' name='http://download.eclipse.org/jetty/updates/jetty-wtp/*******01107011148/site.xml.org.eclipse.jst.server.jetty.site' version='*******--cLTB8V773553G3555AI'/>
      </provides>
      <requires size='1'>
        <required namespace='org.eclipse.equinox.p2.iu' name='org.eclipse.jst.server.jetty.feature.feature.group' range='[*******01107011148,*******01107011148]'/>
      </requires>
      <touchpoint id='null' version='0.0.0'/>
    </unit>
    <unit id='org.eclipse.jst.server.jetty.ui' version='*******01107011148' singleton='false'>
      <update id='org.eclipse.jst.server.jetty.ui' range='[0.0.0,*******01107011148)' severity='0'/>
      <properties size='1'>
        <property name='org.eclipse.equinox.p2.partial.iu' value='true'/>
      </properties>
      <provides size='3'>
        <provided namespace='org.eclipse.equinox.p2.iu' name='org.eclipse.jst.server.jetty.ui' version='*******01107011148'/>
        <provided namespace='osgi.bundle' name='org.eclipse.jst.server.jetty.ui' version='*******01107011148'/>
        <provided namespace='org.eclipse.equinox.p2.eclipse.type' name='bundle' version='1.0.0'/>
      </provides>
      <artifacts size='1'>
        <artifact classifier='osgi.bundle' id='org.eclipse.jst.server.jetty.ui' version='*******01107011148'/>
      </artifacts>
      <touchpoint id='org.eclipse.equinox.p2.osgi' version='1.0.0'/>
      <touchpointData size='1'>
        <instructions size='1'>
          <instruction key='manifest'>
            Bundle-SymbolicName: org.eclipse.jst.server.jetty.ui&#xA;Bundle-Version: *******01107011148&#xA;
          </instruction>
        </instructions>
      </touchpointData>
    </unit>
    <unit id='org.eclipse.jst.server.jetty.feature.feature.jar' version='*******01107011148'>
      <properties size='9'>
        <property name='org.eclipse.equinox.p2.name' value='%featureName'/>
        <property name='org.eclipse.equinox.p2.description' value='%description'/>
        <property name='org.eclipse.equinox.p2.description.url' value='%descriptionURL'/>
        <property name='org.eclipse.equinox.p2.provider' value='%providerName'/>
        <property name='df_LT.license' value='Eclipse Foundation Software User Agreement&#xA;April 14, 2010&#xA;&#xA;Usage Of Content&#xA;&#xA;THE ECLIPSE FOUNDATION MAKES AVAILABLE SOFTWARE, DOCUMENTATION, INFORMATION AND/OR&#xA;OTHER MATERIALS FOR OPEN SOURCE PROJECTS (COLLECTIVELY &quot;CONTENT&quot;).&#xA;USE OF THE CONTENT IS GOVERNED BY THE TERMS AND CONDITIONS OF THIS&#xA;AGREEMENT AND/OR THE TERMS AND CONDITIONS OF LICENSE AGREEMENTS OR&#xA;NOTICES INDICATED OR REFERENCED BELOW.  BY USING THE CONTENT, YOU&#xA;AGREE THAT YOUR USE OF THE CONTENT IS GOVERNED BY THIS AGREEMENT&#xA;AND/OR THE TERMS AND CONDITIONS OF ANY APPLICABLE LICENSE AGREEMENTS&#xA;OR NOTICES INDICATED OR REFERENCED BELOW.  IF YOU DO NOT AGREE TO THE&#xA;TERMS AND CONDITIONS OF THIS AGREEMENT AND THE TERMS AND CONDITIONS&#xA;OF ANY APPLICABLE LICENSE AGREEMENTS OR NOTICES INDICATED OR REFERENCED&#xA;BELOW, THEN YOU MAY NOT USE THE CONTENT.&#xA;&#xA;Applicable Licenses&#xA;&#xA;Unless otherwise indicated, all Content made available by the&#xA;Eclipse Foundation is provided to you under the terms and conditions of&#xA;the Eclipse Public License Version 1.0 (&quot;EPL&quot;). A copy of the EPL is&#xA;provided with this Content and is also available at http://www.eclipse.org/legal/epl-v10.html.&#xA;For purposes of the EPL, &quot;Program&quot; will mean the Content.&#xA;&#xA;Content includes, but is not limited to, source code, object code,&#xA;documentation and other files maintained in the Eclipse Foundation source code&#xA;repository (&quot;Repository&quot;) in software modules (&quot;Modules&quot;) and made available&#xA;as downloadable archives (&quot;Downloads&quot;).&#xA;&#xA;- Content may be structured and packaged into modules to facilitate delivering,&#xA;extending, and upgrading the Content. Typical modules may include plug-ins (&quot;Plug-ins&quot;),&#xA;plug-in fragments (&quot;Fragments&quot;), and features (&quot;Features&quot;).&#xA;- Each Plug-in or Fragment may be packaged as a sub-directory or JAR (Java(TM) ARchive)&#xA;in a directory named &quot;plugins&quot;.&#xA;- A Feature is a bundle of one or more Plug-ins and/or Fragments and associated material.&#xA;Each Feature may be packaged as a sub-directory in a directory named &quot;features&quot;.&#xA;Within a Feature, files named &quot;feature.xml&quot; may contain a list of the names and version&#xA;numbers of the Plug-ins and/or Fragments associated with that Feature.&#xA;- Features may also include other Features (&quot;Included Features&quot;). Within a Feature, files&#xA;named &quot;feature.xml&quot; may contain a list of the names and version numbers of Included Features.&#xA;&#xA;The terms and conditions governing Plug-ins and Fragments should be&#xA;contained in files named &quot;about.html&quot; (&quot;Abouts&quot;). The terms and&#xA;conditions governing Features and Included Features should be contained&#xA;in files named &quot;license.html&quot; (&quot;Feature Licenses&quot;). Abouts and Feature&#xA;Licenses may be located in any directory of a Download or Module&#xA;including, but not limited to the following locations:&#xA;&#xA;- The top-level (root) directory&#xA;- Plug-in and Fragment directories&#xA;- Inside Plug-ins and Fragments packaged as JARs&#xA;- Sub-directories of the directory named &quot;src&quot; of certain Plug-ins&#xA;- Feature directories&#xA;&#xA;Note: if a Feature made available by the Eclipse Foundation is installed using the&#xA;Provisioning Technology (as defined below), you must agree to a license (&quot;Feature &#xA;Update License&quot;) during the installation process. If the Feature contains&#xA;Included Features, the Feature Update License should either provide you&#xA;with the terms and conditions governing the Included Features or inform&#xA;you where you can locate them. Feature Update Licenses may be found in&#xA;the &quot;license&quot; property of files named &quot;feature.properties&quot; found within a Feature.&#xA;Such Abouts, Feature Licenses, and Feature Update Licenses contain the&#xA;terms and conditions (or references to such terms and conditions) that&#xA;govern your use of the associated Content in that directory.&#xA;&#xA;THE ABOUTS, FEATURE LICENSES, AND FEATURE UPDATE LICENSES MAY REFER&#xA;TO THE EPL OR OTHER LICENSE AGREEMENTS, NOTICES OR TERMS AND CONDITIONS.&#xA;SOME OF THESE OTHER LICENSE AGREEMENTS MAY INCLUDE (BUT ARE NOT LIMITED TO):&#xA;&#xA;- Common Public License Version 1.0 (available at http://www.eclipse.org/legal/cpl-v10.html)&#xA;- Apache Software License 1.1 (available at http://www.apache.org/licenses/LICENSE)&#xA;- Apache Software License 2.0 (available at http://www.apache.org/licenses/LICENSE-2.0)&#xA;- Metro Link Public License 1.00 (available at http://www.opengroup.org/openmotif/supporters/metrolink/license.html)&#xA;- Mozilla Public License Version 1.1 (available at http://www.mozilla.org/MPL/MPL-1.1.html)&#xA;&#xA;IT IS YOUR OBLIGATION TO READ AND ACCEPT ALL SUCH TERMS AND CONDITIONS PRIOR&#xA;TO USE OF THE CONTENT. If no About, Feature License, or Feature Update License&#xA;is provided, please contact the Eclipse Foundation to determine what terms and conditions&#xA;govern that particular Content.&#xA;&#xA;&#xA;Use of Provisioning Technology&#xA;&#xA;The Eclipse Foundation makes available provisioning software, examples of which include,&#xA;but are not limited to, p2 and the Eclipse Update Manager (&quot;Provisioning Technology&quot;) for&#xA;the purpose of allowing users to install software, documentation, information and/or&#xA;other materials (collectively &quot;Installable Software&quot;). This capability is provided with&#xA;the intent of allowing such users to install, extend and update Eclipse-based products.&#xA;Information about packaging Installable Software is available at&#xA;http://eclipse.org/equinox/p2/repository_packaging.html (&quot;Specification&quot;).&#xA;&#xA;You may use Provisioning Technology to allow other parties to install Installable Software.&#xA;You shall be responsible for enabling the applicable license agreements relating to the&#xA;Installable Software to be presented to, and accepted by, the users of the Provisioning Technology&#xA;in accordance with the Specification. By using Provisioning Technology in such a manner and&#xA;making it available in accordance with the Specification, you further acknowledge your&#xA;agreement to, and the acquisition of all necessary rights to permit the following:&#xA;&#xA;1. A series of actions may occur (&quot;Provisioning Process&quot;) in which a user may execute&#xA;the Provisioning Technology on a machine (&quot;Target Machine&quot;) with the intent of installing,&#xA;extending or updating the functionality of an Eclipse-based product.&#xA;2. During the Provisioning Process, the Provisioning Technology may cause third party&#xA;Installable Software or a portion thereof to be accessed and copied to the Target Machine.&#xA;3. Pursuant to the Specification, you will provide to the user the terms and conditions that&#xA;govern the use of the Installable Software (&quot;Installable Software Agreement&quot;) and such&#xA;Installable Software Agreement shall be accessed from the Target Machine in accordance&#xA;with the Specification. Such Installable Software Agreement must inform the user of the&#xA;terms and conditions that govern the Installable Software and must solicit acceptance by&#xA;the end user in the manner prescribed in such Installable Software Agreement. Upon such&#xA;indication of agreement by the user, the provisioning Technology will complete installation&#xA;of the Installable Software.&#xA;&#xA;Cryptography&#xA;&#xA;Content may contain encryption software. The country in which you are&#xA;currently may have restrictions on the import, possession, and use,&#xA;and/or re-export to another country, of encryption software. BEFORE&#xA;using any encryption software, please check the country&apos;s laws,&#xA;regulations and policies concerning the import, possession, or use, and&#xA;re-export of encryption software, to see if this is permitted.&#xA;&#xA;Java and all Java-based trademarks are trademarks of Oracle Corporation in the United States, other countries, or both.&#xA;'/>
        <property name='df_LT.copyright' value='Copyright (c) 2004-2010 Mort Bay Consulting Pty. Ltd. and others.&#xA;'/>
        <property name='df_LT.featureName' value='Jetty WTP Adaptor'/>
        <property name='df_LT.description' value='Jetty WTP Adaptor allows you to develop applications with jetty with in eclipse.'/>
        <property name='df_LT.providerName' value='Eclipse.org - Jetty'/>
      </properties>
      <provides size='3'>
        <provided namespace='org.eclipse.equinox.p2.iu' name='org.eclipse.jst.server.jetty.feature.feature.jar' version='*******01107011148'/>
        <provided namespace='org.eclipse.equinox.p2.eclipse.type' name='feature' version='1.0.0'/>
        <provided namespace='org.eclipse.update.feature' name='org.eclipse.jst.server.jetty.feature' version='*******01107011148'/>
      </provides>
      <filter>
        (org.eclipse.update.install.features=true)
      </filter>
      <artifacts size='1'>
        <artifact classifier='org.eclipse.update.feature' id='org.eclipse.jst.server.jetty.feature' version='*******01107011148'/>
      </artifacts>
      <touchpoint id='org.eclipse.equinox.p2.osgi' version='1.0.0'/>
      <touchpointData size='1'>
        <instructions size='1'>
          <instruction key='zipped'>
            true
          </instruction>
        </instructions>
      </touchpointData>
      <licenses size='1'>
        <license uri='%25licenseURL' url='%25licenseURL'>
          %license
        </license>
      </licenses>
      <copyright uri='%25copyrightURL' url='%25copyrightURL'>
        %copyright
      </copyright>
    </unit>
    <unit id='org.eclipse.jst.server.jetty.ui.websocket.source' version='*******01107011148' singleton='false'>
      <update id='org.eclipse.jst.server.jetty.ui.websocket.source' range='[0.0.0,*******01107011148)' severity='0'/>
      <properties size='1'>
        <property name='org.eclipse.equinox.p2.partial.iu' value='true'/>
      </properties>
      <provides size='3'>
        <provided namespace='org.eclipse.equinox.p2.iu' name='org.eclipse.jst.server.jetty.ui.websocket.source' version='*******01107011148'/>
        <provided namespace='osgi.bundle' name='org.eclipse.jst.server.jetty.ui.websocket.source' version='*******01107011148'/>
        <provided namespace='org.eclipse.equinox.p2.eclipse.type' name='bundle' version='1.0.0'/>
      </provides>
      <artifacts size='1'>
        <artifact classifier='osgi.bundle' id='org.eclipse.jst.server.jetty.ui.websocket.source' version='*******01107011148'/>
      </artifacts>
      <touchpoint id='org.eclipse.equinox.p2.osgi' version='1.0.0'/>
      <touchpointData size='1'>
        <instructions size='1'>
          <instruction key='manifest'>
            Bundle-SymbolicName: org.eclipse.jst.server.jetty.ui.websocket.source&#xA;Bundle-Version: *******01107011148&#xA;
          </instruction>
        </instructions>
      </touchpointData>
    </unit>
    <unit id='org.eclipse.jst.server.jetty.ui.source' version='*******01107011148' singleton='false'>
      <update id='org.eclipse.jst.server.jetty.ui.source' range='[0.0.0,*******01107011148)' severity='0'/>
      <properties size='1'>
        <property name='org.eclipse.equinox.p2.partial.iu' value='true'/>
      </properties>
      <provides size='3'>
        <provided namespace='org.eclipse.equinox.p2.iu' name='org.eclipse.jst.server.jetty.ui.source' version='*******01107011148'/>
        <provided namespace='osgi.bundle' name='org.eclipse.jst.server.jetty.ui.source' version='*******01107011148'/>
        <provided namespace='org.eclipse.equinox.p2.eclipse.type' name='bundle' version='1.0.0'/>
      </provides>
      <artifacts size='1'>
        <artifact classifier='osgi.bundle' id='org.eclipse.jst.server.jetty.ui.source' version='*******01107011148'/>
      </artifacts>
      <touchpoint id='org.eclipse.equinox.p2.osgi' version='1.0.0'/>
      <touchpointData size='1'>
        <instructions size='1'>
          <instruction key='manifest'>
            Bundle-SymbolicName: org.eclipse.jst.server.jetty.ui.source&#xA;Bundle-Version: *******01107011148&#xA;
          </instruction>
        </instructions>
      </touchpointData>
    </unit>
    <unit id='org.eclipse.jst.server.jetty.ui.websocket' version='*******01107011148' singleton='false'>
      <update id='org.eclipse.jst.server.jetty.ui.websocket' range='[0.0.0,*******01107011148)' severity='0'/>
      <properties size='1'>
        <property name='org.eclipse.equinox.p2.partial.iu' value='true'/>
      </properties>
      <provides size='3'>
        <provided namespace='org.eclipse.equinox.p2.iu' name='org.eclipse.jst.server.jetty.ui.websocket' version='*******01107011148'/>
        <provided namespace='osgi.bundle' name='org.eclipse.jst.server.jetty.ui.websocket' version='*******01107011148'/>
        <provided namespace='org.eclipse.equinox.p2.eclipse.type' name='bundle' version='1.0.0'/>
      </provides>
      <artifacts size='1'>
        <artifact classifier='osgi.bundle' id='org.eclipse.jst.server.jetty.ui.websocket' version='*******01107011148'/>
      </artifacts>
      <touchpoint id='org.eclipse.equinox.p2.osgi' version='1.0.0'/>
      <touchpointData size='1'>
        <instructions size='1'>
          <instruction key='manifest'>
            Bundle-SymbolicName: org.eclipse.jst.server.jetty.ui.websocket&#xA;Bundle-Version: *******01107011148&#xA;
          </instruction>
        </instructions>
      </touchpointData>
    </unit>
  </units>
</repository>
