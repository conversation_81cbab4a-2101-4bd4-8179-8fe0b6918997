<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"><head>


<meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>About</title></head><body lang="EN-US">
<h2>About This Content</h2>
 
<p>May 30, 2007</p>	
<h3>License</h3>

<p>The Eclipse Foundation makes available all content in this plug-in ("Content").  Unless otherwise 
indicated below, the Content is provided to you under the terms and conditions of the
Eclipse Public License Version 1.0 ("EPL").  A copy of the EPL is available 
at <a href="http://www.eclipse.org/legal/epl-v10.html">http://www.eclipse.org/legal/epl-v10.html</a>.
For purposes of the EPL, "Program" will mean the Content.</p>

<p>If you did not receive this Content directly from the Eclipse Foundation, the Content is 
being redistributed by another party ("Redistributor") and different terms and conditions may
apply to your use of any object code in the Content.  Check the Redistributor's license that was 
provided with the Content.  If no such license exists, contact the Redistributor.  Unless otherwise
indicated below, the terms and conditions of the EPL still apply to any source code in the Content
and such source code may be obtained at <a href="http://www.eclipse.org/">http://www.eclipse.org</a>.</p>

		
		<h3>Third Party Content</h3>
		<p>The Content includes items that have been sourced from third parties as set out below. If you 
		did not receive this Content directly from the Eclipse Foundation, the following is provided 
		for informational purposes only, and you should look to the Redistributor's license for 
		terms and conditions of use.</p>
		<p><em>
		
	    <strong>BCEL v5.1</strong>
		<p>This product contains software developed by the
         Apache Software Foundation (<a href="http://www.apache.org/">http://www.apache.org</a>).</p>
 
		<p>AspectJ includes a modified version of the Apache Jakarta Byte Code Engineering Library (BCEL) v5.1.
		BCEL is available at <a href="http://jakarta.apache.org/bcel/">http://jakarta.apache.org/bcel/</a>. Source
		code for the modified version of BCEL is available at Eclipse.org in the AspectJ source tree. This code
		is made available under the Apache Software License v1.1</p>
		
		</em></p>


</body></html>