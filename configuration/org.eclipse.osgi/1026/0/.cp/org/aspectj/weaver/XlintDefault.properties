invalidAbsoluteTypeName = warning
invalidWildcardTypeName = ignore

unresolvableMember = warning

typeNotExposedToWeaver = warning

shadowNotInStructure = ignore

unmatchedSuperTypeInCall = warning

canNotImplementLazyTjp = ignore
multipleAdviceStoppingLazyTjp=ignore
noGuardForLazyTjp=ignore

uncheckedAdviceConversion = warning

needsSerialVersionUIDField = ignore
brokeSerialVersionCompatibility = ignore

noInterfaceCtorJoinpoint = warning

noJoinpointsForBridgeMethods = warning
cantMatchArrayTypeOnVarargs = ignore
enumAsTargetForDecpIgnored = warning
annotationAsTargetForDecpIgnored = warning
adviceDidNotMatch = warning
invalidTargetForAnnotation = warning
elementAlreadyAnnotated = warning
runtimeExceptionNotSoftened = warning
uncheckedArgument = warning
noExplicitConstructorCall = warning

aspectExcludedByConfiguration = ignore

unmatchedTargetKind = warning

cantFindType = error
cantFindTypeAffectingJPMatch = warning

unorderedAdviceAtShadow=ignore
swallowedExceptionInCatchBlock=ignore
calculatingSerialVersionUID=ignore
advisingSynchronizedMethods=warning
mustWeaveXmlDefinedAspects=warning

missingAspectForReweaving=error
cannotAdviseJoinpointInInterfaceWithAroundAdvice=warning

nonReweavableTypeEncountered=error