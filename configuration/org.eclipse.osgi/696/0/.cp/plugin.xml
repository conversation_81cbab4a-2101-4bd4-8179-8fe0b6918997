<?xml version="1.0" encoding="UTF-8"?>
<?eclipse version="3.0"?><!--
     Copyright (c) 2005, 2009 IBM Corporation and others.
     All rights reserved. This program and the accompanying materials
     are made available under the terms of the Eclipse Public License v1.0
     which accompanies this distribution, and is available at
     http://www.eclipse.org/legal/epl-v10.html
    
     Contributors:
         IBM Corporation - initial API and implementation
 -->

<plugin>

   <extension
         point="org.eclipse.ui.intro.configExtension">
      <configExtension
            configId="org.eclipse.ui.intro.universalConfig" 
            content="$nl$/intro/overviewExtensionContent.xml"/>  
      <configExtension
            configId="org.eclipse.ui.intro.universalConfig"
            content="$nl$/intro/tutorialsExtensionContent.xml"/>
      <configExtension
            configId="org.eclipse.ui.intro.universalConfig"
            content="$nl$/intro/samplesExtensionContent.xml"/>
      <configExtension
            configId="org.eclipse.ui.intro.universalConfig"
            content="$nl$/intro/samplesExtensionContent2.xml"/>
      <configExtension
            configId="org.eclipse.ui.intro.universalConfig"
            content="$nl$/intro/whatsnewExtensionContent.xml"/> 
   </extension>

   <extension 
   		 point="org.eclipse.ui.cheatsheets.cheatSheetContent">
      <category
            id="org.eclipse.pde"
            name="%cheatsheet.category.pde"/>
      <cheatsheet
            category="org.eclipse.pde"
            composite="true"
            contentFile="$nl$/cheatsheets/helloworld-composite.xml"
            id="org.eclipse.pde.helloworld"
            name="%cheatsheet.helloworld.name">
         <description>%cheatsheet.helloworld.desc</description>
      </cheatsheet>
      <cheatsheet
            category="org.eclipse.pde"
            composite="true"
            contentFile="$nl$/cheatsheets/rcpapp-composite.xml"
            id="org.eclipse.pde.rcpapp"
            name="%cheatsheet.rcpapp.name">
         <description>
            %cheatsheet.rcpapp.desc
         </description>
      </cheatsheet>
      <cheatsheet
            category="org.eclipse.pde"
            composite="false"
            contentFile="cheatsheets/setup-apitools-existing-projects.xml"
            id="org.eclipse.pde.cheatsheet.setup.apitools.existing"
            name="%cheatsheet.apitools.setupexisting.name">
         <description>
            %cheatsheet.apitools.setupexisting.description
         </description>
      </cheatsheet>
   </extension>
   
</plugin>
