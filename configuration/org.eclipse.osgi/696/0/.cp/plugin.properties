###############################################################################
# Copyright (c) 2000, 2009 IBM Corporation and others.
# All rights reserved. This program and the accompanying materials
# are made available under the terms of the Eclipse Public License v1.0
# which accompanies this distribution, and is available at
# http://www.eclipse.org/legal/epl-v10.html
#
# Contributors:
#     IBM Corporation - initial API and implementation
###############################################################################
pluginName=PDE
providerName=Eclipse.org

#Cheatsheets  
cheatsheet.category.pde = Plug-in Development

cheatsheet.helloworld.name = Creating an Eclipse Plug-in
cheatsheet.helloworld.desc = This cheat sheet helps you to create a plug-in, a plug-in extension, a feature and an update site using PDE.  \
It also helps you install and uninstall a feature using Install/Update.

cheatsheet.rcpapp.name = Creating a Rich Client Application
cheatsheet.rcpapp.desc = This cheat sheet helps you to create, define, test and export a fully-branded standalone RCP product.

cheatsheet.apitools.setupexisting.name = Set up API Tools on Existing Projects
cheatsheet.apitools.setupexisting.description = This cheat sheet will help you set up API Tools on existing projects in your workspace.