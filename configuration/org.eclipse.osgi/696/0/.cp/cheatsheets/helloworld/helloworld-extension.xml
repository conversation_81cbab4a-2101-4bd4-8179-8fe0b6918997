<?xml version="1.0" encoding="UTF-8"?><!--
     Copyright (c) 2005, 2008 IBM Corporation and others.
     All rights reserved. This program and the accompanying materials
     are made available under the terms of the Eclipse Public License v1.0
     which accompanies this distribution, and is available at
     http://www.eclipse.org/legal/epl-v10.html
    
     Contributors:
         IBM Corporation - initial API and implementation
 -->

<cheatsheet title="Create a plug-in extension">
   <intro href="/org.eclipse.platform.doc.user/reference/ref-cheatsheets.htm">
      <description>
         This cheat sheet will demonstrate how to create and test a plug-in extension
         using PDE. 
         <br/>
         <br/>
         To learn more about using cheat sheets, click the help button
         in the top right corner (?).
      </description>
   </intro>
   <item title="Open the plug-in development perspective" dialog="true" skip="false" href="/org.eclipse.pde.doc.user/guide/tools/views/views.htm">
      <description>
         To open the plug-in development perspective,
         select <b>Window-&gt;Open Perspective-&gt;Other...</b> 
         and choose <b>Plug-in Development</b>.
         <br/>
         <br/>
         Alternatively, click the following button to perform the task.
      </description>
      <command
            confirm="false"
            required="false"
            translate=""
            serialization="org.eclipse.ui.perspectives.showPerspective(org.eclipse.ui.perspectives.showPerspective.perspectiveId=org.eclipse.pde.ui.PDEPerspective)">
      </command>
   </item>
   <item title="Open the plug-in editor" dialog="true" skip="false" href="/org.eclipse.pde.doc.user/guide/tools/editors/manifest_editor/editor.htm">
      <description>
         To open the plug-in editor (if it is not already open),
         expand the <b>com.example.helloworld</b> tree dialog
         in the <b>Package Explorer</b> view and double-click
         on the <b>plugin.xml</b> file.
         <br/>
         <br/>
         Alternatively, click the following button to perform the task.
      </description>
      <command
            confirm="false"
            required="false"
            translate=""
            serialization="org.eclipse.ui.navigate.openResource(filePath=/com.example.helloworld/plugin.xml)">
      </command>
   </item>
   <item title="Define a view extension" dialog="true" skip="false" href="/org.eclipse.pde.doc.user/guide/tools/editors/manifest_editor/extensions.htm">
      <description>
         To define a view extension, perform the following steps:
      </description>
      <subitem label="Select the &quot;Extensions&quot; tab within the plug-in editor to display the &quot;Extensions&quot; page" skip="false">
      </subitem>
      <subitem label="Click the &quot;Add&quot; button to open the &quot;New Extension&quot; wizard" skip="false">
      </subitem>
      <subitem label="Select &quot;org.eclipse.ui.views&quot; from the &quot;Extension Points&quot; list box and click &quot;Finish&quot;" skip="false">
      </subitem>
   </item>
   <item title="Add a view category" dialog="true" skip="false" href="/org.eclipse.pde.doc.user/guide/tools/editors/manifest_editor/extensions.htm">
      <description>
         To define a view category, perform the following steps:
      </description>
      <subitem label="Select &quot;org.eclipse.ui.views&quot; in the list box under &quot;All Extensions&quot; heading in the &quot;Extensions&quot; page" skip="false">
      </subitem>
      <subitem label="Right-click and select &quot;New-&gt;category&quot; from the context menu" skip="false">
      </subitem>
      <subitem label="Replace the content of the &quot;id&quot; text box under &quot;Extension Element Details&quot; with &quot;com.example.helloworld.view.helloworldcategory&quot;" skip="false">
      </subitem>
      <subitem label="Replace the content of the &quot;name&quot; text box with &quot;Hello World!&quot;" skip="false">
      </subitem>
      <subitem label="Select &quot;File-&gt;Save&quot; from the main menu" skip="false">
      </subitem>
   </item>
   <item title="Add a view" dialog="true" skip="false" href="/org.eclipse.pde.doc.user/guide/tools/editors/manifest_editor/extensions.htm">
      <description>
         To define a view, perform the following steps:
      </description>
      <subitem label="Select &quot;org.eclipse.ui.views&quot; in the list box under &quot;All Extensions&quot; heading in the &quot;Extensions&quot; page" skip="false">
      </subitem>
      <subitem label="Right-click and select &quot;New-&gt;view&quot; from the context menu" skip="false">
      </subitem>
      <subitem label="Replace the content of the &quot;id&quot; text box under &quot;Extension Element Details&quot; with &quot;com.example.helloworld.view.helloworldview&quot;" skip="false">
      </subitem>
      <subitem label="Replace the content of the &quot;name&quot; text box with &quot;Hello World!&quot;" skip="false">
      </subitem>
      <subitem label="Replace the content of the &quot;class&quot; text box with &quot;com.example.helloworld.view.SampleView&quot;" skip="false">
      </subitem>
      <subitem label="Replace the content of the &quot;category&quot; text box with &quot;com.example.helloworld.view.helloworldcategory&quot;" skip="false">
      </subitem>
      <subitem label="Select &quot;File-&gt;Save&quot; from the main menu" skip="false">
      </subitem>
   </item>
   <item title="Create a view class" dialog="true" skip="false">
      <description>
         To create a view class, perform the following steps:
      </description>
      <subitem label="Click the &quot;class&quot; hyperlink under &quot;Extension Element Details&quot; to start the &quot;New Java Class&quot; wizard" skip="false">
      </subitem>
      <subitem label="Accept the default values and click the &quot;Finish&quot; button to exit the wizard" skip="false">
      </subitem>
   </item>
   <item title="Add view class code" dialog="true" skip="false">
      <description>
         We are now going to add code to our view class to simply
         display a &quot;Hello, Eclipse world!&quot; message in the view when
         it is opened.
         <br/>
         <br/>
         The previous step should have opened the <b>SampleView</b> class
         in a Java editor.
         <br/>
         <br/>
         Add the following lines of code under the pre-generated 
         <b>createPartControl</b> method:
         <br/>
         <br/>
		 Label label = new Label( parent, SWT.LEFT );
         <br/>
		 label.setText( &quot;Hello, Eclipse world!&quot; );
         <br/>
         <br/>
         Note:  There will be two import errors to correct.
         <br/>
         Either add the following import statements manually or
         click on the lightbulb icons in the margin of the Java
         editor to add them:
         <br/>
         <br/>
		 import org.eclipse.swt.widgets.Label;
         <br/>
		 import org.eclipse.swt.SWT;
		 <br/>
		 <br/>
		 Select &quot;File-&gt;Save&quot; from the main menu
      </description>
   </item>
   <item title="Test the plug-in" dialog="true" skip="false" href="/org.eclipse.pde.doc.user/guide/tools/launchers/eclipse_application_launcher.htm">
      <description>
         To test your Eclipse plug-in inside a target Eclipse platform,
         perform the following steps:
      </description>
      <subitem label="Open the plug-in editor" skip="false">
         <command
               confirm="false"
               required="false"
	           translate=""
               serialization="org.eclipse.ui.navigate.openResource(filePath=/com.example.helloworld/plugin.xml)">
         </command>
      </subitem>
      <subitem label="Select the &quot;Overview&quot; tab to display the &quot;Overview&quot; page" skip="false">
      </subitem>
      <subitem label="Click the &quot;Launch an Eclipse application&quot; hyperlink under the &quot;Testing&quot; heading" skip="false">
      </subitem>
      <subitem label="Select &quot;Window-&gt;Show View-&gt;Other...&quot; from the main menu in the newly launched Eclipse platform" skip="false">
      </subitem>
      <subitem label="Note the &quot;Hello World!&quot; category and expand its tree dialog" skip="false">
      </subitem>
      <subitem label="Select the &quot;Hello World!&quot; view and click the &quot;OK&quot; button" skip="false">
      </subitem>
      <subitem label="Select &quot;File-&gt;Exit&quot; from the main menu to exit the target Eclipse platform" skip="false">
      </subitem>
   </item>
</cheatsheet>
