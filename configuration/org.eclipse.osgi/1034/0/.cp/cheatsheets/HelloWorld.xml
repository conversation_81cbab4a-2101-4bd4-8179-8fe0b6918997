<?xml version="1.0" encoding="UTF-8" ?> 
<cheatsheet title="Simple AspectJ Application">

	<intro> 
		<description>
			Welcome to the Hello World AspectJ tutorial. It will help you 
			extend the famous Java &quot;Hello World&quot; application by 
			adding aspects. You will create an AspectJ project, a class 
			which prints &quot;Hello&quot; in the console when run, and an aspect 
			that advises execution of the main method and prints &quot;World&quot;.
			<br/>
			<br/>
			Let's get started!
		</description>
</intro>

	<item
		href="/org.eclipse.platform.doc.user/concepts/concepts-4.htm"
		title="Open the Java Perspective"
		skip="true">
		<action
			pluginId="org.eclipse.ui.cheatsheets"
			class="org.eclipse.ui.internal.cheatsheets.actions.OpenPerspective"
			param1="org.eclipse.jdt.ui.JavaPerspective"/>
		<description>
			Select <b>Window-&gt;Open Perspective-&gt;Java</b> in the menubar at 
			the top of the workbench. This step changes the perspective to set up 
			the Eclipse workbench for Java and AspectJ development. You can click 
			the &quot;Click to Perform&quot; button to have the Java perspective 
			opened automatically. If you do not use the "Click to Perform" button, 
			click the "Click to Skip" button below to proceed to the next step.
		</description>
</item>

	<item
		href="/org.eclipse.ajdt.doc.user/gettingStarted/firstproject.htm"
		title="Create an AspectJ Project"
		skip="true">
		<action
			pluginId="org.eclipse.ajdt.ui"
			class="org.eclipse.ajdt.internal.ui.actions.AspectJProjectWizardAction"/>
		<description>
			The first thing you will need is an AspectJ Project. You can do this either
			by clicking the &quot;Click to Perform&quot; button or by using the 
			<b>File-&gt;New-&gt;AspectJ Project</b> action to bring up the New AspectJ 
			Project wizard. Enter a name for your new project and press "Finish".
			<br/>
			<br/>
			If this is the first AspectJ project you have created in 
			your workbench then an AJDT Preferences Configuration Wizard will appear 
			when you press &quot;Finish&quot; on the New AspectJ Project wizard. Leave 
			everything ticked on this configuration page and press &quot;Finish&quot;.
			<br/>
			<br/>
			If you do not use the "Click to Perform" button, click the "Click to Skip"
			button below to proceed to the next step.
		</description>
</item>

	<item
		href="/org.eclipse.jdt.doc.user/tasks/tasks-15.htm"
		title="Create the hello package"
		skip="true">
		<action
			pluginId="org.eclipse.jdt.ui"
			class="org.eclipse.jdt.ui.actions.OpenNewPackageWizardAction"/>
		<description>
			The next thing you will need is a package to put your class and aspect in. 
			In this step we create a &quot;hello&quot; package.
			<br/>
			<br/>
			To create a new package either press the &quot;Click to 
			Perform&quot; button below to launch the New Java Package wizard, 
			or use the <b>File-&gt;New-&gt;Package</b> 
			action.  When you use the wizard, specify the package name to be hello.
			If you do not use the "Click to Perform" button, click the "Click to Skip"
			button below to proceed to the next step. 
		</description>
</item>

	<item
		href="/org.eclipse.jdt.doc.user/gettingStarted/qs-9.htm" 
		title="Create the HelloWorld class"
		skip="true">
		<action
			pluginId="org.eclipse.jdt.ui"
			class="org.eclipse.jdt.ui.actions.OpenNewClassWizardAction"/>
		<description>
			You should now have an AspectJ Project containing a hello package in your 
			workspace. The next step in building the Hello World application is to 
			create the HelloWorld class.
			<br/>
			<br/>
			To create a new class either press the 
			&quot;Click to Perform&quot; button below to launch the New Java Class wizard, 
			or use <b>File-&gt;New-&gt;Class</b> 
			action.  When you use the wizard, make sure that you specify that you would 
			like to have a &quot;main&quot; method added, name your class &quot;HelloWorld&quot; 
			and specify the package to be hello.
			If you do not use the "Click to Perform" button, click the "Click to Skip"
			button below to proceed to the next step. 
		</description>
</item>

	<item
		href="/org.eclipse.jdt.doc.user/tasks/tasks-54.htm" 
		title="Add a sayHello() method to the HelloWorld class which prints out Hello">
		<description>
			Now that you have a HelloWorld class, you can create a public void sayHello() 
			method which takes no arguments and prints out "Hello". Add the following to the 
			body of the HelloWorld class:
			<br/>
			<br/>
			<b>public void sayHello() { System.out.print(" Hello ");}</b> 
			<br/>
			<br/>
			and save your changes. Press the &quot;Click to Complete&quot; button to move
			onto the next step.
		</description>
</item>

	<item
		href="/org.eclipse.jdt.doc.user/tasks/tasks-54.htm"
		title="Add a call to sayHello() in the main method">
		<description>
			You now need to call the sayHello() method. In the &quot;public static void main&quot; 
			method add the following:
			<br/>
			<br/>
			<b>new HelloWorld().sayHello();</b>
			<br/>
			<br/>
			and save your changes. Press the &quot;Click to Complete&quot; button when you have 
			done this.
 		</description>
</item>

	<item
		href="/org.eclipse.jdt.doc.user/gettingStarted/qs-12.htm"
		title="Test the application">
		<description>
			Before adding an aspect to the application, let's first test it as a java application.
			<br/>
			<br/>
			Select the HelloWorld class in the package explorer (the java tree view on the left 
			of the workbench window in the Java Perspective.). Right-click to bring up the context
			menu and select <b>Run-&gt;Java Application</b>.
			<br/>
			<br/>
			The &quot;Console&quot; view at the bottom of the Java Perspective should now 
			contain the text &quot;Hello&quot;.
			<br/>
			<br/>
			You are now ready to add an aspect...
		</description>
</item>

	<item
		href="/org.eclipse.ajdt.doc.user/gettingStarted/firstaspect.htm"
		title="Create the World Aspect"
		skip="true">
		<action
			pluginId="org.eclipse.ajdt.ui"
			class="org.eclipse.ajdt.internal.ui.actions.OpenAspectWizardAction"/>
		<description>
			Now you have a running java application in your workspace, the next step in 
			building the Hello World application is to create the World aspect.  
			<br/>
			<br/>
			To create a new Aspect you can either use the <b>File-&gt;New-&gt;Aspect</b> action 
			or click the &quot;Click to Perform&quot; button below to bring up the New Aspect Wizard.  
			When you use the wizard, make sure to name your aspect &quot;World&quot; 
			and specify that you want it created in the hello package of your project.
			If you do not use the "Click to Perform" button, click the "Click to Skip"
			button below to proceed to the next step.
		</description>
</item>

	<item
		href="/org.aspectj.ajde/doc/progguide/starting-aspectj.html#pointcuts"
		title="Define a greeting() pointcut">
		<description>
			Now that you have a World aspect, add the following statement to the
			body of the World aspect:
			<br/>
			<br/>
			<b>pointcut greeting() : execution(* HelloWorld.sayHello(..));</b>
			<br/>
			<br/>
			and save your changes.  This defines a pointcut greeting() which will
			match the execution of any method named HelloWorld.sayHello(),  
			with any return type and taking any number of arguments.
			<br/>
			<br/>
			Press the &quot;Click to Complete&quot; button below when you're finished.
 		</description>
</item>

	<item
		href="/org.aspectj.ajde/doc/progguide/starting-aspectj.html#advice"
		title="Define after() returning advice to print &quot;World!&quot;">
		<description>
			The greeting pointcut will match the execution of the sayHello() method, but
			doesn't do anything in its own right. To specify some behaviour to execute at
			the join points it matches, you need to declare some <b>advice</b>. 
			<br/>
			<br/>
			There are different types of advice depending on when you want the behaviour
			to be executed. In this case we want to do something after "Hello" has been
			printed, so we will use a form of after advice. 
			After any execution of HelloWorld.sayHello() (as matched by the 
			greeting() pointcut), we want to print out &quot;World!&quot;. 
			<br/>
			<br/>
			In the World aspect, add the following after the pointcut statement:
			<br/>
			<br/>
			<b>after() returning : greeting() { System.out.print("World!");}</b>
			<br/>
			<br/>
			and save your changes. Press the &quot;Click to Complete&quot; button below 
			when you've finished.
 		</description>
</item>

	<item
		href="/org.eclipse.jdt.doc.user/tasks/tasks-106.htm"
		title="Building the Application"
		skip="true">
		<description>
			Before running your application, you must first link the World aspect with
			the HelloWorld class. This is done when you save any changes
			to your application unless you have unchecked the &quot;Build Automatically&quot; option
			in the Project drop down menu.
			<br/>
			<br/>
			If you're building automatically, then press the "Click to Skip" button below, otherwise
			click the page icon with the orange
			&quot;010&quot; along the top bar of the java perspective to build your AspectJ project. 
			Press the &quot;Click to Complete&quot; button below when your project has built.
 		</description>
</item>

	<item
		href="/org.eclipse.platform.doc.user/concepts/coutline.htm"
		title="Open the Outline View">
		<description>
			Before running the new application, 
			let's have a look at some of the features of AJDT which help you see which code 
			your aspects are affecting. We'll look first at the Outline View.
			<br/>
			<br/>
			If you have not changed the setup of the Java Perspective, you should see the 
			Outline View in the top right hand corner of the Java Perspective. If not, select 
			<b>Window-&gt;Show View-&gt;Outline</b> in the menubar at the top of the workbench.
			Press the &quot;Click to Complete&quot; button below when you have found the outline view.
 		</description>
</item>

	<item
		href="/org.eclipse.platform.doc.user/concepts/coutline.htm"
		title="Viewing the World Aspect in the Outline View">
		<description>
			Let us first look at the World Aspect. Select the World Aspect so that it 
			appears in the editor. You see that the Outline View now contains information 
			about the World Aspect.
			<br/>
			<br/>
			The <b>blue lightning bolt</b> next to greeting() indicates that you have a pointcut 
			called greeting().
			<br/>
			<br/>
			The <b>orange arrow</b> next to afterReturning() indicates that you have 
			defined some after() returning advice. 
			<br/>
			<br/>
			When the editor is open on the World aspect, notice on the left hand bar of the editor 
			there is an <b>orange arrow</b> pointing to the left, and on the right hand bar of the editor there is an <b>orange 
			block</b>, both of which appear on the line of the after advice. These are 
			visual aids to let you know that this piece of advice affects some of your code. 
			Hovering over them tells you that they are advising the sayHello() method in
			the HelloWorld class. Moreover, right clicking on the orange arrow brings up a menu
			which contains an <b>advises</b> option. Navigating to this will display a sub-menu
			which also tells you that the sayHello() method is 
			advised by this after() returning advice. You can select this to
			navigate to the affected place in the code.
			<br/>
			<br/>
			Press the &quot;Click to Complete&quot; button 
			below when you've finished looking at the outline view for the World aspect.
 		</description>
</item>

	<item
		href="/org.eclipse.platform.doc.user/concepts/coutline.htm"
		title="Viewing the advised HelloWorld class in the Outline View">
		<description>
			Select the HelloWorld class so that it appears in the editor. You see that the 
			Outline View now contains information about the advised HelloWorld class. 
			There is a little orange arrow next to the sayHello() method. This tells you
			that the sayHello() method is affected by some advice.
			<br/>
			<br/>
			When the editor is open on the HelloWorld class, notice on the left hand bar of the editor 
			there is an <b>orange arrow</b>, and on the right hand bar of the editor there is an <b>orange 
			block</b>, both of which appear on the line of the sayHello() method. These are other 
			visual aids to let you know that your sayHello() method is advised by an aspect. 
			Hovering over them tells you that they are advised by the after() returning
			advice in the World aspect. Moreover, right clicking on the orange arrow brings up a menu
			which contains an <b>advised by</b> option. Navigating to this will display a sub-menu
			which also tells you that the sayHello() method is 
			advised by the after() returning advice in the World aspect. You can select this advice to
			navigate to the advice definition in the World aspect.
			<br/>
			<br/>
			When you are comfortable with the different features AJDT offers via the Outline 
			View and Editor press the &quot;Click to Complete&quot; button below.
 		</description>
</item>

	<item
		href="/org.eclipse.ajdt.doc.user/gettingStarted/crosscutting.htm"
		title="Open the Cross Reference View">
		<description>
			AJDT provides a new view which shows you where your advice is affecting.
			This is called the Cross Reference View and we'll look at this now.
			<br/>
			<br/>
			Select <b>Window-&gt;Show View-&gt;Other-&gt;AspectJ-&gt;Cross References</b> 
			in the menubar at the top of the workbench. This brings up the Cross Reference
			view at the bottom of the Java Perspective. Press the &quot;Click to Complete&quot; 
			button below when you have found the Cross Reference view.
 		</description>
</item>


	<item
		href="/org.eclipse.ajdt.doc.user/gettingStarted/crosscutting.htm"
		title="Viewing the World Aspect in the Cross Reference View">
		<description>
			Let us first look at the World Aspect. Select the World Aspect so that it 
			appears in the editor. You see that the Cross Reference view now contains information 
			about the World Aspect.
			<br/>
			<br/>
			The <b>orange arrow</b> next to afterReturning() indicates that you have 
			defined some after returning advice. Below this there is an <b>advises</b>
			node which tells you that this piece of advice affects some code. Expanding
			this node tells you that the sayHello() method in the HelloWorld class is
			affected by this advice. Clicking on <b>HelloWorld.sayHello()</b> in the 
			Cross Reference view will take you to this point in the HelloWorld class.
			<br/>
			<br/>
			Press the &quot;Click to Complete&quot; button 
			below when you've finished looking at the Cross Reference view for the World aspect.
 		</description>
</item>


	<item
		href="/org.eclipse.ajdt.doc.user/gettingStarted/crosscutting.htm"
		title="Viewing the advised HelloWorld class in the Cross Reference View">
		<description>
			Select the HelloWorld class so that it appears in the editor. You see that the 
			Cross Reference view now contains information about the advised HelloWorld class. 
			Expanding all the nodes you see that the HelloWorld class has a method
			<b>sayHello()</b> in it which is <b>advised by</b> the <b>afterReturning()</b> advice in 
			the World aspect. Notice that the Cross Reference view only contains information
			about those methods which are affected by advice.
			<br/>
			<br/>			
			When you are comfortable with the different features AJDT offers via the Cross Reference 
			View press the &quot;Click to Complete&quot; button below.
 		</description>
</item>

	<item
		href="/org.eclipse.ajdt.doc.user/gettingStarted/visualising.htm"
		title="Open the Aspect Visualization Perspective">
		<description>
			There is a whole perspective dedicated to showing you where the aspects you have
			written interact with the types in your program. This is called the <b>Aspect Visualization 
			Perspective</b>. Select <b>Window-&gt;Open Perspective-&gt;Other-&gt;Aspect Visualization</b> 
			in the menubar at the top of the workbench. This step changes the perspective to set 
			up the Eclipse workbench for AspectJ visualisation. Press the &quot;Click to Complete&quot; 
			button when you have opened the Aspect Visualization perspective.
	</description>
</item>

	<item
		href="/org.eclipse.ajdt.doc.user/gettingStarted/visualising.htm"
		title="The AspectJ Visualiser">
		<description>
			In the middle of your workbench is the <b>AspectJ Visualiser</b>. Selecting either your project 
			in the left hand project view, or the hello package in the Package view produces two bars 
			in the visualiser. One bar has the title HelloWorld and the other has the title World. 
			These represent the HelloWorld class and the World aspect respectively. The length of 
			the bars corresponds to the length of the files. You will see that your World bar is
			dark grey, whereas your HelloWorld bar is white and contains a stripe. Being dark grey
			shows that your World aspect is not itself advised by any aspects in the application. On the other
			hand, being white and containing a stripe shows that the HelloWorld class is advised by
			an aspect in the application.
			<br/>
			<br/>
			Looking at the <b>Visualiser Menu</b> (which is to the right of the Visualiser) you see 
			that there is a button with the same color as the stripe on it and next to it the name 
			of your aspect. This means that this color is used to indicate the places in the code
			where advice defined in the World aspect is in effect.
			It also currently has a tick in its box which means that it 
			is included in the visualization.
			<br/>
			<br/>
			Returning to the Visualiser and hovering over the stripe also tells you that it is 
			advice from the World aspect. If you had more than one aspect then each would have a 
			different color so you could distinguish between the advice from the different aspects. 
			Finally, double clicking on the stripe in the HelloWorld bar takes you to the relevant 
			place in your HelloWorld class in the editor at the bottom of the perspective.
			<br/>
			<br/>
			Press the &quot;Click to Complete&quot; button when you have become familiar with the 
			AspectJ Visualization perspective.
	</description>
</item>

	<item
		href="/org.eclipse.ajdt.doc.user/gettingStarted/launching.htm"
		title="Test the application">
		<description>
			The final moment has arrived.  You are ready to test the AspectJ Hello World application.
			Since you've already tested your application when it just contained a single class,
			you should already have a run configuration for your Hello World application.
			<br/>
			<br/>
			Return to the Java Perspective (<b>Window-&gt;Open Perspective-&gt;Java</b>) and 
			press the down arrow beside the green circle containing a white arrow icon in 
			the toolbar and select HelloWorld. Hello World! should be printed in your 
			&quot;Console&quot; view.
			<br/>
			<br/>
			Congratulations!  You have successfully built and run the AspectJ Hello World application.
		</description>
</item>


	<item
		href="/org.aspectj.ajde/doc/progguide/starting-aspectj.html#advice"
		title="Saying World, Hello rather than Hello World">
		<description>
			In the example so far we have used after returning advice to print &quot;Hello World&quot; to the 
			console, but what if we wanted to print &quot;World, Hello&quot;? For this we can use <b>before</b> 
			advice. Before advice behaves in a similar way to after advice except that it happens before 
			rather than after the join points matched by the pointcut.
			<br/>
			<br/>
			Open the World aspect and replace <b>after() returning</b> with <b>before()</b>, and 
			<b>World!</b> with <b>World,</b>. Save the changes, 
			build the application if necessary and note
			the changes to the Outline View and AspectJ Visualization perspective. Run the application 
			as before and &quot;World, Hello&quot; should be printed in your &quot;Console&quot; view. 
			Press the &quot;Click to Complete&quot; button below when you've finished.
 		</description>
</item>


</cheatsheet>
