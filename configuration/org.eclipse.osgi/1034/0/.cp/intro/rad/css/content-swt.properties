
overview.layout.ncolumns = 2
overview.layout.link-vspacing = 40

overview.basics.link-icon = css/graphics/icons/obj48/teamsup_obj.gif
overview.team.link-icon = css/graphics/icons/obj48/wbbasics_obj.gif
overview.overview-links.layout.ncolumns = 2

overview.subtitle-id = overview/page-content/page-title
overview.description-id = overview/page-content/page-description

news.layout.ncolumns = 2

news.new-and-noteworthy.link-icon = css/graphics/icons/obj48/new_obj.gif
news.migration.link-icon = css/graphics/icons/obj48/migrate_obj.gif
news.updates.link-icon = css/graphics/icons/obj48/updates_obj.gif
news.eclipse.link-icon = css/graphics/icons/obj48/community_obj.gif

news.subtitle-id = news/page-content/page-title



samples.layout.ncolumns = 2
samples.layout.link-vspacing = 20

samples.link-icon = css/graphics/icons/obj48/samplepurp_obj.gif
samples.standalone.link-icon = css/graphics/icons/obj48/samplered_obj.gif
samples.views.link-icon = css/graphics/icons/obj48/samplered_obj.gif
samples.multi-page-editor.link-icon = css/graphics/icons/obj48/samplepurp_obj.gif
samples.property-sheet.link-icon = css/graphics/icons/obj48/samplepurp_obj.gif
samples.readmetool.link-icon = css/graphics/icons/obj48/samplepurp_obj.gif

samples.page-content.layout.ncolumns = 1
samples.swt.layout.ncolumns = 2
samples.workbench.layout.ncolumns = 2

description-style-id = group-description
samples.subtitle-id = samples/page-content/page-title
samples.description-id = samples/page-content/page-description



tutorials.layout.ncolumns = 2
tutorials.layout.link-vspacing = 20

tutorials.page-content.layout.ncolumns = 2
tutorials.subtitle-id = tutorials/page-content/page-title
tutorials.description-id = tutorials/page-content/page-description
