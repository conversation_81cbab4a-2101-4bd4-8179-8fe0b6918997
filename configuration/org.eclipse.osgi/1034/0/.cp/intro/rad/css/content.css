/** 
  * CSS content formatting
 **/

/*
 * Set up the body area for transition (scaffolding) page. 
 */
 
body {
	background-color : #FFFFFF;
	background-repeat : no-repeat;
	background-position : bottom right;
}

body, .page {
	min-width : 770px;
	/* since IE doesn't support min-width, try expression */
	width:expression(document.body.clientWidth < 770? "770px": "auto" );
	min-height : 475px;
	height : 100%;
	height : expression(document.body.clientHeight < 425? "425px": "100%" );
}

.page { 
	min-height : 700px;
}

/*
 * Transition page css settings
 */

#t-links {
	text-align : left;
	width : 760px;
	/* To center in Moz (have to use text-align for IE) */	
	margin: 0px auto;
}

#t-links a { 
	width : 370px;
	text-align : left; 
	margin-left : 5px;
	margin-right : 5px;
	margin-top : 5px;
	margin-bottom : -20px;
	vertical-align : top;
}

#t-links > a { vertical-align : middle; }

#t-links a img {
	height : 57px;
	width : 57px;
	vertical-align : middle;
}

#t-links a .link-label {
	display : block;
	width : 300px;
	position : relative;
	top : -50px;
	left : 60px;
}

#t-links a p .text {
	display : block;
	width : 300px;
	position : relative;
	top : -45px;
	left : 53px;
}

/* 
 * Special case for Mozilla, because the links are displayed
 * in 1 vertical column instead of 2 centered columns 
 */

#t-links > a { 	width : 700px; }
#t-links a > .link-label { width : 700px; }
#t-links a p > .text { width : 700px; }

#t-links a:hover { border-right : 5px; }


/*** Backup Link Image ***/

a#t-links img { background-image : url(graphics/icons/obj48/new_obj.gif); }
a#t-links:hover img { background-image : url(graphics/icons/obj48/newhov_obj.gif); }

/*
 * Content page settings
 */

#page-content {
	float : none;
	clear : both;
	text-align : center; 
	margin-top : 35px;
}

#content-page-content {
	float : none;
	clear : both;
	text-align : left; 
	/* margin-top : 35px; */
}

.page > #content-page-content { margin-top : 100px; }

#content-page-content p { 
	padding-bottom : 15px; 
	margin-left : 25px;
	text-align : left; 
	float : none;
	clear : both;
}

#heading {
	font-size : 11pt;
	font-weight : normal;
	font-style : normal;
	text-decoration : none;
}

#bullet {
	list-style-image : url(graphics/bullet.gif);
	list-style-position : outside;
	list-style-type : disc;

	font-size : 9pt;
	font-weight : normal;
	font-style : normal;
}


#mybullet {
	list-style-image : url(graphics/bullet.gif);
	list-style-position : outside;
	list-style-type : disc;
}

#mybullet p {
	padding-bottom : 15px; 
	margin-left : 125px;
	text-align : left; 
	float : none;
	clear : both;
}

#bullet p { 
	padding-bottom : 15px; 
	margin-left : 25px;
	text-align : left; 
	float : none;
	clear : both;
}

#paragraph {
	font-size : 9pt;
	font-weight : normal;
	font-style : normal;
	text-decoration : none;
	text-align : left;	
}

#italic {
	font-size : 9pt;
	font-weight : normal;
	font-style : italic;
	text-decoration : none;	
	text-align : left;	
}

#bold {
	font-size : 9pt;
	font-weight : bold;
	font-style : normal;
	text-decoration : none;	
}

.problem-description2 p {
	display : inline;
	text-align : left;
	
}

.graphic-content-title p {
	display : inline;
	text-align : left;
	margin-top : 5px;
	margin-bottom : 5px;
	padding-top : 5px;
	padding-bottom : 5px;
}

/* position the page content so that the page title overlays the bottom
 * of the background image, but make sure the content is always on top 
 * (using z-index) */
#content-page-content {
	float : none;
	clear : both;
	text-align : left;
	margin-top : 35px;
}

.content-page > #page-content { margin-top : 100px; }

#content-page-content p { 
	padding-bottom : 15px; 
	text-align : left; 
	float : none;
	clear : both;
}

.content-page-title, .page-description {
	text-align : left;
	margin-right : 10px;
	float : none;
	clear : both;
}

/*** Overview: Content Pages ***/

#content-area {
	width : 100%;
	height : 429px;
	/* background-image : url(../new_graphics/templates/pagegrid.gif); */
	background-repeat : no-repeat;
	background-position : center;
	text-align : left; 
	margin-left : 10px;
	margin-right : 10px;
	margin-top : 10px;
	margin-bottom : -20px;	
	position : relative;
	top : 0px;
	z-index : -100;
}

#radejbj2ee-image-area {
	float : left;
	width : 50%;
	height : 429px;
	background-image : url(graphics/contentpage/ejbj2ee.gif);
	background-repeat : no-repeat;
	background-position : center;
	position : left;
	top : 0px;
	z-index : -100;
}

#radejbj2ee-text-area {
	float : left;
	width : 50%;
	height : 429px;
	text-align : left; 
	position : relative;
	top : 0px;
	z-index : -100;
	margin-right : -1px;
}

/*** What's New: Content Pages ***/

#radwnportal-image-area {
	/*float : left;*/
	width : 100%;
	height : 50%;
	background-image : url(graphics/contentpage/portal.jpg);
	background-repeat : no-repeat;
	background-position : center;
	position : left;
	top : 0px;
	z-index : -100;
}

#radwnportal-text-area {
	/* float : left; */
	width : 100%;
	height : 50%;
	text-align : left; 
	position : relative;
	top : 0px;
	z-index : -100;
	margin-right : -1px;
}

#radwnuml-image-area {
	float : left;
	width : 50%;
	height : 429px;
	/* Change  this to the picture that will be shown.   */
	background-image : url(graphics/contentpage/uml3.gif);
	background-repeat : no-repeat;
	background-position : center;
	position : left;
	top : 0px;
	z-index : -100;
}

#wncrystal1-text-area {
	float : left;
	width : 50%;
	height : 429px;
	text-align : left; 
	position : relative;
	top : 0px;
	z-index : -100;
	margin-right : -1px;
}

#wncrystaldeveloper-image-area {
	float : left;
	width : 50%;
	height : 429px;
	/* Change  this to the picture that will be shown.   */
	background-image : url(graphics/contentpage/CrystalReports_devtools.jpg);
	background-repeat : no-repeat;
	background-position : center;
	position : left;
	top : 0px;
	z-index : -100;
}

#rwdwncrystal2-text-area {
	float : left;
	width : 50%;
	height : 429px;
	text-align : left; 
	position : relative;
	top : 0px;
	z-index : -100;
	margin-right : -1px;
}

#wncrystaldesigner-image-area {
	float : left;
	width : 50%;
	height : 429px;
	/* Change  this to the picture that will be shown.   */
	background-image : url(graphics/contentpage/CrystalReports_designer.jpg);
	background-repeat : no-repeat;
	background-position : center;
	position : left;
	top : 0px;
	z-index : -100;
}

#wncrystal3-text-area {
	float : left;
	width : 50%;
	height : 429px;
	text-align : left; 
	position : relative;
	top : 0px;
	z-index : -100;
	margin-right : -1px;
}

#wncrystalenterprise-image-area {
	float : left;
	width : 50%;
	height : 429px;
	/* Change  this to the picture that will be shown.   */
	background-image : url(graphics/contentpage/CrystalEnterprise.jpg);
	background-repeat : no-repeat;
	background-position : center;
	position : left;
	top : 0px;
	z-index : -100;
}

#radwnuml-text-area {
	float : left;
	width : 50%;
	height : 429px;
	text-align : left; 
	position : relative;
	top : 0px;
	z-index : -100;
	margin-right : -1px;
}

#radwnrup-image-area {
	float : left;
	width : 55%;
	height : 429px;
	/* Change  this to the picture that will be shown.   */
	background-image : url(graphics/contentpage/rup2.jpg);
	background-repeat : no-repeat;
	background-position : center;
	position : left;
	top : 0px;
	z-index : -100;
}

#radwnrup-text-area {
	float : left;
	width : 45%;
	height : 429px;
	text-align : left; 
	position : relative;
	top : 0px;
	z-index : -100;
	margin-right : -1px;
}

#wncodereview-image-area {
	float : left;
	width : 55%;
	height : 429px;
	/* Change  this to the picture that will be shown.   */
	background-image : url(graphics/contentpage/codereview.jpg);
	background-repeat : no-repeat;
	background-position : center;
	position : left;
	top : 0px;
	z-index : -100;
}

#wncodereview-text-area {
	float : left;
	width : 45%;
	height : 429px;
	text-align : left; 
	position : relative;
	top : 0px;
	z-index : -100;
	margin-right : -1px;
}

#wncomponenttesting-image-area {
	float : left;
	width : 55%;
	height : 429px;
	/* Change  this to the picture that will be shown.   */
	background-image : url(graphics/contentpage/comptest.jpg);
	background-repeat : no-repeat;
	background-position : center;
	position : left;
	top : 0px;
	z-index : -100;
}

#wncomponenttesting-text-area {
	float : left;
	width : 45%;
	height : 429px;
	text-align : left; 
	position : relative;
	top : 0px;
	z-index : -100;
	margin-right : -1px;
}


/*** First Steps: Content Pages ***/

#rad1stmig-image-area {
	/*float : left;*/
	width : 100%;
	height : 50%;
	background-image : url(graphics/contentpage/reservation.gif);
	background-repeat : no-repeat;
	background-position : center;
	position : left;
	top : 0px;
	z-index : -100;
}

/*#rad1stmig-text-area {*/
	/* float : left; */
/*	width : 100%;
	height : 50%;
	text-align : left; 
	position : relative;
	top : 0px;
	z-index : -100;
	margin-right : -1px;
}*/
