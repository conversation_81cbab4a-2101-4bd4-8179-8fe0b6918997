# about.ini
# contains information about a feature
# java.io.Properties file (ISO 8859-1 with "\" escapes)
# "%key" are externalized strings defined in about.properties
# This file does not need to be translated.

# Property "aboutText" contains blurb for "About" dialog (translated)
aboutText=%blurb

# Property "windowImage" contains path to window icon (16x16)
# needed for primary features only

# Property "featureImage" contains path to feature image (32x32)
featureImage=aspectj32.png

# Property "aboutImage" contains path to product image (500x330 or 115x164)
# needed for primary features only

# Property "appName" contains name of the application (translated)
# needed for primary features only

# Property "welcomePage" contains path to welcome page (special XML-based format)
# Bug 360093: AJDT is not a primary feature and so should not contribute a welcome page
# welcomePage=$nl$/welcome.xml

# Property "welcomePerspective" contains the id of the perspective in which the
# welcome page is to be opened.
# optional
