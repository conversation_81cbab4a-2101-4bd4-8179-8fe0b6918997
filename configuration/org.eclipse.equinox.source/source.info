#encoding=UTF-8
#version=1
io.projectreactor.reactor-core.source,3.1.5.201804101345-RELEASE,plugins/io.projectreactor.reactor-core.source_3.1.5.201804101345-RELEASE.jar,-1,false
org.reactivestreams.reactive-streams.source,1.0.2,plugins/org.reactivestreams.reactive-streams.source_1.0.2.jar,-1,false
org.springframework.aop.source,4.3.15.20180306-CI,plugins/org.springframework.aop.source_4.3.15.20180306-CI.jar,-1,false
org.springframework.beans.source,4.3.15.20180306-CI,plugins/org.springframework.beans.source_4.3.15.20180306-CI.jar,-1,false
org.springframework.context.source,4.3.15.20180306-CI,plugins/org.springframework.context.source_4.3.15.20180306-CI.jar,-1,false
org.springframework.context.support.source,4.3.15.20180306-<PERSON><PERSON>,plugins/org.springframework.context.support.source_4.3.15.20180306-CI.jar,-1,false
org.springframework.core.source,4.3.15.20180306-CI,plugins/org.springframework.core.source_4.3.15.20180306-CI.jar,-1,false
org.springframework.data.core.source,1.11.4.20160223-RELEASE,plugins/org.springframework.data.core.source_1.11.4.20160223-RELEASE.jar,-1,false
org.springframework.expression.source,4.3.15.20180306-CI,plugins/org.springframework.expression.source_4.3.15.20180306-CI.jar,-1,false
org.springframework.ide.eclipse.aeri.source,3.9.4.201804120850-RELEASE,plugins/org.springframework.ide.eclipse.aeri.source_3.9.4.201804120850-RELEASE.jar,-1,false
org.springframework.ide.eclipse.ajdt.ui.visualiser.source,3.9.4.201804120850-RELEASE,plugins/org.springframework.ide.eclipse.ajdt.ui.visualiser.source_3.9.4.201804120850-RELEASE.jar,-1,false
org.springframework.ide.eclipse.ajdt.ui.xref.source,3.9.4.201804120850-RELEASE,plugins/org.springframework.ide.eclipse.ajdt.ui.xref.source_3.9.4.201804120850-RELEASE.jar,-1,false
org.springframework.ide.eclipse.aop.core.source,3.9.4.201804120850-RELEASE,plugins/org.springframework.ide.eclipse.aop.core.source_3.9.4.201804120850-RELEASE.jar,-1,false
org.springframework.ide.eclipse.aop.mylyn.source,3.9.4.201804120850-RELEASE,plugins/org.springframework.ide.eclipse.aop.mylyn.source_3.9.4.201804120850-RELEASE.jar,-1,false
org.springframework.ide.eclipse.aop.ui.matcher.source,3.9.4.201804120850-RELEASE,plugins/org.springframework.ide.eclipse.aop.ui.matcher.source_3.9.4.201804120850-RELEASE.jar,-1,false
org.springframework.ide.eclipse.aop.ui.source,3.9.4.201804120850-RELEASE,plugins/org.springframework.ide.eclipse.aop.ui.source_3.9.4.201804120850-RELEASE.jar,-1,false
org.springframework.ide.eclipse.batch.source,3.9.4.201804120850-RELEASE,plugins/org.springframework.ide.eclipse.batch.source_3.9.4.201804120850-RELEASE.jar,-1,false
org.springframework.ide.eclipse.beans.core.autowire.source,3.9.4.201804120850-RELEASE,plugins/org.springframework.ide.eclipse.beans.core.autowire.source_3.9.4.201804120850-RELEASE.jar,-1,false
org.springframework.ide.eclipse.beans.core.metadata.source,3.9.4.201804120850-RELEASE,plugins/org.springframework.ide.eclipse.beans.core.metadata.source_3.9.4.201804120850-RELEASE.jar,-1,false
org.springframework.ide.eclipse.beans.core.source,3.9.4.201804120850-RELEASE,plugins/org.springframework.ide.eclipse.beans.core.source_3.9.4.201804120850-RELEASE.jar,-1,false
org.springframework.ide.eclipse.beans.mylyn.source,3.9.4.201804120850-RELEASE,plugins/org.springframework.ide.eclipse.beans.mylyn.source_3.9.4.201804120850-RELEASE.jar,-1,false
org.springframework.ide.eclipse.beans.ui.autowire.source,3.9.4.201804120850-RELEASE,plugins/org.springframework.ide.eclipse.beans.ui.autowire.source_3.9.4.201804120850-RELEASE.jar,-1,false
org.springframework.ide.eclipse.beans.ui.editor.source,3.9.4.201804120850-RELEASE,plugins/org.springframework.ide.eclipse.beans.ui.editor.source_3.9.4.201804120850-RELEASE.jar,-1,false
org.springframework.ide.eclipse.beans.ui.graph.source,3.9.4.201804120850-RELEASE,plugins/org.springframework.ide.eclipse.beans.ui.graph.source_3.9.4.201804120850-RELEASE.jar,-1,false
org.springframework.ide.eclipse.beans.ui.live.source,3.9.4.201804120850-RELEASE,plugins/org.springframework.ide.eclipse.beans.ui.live.source_3.9.4.201804120850-RELEASE.jar,-1,false
org.springframework.ide.eclipse.beans.ui.livegraph.source,3.9.4.201804120850-RELEASE,plugins/org.springframework.ide.eclipse.beans.ui.livegraph.source_3.9.4.201804120850-RELEASE.jar,-1,false
org.springframework.ide.eclipse.beans.ui.refactoring.source,3.9.4.201804120850-RELEASE,plugins/org.springframework.ide.eclipse.beans.ui.refactoring.source_3.9.4.201804120850-RELEASE.jar,-1,false
org.springframework.ide.eclipse.beans.ui.search.source,3.9.4.201804120850-RELEASE,plugins/org.springframework.ide.eclipse.beans.ui.search.source_3.9.4.201804120850-RELEASE.jar,-1,false
org.springframework.ide.eclipse.beans.ui.source,3.9.4.201804120850-RELEASE,plugins/org.springframework.ide.eclipse.beans.ui.source_3.9.4.201804120850-RELEASE.jar,-1,false
org.springframework.ide.eclipse.bestpractices.source,3.9.4.201804120850-RELEASE,plugins/org.springframework.ide.eclipse.bestpractices.source_3.9.4.201804120850-RELEASE.jar,-1,false
org.springframework.ide.eclipse.boot.dash.source,3.9.4.201804120850-RELEASE,plugins/org.springframework.ide.eclipse.boot.dash.source_3.9.4.201804120850-RELEASE.jar,-1,false
org.springframework.ide.eclipse.boot.launch.source,3.9.4.201804120850-RELEASE,plugins/org.springframework.ide.eclipse.boot.launch.source_3.9.4.201804120850-RELEASE.jar,-1,false
org.springframework.ide.eclipse.boot.properties.editor.source,3.9.4.201804120850-RELEASE,plugins/org.springframework.ide.eclipse.boot.properties.editor.source_3.9.4.201804120850-RELEASE.jar,-1,false
org.springframework.ide.eclipse.boot.properties.editor.yaml.source,3.9.4.201804120850-RELEASE,plugins/org.springframework.ide.eclipse.boot.properties.editor.yaml.source_3.9.4.201804120850-RELEASE.jar,-1,false
org.springframework.ide.eclipse.boot.restart.source,3.9.4.201804120850-RELEASE,plugins/org.springframework.ide.eclipse.boot.restart.source_3.9.4.201804120850-RELEASE.jar,-1,false
org.springframework.ide.eclipse.boot.source,3.9.4.201804120850-RELEASE,plugins/org.springframework.ide.eclipse.boot.source_3.9.4.201804120850-RELEASE.jar,-1,false
org.springframework.ide.eclipse.boot.templates.source,3.9.4.201804120850-RELEASE,plugins/org.springframework.ide.eclipse.boot.templates.source_3.9.4.201804120850-RELEASE.jar,-1,false
org.springframework.ide.eclipse.boot.wizard.source,3.9.4.201804120850-RELEASE,plugins/org.springframework.ide.eclipse.boot.wizard.source_3.9.4.201804120850-RELEASE.jar,-1,false
org.springframework.ide.eclipse.buildship.source,3.9.4.201804120850-RELEASE,plugins/org.springframework.ide.eclipse.buildship.source_3.9.4.201804120850-RELEASE.jar,-1,false
org.springframework.ide.eclipse.buildship20.source,3.9.4.201804120850-RELEASE,plugins/org.springframework.ide.eclipse.buildship20.source_3.9.4.201804120850-RELEASE.jar,-1,false
org.springframework.ide.eclipse.cft.source,3.9.4.201804120850-RELEASE,plugins/org.springframework.ide.eclipse.cft.source_3.9.4.201804120850-RELEASE.jar,-1,false
org.springframework.ide.eclipse.cloudfoundry.manifest.editor.source,3.9.4.201804120850-RELEASE,plugins/org.springframework.ide.eclipse.cloudfoundry.manifest.editor.source_3.9.4.201804120850-RELEASE.jar,-1,false
org.springframework.ide.eclipse.config.core.source,3.9.4.201804120850-RELEASE,plugins/org.springframework.ide.eclipse.config.core.source_3.9.4.201804120850-RELEASE.jar,-1,false
org.springframework.ide.eclipse.config.graph.source,3.9.4.201804120850-RELEASE,plugins/org.springframework.ide.eclipse.config.graph.source_3.9.4.201804120850-RELEASE.jar,-1,false
org.springframework.ide.eclipse.config.ui.source,3.9.4.201804120850-RELEASE,plugins/org.springframework.ide.eclipse.config.ui.source_3.9.4.201804120850-RELEASE.jar,-1,false
org.springframework.ide.eclipse.core.source,3.9.4.201804120850-RELEASE,plugins/org.springframework.ide.eclipse.core.source_3.9.4.201804120850-RELEASE.jar,-1,false
org.springframework.ide.eclipse.data.core.source,3.9.4.201804120850-RELEASE,plugins/org.springframework.ide.eclipse.data.core.source_3.9.4.201804120850-RELEASE.jar,-1,false
org.springframework.ide.eclipse.editor.support.source,3.9.4.201804120850-RELEASE,plugins/org.springframework.ide.eclipse.editor.support.source_3.9.4.201804120850-RELEASE.jar,-1,false
org.springframework.ide.eclipse.imports.source,3.9.4.201804120850-RELEASE,plugins/org.springframework.ide.eclipse.imports.source_3.9.4.201804120850-RELEASE.jar,-1,false
org.springframework.ide.eclipse.integration.source,3.9.4.201804120850-RELEASE,plugins/org.springframework.ide.eclipse.integration.source_3.9.4.201804120850-RELEASE.jar,-1,false
org.springframework.ide.eclipse.maven.source,3.9.4.201804120850-RELEASE,plugins/org.springframework.ide.eclipse.maven.source_3.9.4.201804120850-RELEASE.jar,-1,false
org.springframework.ide.eclipse.metadata.source,3.9.4.201804120850-RELEASE,plugins/org.springframework.ide.eclipse.metadata.source_3.9.4.201804120850-RELEASE.jar,-1,false
org.springframework.ide.eclipse.mylyn.source,3.9.4.201804120850-RELEASE,plugins/org.springframework.ide.eclipse.mylyn.source_3.9.4.201804120850-RELEASE.jar,-1,false
org.springframework.ide.eclipse.osgi.blueprint.source,3.9.4.201804120850-RELEASE,plugins/org.springframework.ide.eclipse.osgi.blueprint.source_3.9.4.201804120850-RELEASE.jar,-1,false
org.springframework.ide.eclipse.osgi.runtime.source,3.9.4.201804120850-RELEASE,plugins/org.springframework.ide.eclipse.osgi.runtime.source_3.9.4.201804120850-RELEASE.jar,-1,false
org.springframework.ide.eclipse.osgi.source,3.9.4.201804120850-RELEASE,plugins/org.springframework.ide.eclipse.osgi.source_3.9.4.201804120850-RELEASE.jar,-1,false
org.springframework.ide.eclipse.osgi.targetdefinition.source,3.9.4.201804120850-RELEASE,plugins/org.springframework.ide.eclipse.osgi.targetdefinition.source_3.9.4.201804120850-RELEASE.jar,-1,false
org.springframework.ide.eclipse.quickfix.source,3.9.4.201804120850-RELEASE,plugins/org.springframework.ide.eclipse.quickfix.source_3.9.4.201804120850-RELEASE.jar,-1,false
org.springframework.ide.eclipse.roo.core.source,3.9.4.201804120850-RELEASE,plugins/org.springframework.ide.eclipse.roo.core.source_3.9.4.201804120850-RELEASE.jar,-1,false
org.springframework.ide.eclipse.roo.ui.source,3.9.4.201804120850-RELEASE,plugins/org.springframework.ide.eclipse.roo.ui.source_3.9.4.201804120850-RELEASE.jar,-1,false
org.springframework.ide.eclipse.security.source,3.9.4.201804120850-RELEASE,plugins/org.springframework.ide.eclipse.security.source_3.9.4.201804120850-RELEASE.jar,-1,false
org.springframework.ide.eclipse.source,3.9.4.201804120850-RELEASE,plugins/org.springframework.ide.eclipse.source_3.9.4.201804120850-RELEASE.jar,-1,false
org.springframework.ide.eclipse.ui.source,3.9.4.201804120850-RELEASE,plugins/org.springframework.ide.eclipse.ui.source_3.9.4.201804120850-RELEASE.jar,-1,false
org.springframework.ide.eclipse.webflow.core.source,3.9.4.201804120850-RELEASE,plugins/org.springframework.ide.eclipse.webflow.core.source_3.9.4.201804120850-RELEASE.jar,-1,false
org.springframework.ide.eclipse.webflow.mylyn.source,3.9.4.201804120850-RELEASE,plugins/org.springframework.ide.eclipse.webflow.mylyn.source_3.9.4.201804120850-RELEASE.jar,-1,false
org.springframework.ide.eclipse.webflow.ui.editor.source,3.9.4.201804120850-RELEASE,plugins/org.springframework.ide.eclipse.webflow.ui.editor.source_3.9.4.201804120850-RELEASE.jar,-1,false
org.springframework.ide.eclipse.webflow.ui.graph.source,3.9.4.201804120850-RELEASE,plugins/org.springframework.ide.eclipse.webflow.ui.graph.source_3.9.4.201804120850-RELEASE.jar,-1,false
org.springframework.ide.eclipse.webflow.ui.source,3.9.4.201804120850-RELEASE,plugins/org.springframework.ide.eclipse.webflow.ui.source_3.9.4.201804120850-RELEASE.jar,-1,false
org.springframework.ide.eclipse.wizard.source,3.9.4.201804120850-RELEASE,plugins/org.springframework.ide.eclipse.wizard.source_3.9.4.201804120850-RELEASE.jar,-1,false
org.springframework.jdbc.source,4.3.15.20180306-CI,plugins/org.springframework.jdbc.source_4.3.15.20180306-CI.jar,-1,false
org.springframework.jms.source,4.3.15.20180306-CI,plugins/org.springframework.jms.source_4.3.15.20180306-CI.jar,-1,false
org.springframework.orm.source,4.3.15.20180306-CI,plugins/org.springframework.orm.source_4.3.15.20180306-CI.jar,-1,false
org.springframework.oxm.source,4.3.15.20180306-CI,plugins/org.springframework.oxm.source_4.3.15.20180306-CI.jar,-1,false
org.springframework.transaction.source,4.3.15.20180306-CI,plugins/org.springframework.transaction.source_4.3.15.20180306-CI.jar,-1,false
org.springframework.web.servlet.source,4.3.15.20180306-CI,plugins/org.springframework.web.servlet.source_4.3.15.20180306-CI.jar,-1,false
org.springframework.web.source,4.3.15.20180306-CI,plugins/org.springframework.web.source_4.3.15.20180306-CI.jar,-1,false
org.springsource.ide.eclipse.commons.cloudfoundry.client.v2.source,3.9.4.201804120850-RELEASE,plugins/org.springsource.ide.eclipse.commons.cloudfoundry.client.v2.source_3.9.4.201804120850-RELEASE.jar,-1,false
